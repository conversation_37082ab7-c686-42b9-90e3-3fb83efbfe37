{"version": 3, "routes": {"/about": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/BgwVIqsDD-B4MkXGbbUG1/about.json"}, "/blog": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/BgwVIqsDD-B4MkXGbbUG1/blog.json"}, "/privacy": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/BgwVIqsDD-B4MkXGbbUG1/privacy.json"}, "/deliver": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/BgwVIqsDD-B4MkXGbbUG1/deliver.json"}, "/referral-terms": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/BgwVIqsDD-B4MkXGbbUG1/referral-terms.json"}, "/terms": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/BgwVIqsDD-B4MkXGbbUG1/terms.json"}, "/welcome": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/BgwVIqsDD-B4MkXGbbUG1/welcome.json"}}, "dynamicRoutes": {"/blog/[id]": {"routeRegex": "^/blog/([^/]+?)(?:/)?$", "dataRoute": "/_next/data/BgwVIqsDD-B4MkXGbbUG1/blog/[id].json", "fallback": null, "dataRouteRegex": "^/_next/data/BgwVIqsDD\\-B4MkXGbbUG1/blog/([^/]+?)\\.json$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "938dc0d9eb8fbc9729fb75da60df8e3e", "previewModeSigningKey": "c5a60afccdfee0a135a7e462f5688c7b627469efd4f54a32ab945ce18973f8bd", "previewModeEncryptionKey": "dcc8fc6c6d8194dc626cd7e73aef09737c9a90fade17e4499a0ea91fc4964998"}}