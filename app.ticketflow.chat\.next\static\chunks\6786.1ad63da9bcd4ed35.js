(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6786,7107],{91435:function(e,r,a){"use strict";a.r(r),a.d(r,{default:function(){return eo}});var t=a(85893),o=a(67294),n=a(47107),s=a(33336),l=a.n(s),c=a(6734),i=a(6684),d=a(80892),u=a(94660),p=a(11163),f=a(80108),h=a(26137),m=a.n(h),v=a(72278),x=a.n(v),j=a(29969),w=a(41664),g=a.n(w),b=a(29500),_=a.n(b),N=a(13597),y=a.n(N),O=a(11295);function z(e){var r;let{data:a,handleClose:o}=e,{t:n}=(0,c.$G)(),{push:s}=(0,p.useRouter)(),{logout:l}=(0,j.a)(),i=()=>{l(),o(),s("/login")};return(0,t.jsxs)("div",{className:_().wrapper,children:[(0,t.jsxs)(g(),{href:"/profile",className:_().profile,children:[(0,t.jsxs)("div",{className:_().naming,children:[(0,t.jsxs)("label",{children:[a.firstname," ",null===(r=a.lastname)||void 0===r?void 0:r.charAt(0),"."]}),(0,t.jsx)("span",{className:_().link,children:n("view.profile")})]}),(0,t.jsx)("div",{className:_().profileImage,children:(0,t.jsx)(O.Z,{data:a})})]}),(0,t.jsx)("button",{className:_().logoutBtn,onClick:i,children:(0,t.jsx)(y(),{})})]})}var C=a(97169),k=a.n(C),Z=a(72275),E=a.n(Z),P=a(83439),B=a.n(P),A=a(6591),I=a.n(A),V=a(5458),D=a.n(V),M=a(87456),H=a.n(M),L=a(94577),W=a.n(L),R=a(40341),Q=a.n(R),G=a(37490),S=a(21014),F=a(45049),X=a(1729),$=a(88767),q=a(94098),J=a(80129),K=a.n(J),T=a(16346),Y=a(90026),U=a(93506),ee=a.n(U);function er(e){var r,a,o,n;let{handleClose:s}=e,{t:i}=(0,c.$G)(),{user:d,isAuthenticated:u}=(0,j.a)(),[p,f,h]=(0,G.Z)(),[m,v,x]=(0,G.Z)(),{data:w}=(0,$.useQuery)("activeOrders",()=>q.Z.getAll(K().stringify({order_statuses:!0,statuses:T.j})),{retry:!1,enabled:u});return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:l().body,children:[u&&(0,t.jsx)(z,{data:d,handleClose:s}),u&&(0,t.jsxs)(g(),{href:"/wallet",className:l().row,onClick:s,children:[(0,t.jsxs)("div",{className:l().rowItem,children:[(0,t.jsx)(D(),{}),(0,t.jsxs)("span",{className:l().text,children:[i("wallet"),":"]}),(0,t.jsx)("span",{className:l().bold,children:(0,t.jsx)(Y.Z,{number:null===(r=d.wallet)||void 0===r?void 0:r.price,symbol:null===(a=d.wallet)||void 0===a?void 0:a.symbol})})]}),(0,t.jsx)(k(),{})]}),u&&(0,t.jsxs)(g(),{href:"/orders",className:l().row,onClick:s,children:[(0,t.jsxs)("div",{className:l().rowItem,children:[(0,t.jsx)(B(),{}),(0,t.jsx)("span",{className:l().text,children:i("orders")}),(null==w?void 0:null===(o=w.meta)||void 0===o?void 0:o.total)>0&&(0,t.jsx)("div",{className:l().badge,children:null==w?void 0:null===(n=w.meta)||void 0===n?void 0:n.total})]}),(0,t.jsx)(k(),{})]}),(0,t.jsxs)(g(),{href:"/be-seller",className:l().row,onClick:s,children:[(0,t.jsxs)("div",{className:l().rowItem,children:[(0,t.jsx)(Q(),{}),(0,t.jsx)("span",{className:l().text,children:i("be.seller")})]}),(0,t.jsx)(k(),{})]}),u&&(0,t.jsxs)(g(),{href:"/parcels",className:l().row,onClick:s,children:[(0,t.jsxs)("div",{className:l().rowItem,children:[(0,t.jsx)(I(),{}),(0,t.jsx)("span",{className:l().text,children:i("parcels")})]}),(0,t.jsx)(k(),{})]}),(0,t.jsxs)(g(),{href:"/liked",className:l().row,onClick:s,children:[(0,t.jsxs)("div",{className:l().rowItem,children:[(0,t.jsx)(E(),{}),(0,t.jsx)("span",{className:l().text,children:i("liked")})]}),(0,t.jsx)(k(),{})]}),u&&(0,t.jsxs)(g(),{href:"/settings/notification",className:l().row,onClick:s,children:[(0,t.jsxs)("div",{className:l().rowItem,children:[(0,t.jsx)(W(),{}),(0,t.jsx)("span",{className:l().text,children:i("settings")})]}),(0,t.jsx)(k(),{})]}),u&&(0,t.jsxs)(g(),{href:"/saved-locations",className:l().row,onClick:s,children:[(0,t.jsxs)("div",{className:l().rowItem,children:[(0,t.jsx)(ee(),{}),(0,t.jsx)("span",{className:l().text,children:i("delivery.addresses")})]}),(0,t.jsx)(k(),{})]}),u&&(0,t.jsxs)(g(),{href:"/help",className:l().row,onClick:s,children:[(0,t.jsxs)("div",{className:l().rowItem,children:[(0,t.jsx)(H(),{}),(0,t.jsx)("span",{className:l().text,children:i("help")})]}),(0,t.jsx)(k(),{})]})]}),(0,t.jsx)(S.default,{open:p,onClose:h,children:(0,t.jsx)(F.Z,{onClose:h})}),(0,t.jsx)(S.default,{open:m,onClose:x,children:(0,t.jsx)(X.Z,{onClose:x})})]})}var ea=a(98396),et=a(21697);function eo(e){let{open:r,handleClose:a}=e,{t:s}=(0,c.$G)(),{push:h}=(0,p.useRouter)(),v=(0,ea.Z)("(max-width:1139px)"),{isDarkMode:w,toggleDarkMode:g,direction:b}=(0,o.useContext)(f.N),{isAuthenticated:_}=(0,j.a)(),{settings:N}=(0,et.r)();return(0,t.jsxs)(n.default,{anchor:"rtl"===b?"right":"left",open:r,onClose:a,children:[(0,t.jsx)("button",{className:l().iconBtn,onClick:g,children:w?(0,t.jsx)(m(),{}):(0,t.jsx)(x(),{})}),(0,t.jsxs)("div",{className:l().wrapper,children:[v&&(0,t.jsx)(er,{handleClose:a}),_?"":(0,t.jsxs)("div",{className:l().actions,children:[(0,t.jsx)(u.Z,{onClick(){h("/register"),a()},children:s("sign.up")}),(0,t.jsx)(d.Z,{onClick(){h("/login"),a()},children:s("login")})]}),(0,t.jsxs)("div",{className:l().footer,children:[(0,t.jsxs)("div",{className:l().flex,children:[(0,t.jsx)(i.NV,{}),(0,t.jsx)("p",{className:l().text,children:s("app.text")})]}),(0,t.jsxs)("div",{className:l().flex,children:[(0,t.jsx)("a",{href:null==N?void 0:N.customer_app_ios,className:l().item,target:"_blank",rel:"noopener noreferrer",children:(0,t.jsx)("span",{className:l().imgWrapper,children:(0,t.jsx)("img",{src:"/images/app-store.webp",alt:"App store"})})}),(0,t.jsx)("a",{href:null==N?void 0:N.customer_app_android,className:l().item,target:"_blank",rel:"noopener noreferrer",children:(0,t.jsx)("span",{className:l().imgWrapper,children:(0,t.jsx)("img",{src:"/images/google-play.webp",alt:"Google play"})})})]})]})]})]})}},16346:function(e,r,a){"use strict";a.d(r,{a:function(){return o},j:function(){return t}});let t=["new","accepted","cooking","ready","on_a_way"],o=["delivered","canceled"]},47107:function(e,r,a){"use strict";a.r(r),a.d(r,{default:function(){return u}});var t=a(85893);a(67294);var o=a(77533),n=a(90948),s=a(83222),l=a.n(s),c=a(48654),i=a.n(c);let d=(0,n.ZP)(o.ZP)(()=>({"& .MuiBackdrop-root":{backgroundColor:"rgba(0, 0, 0, 0.15)"},"& .MuiPaper-root":{backgroundColor:"var(--secondary-bg)",boxShadow:"var(--popover-box-shadow)",maxWidth:"450px",padding:"40px","@media (max-width: 576px)":{minWidth:"100vw",maxWidth:"100vw",padding:"15px"}}}));function u(e){let{anchor:r="right",open:a,onClose:o,children:n,title:s,sx:c,PaperProps:u}=e;return(0,t.jsxs)(d,{anchor:r,open:a,onClose:o,sx:c,PaperProps:u,children:[s?(0,t.jsx)("h1",{className:l().title,children:s}):"",(0,t.jsx)("button",{type:"button",className:l().closeBtn,onClick(){o&&o({},"backdropClick")},children:(0,t.jsx)(i(),{})}),n]})}},94098:function(e,r,a){"use strict";var t=a(25728);r.Z={calculate:(e,r)=>t.Z.post("/dashboard/user/cart/calculate/".concat(e),r),checkCoupon:e=>t.Z.post("/rest/coupons/check",e),create:e=>t.Z.post("/dashboard/user/orders",e),getAll:e=>t.Z.get("/dashboard/user/orders/paginate?".concat(e)),getById:(e,r,a)=>t.Z.get("/dashboard/user/orders/".concat(e),{params:r,headers:a}),cancel:e=>t.Z.post("/dashboard/user/orders/".concat(e,"/status/change?status=canceled")),review:(e,r)=>t.Z.post("/dashboard/user/orders/review/".concat(e),r),autoRepeat:(e,r)=>t.Z.post("/dashboard/user/orders/".concat(e,"/repeat"),r),deleteAutoRepeat:e=>t.Z.delete("/dashboard/user/orders/".concat(e,"/delete-repeat"))}},33336:function(e){e.exports={wrapper:"appDrawer_wrapper___1El3",actions:"appDrawer_actions__jyq1_",body:"appDrawer_body__LPlrC",row:"appDrawer_row__X1Fh_",rowItem:"appDrawer_rowItem__1QEiZ",text:"appDrawer_text__NXg06",bold:"appDrawer_bold__d9w5Z",badge:"appDrawer_badge__EVHwN",footer:"appDrawer_footer__7mtZ2",flex:"appDrawer_flex__V3PdX",item:"appDrawer_item__Qrjni",imgWrapper:"appDrawer_imgWrapper__FdDRs",iconBtn:"appDrawer_iconBtn__EEkIQ"}},29500:function(e){e.exports={wrapper:"profileCard_wrapper__1K0Rd",profile:"profileCard_profile__2Iz7f",naming:"profileCard_naming__sQ6sV",link:"profileCard_link__y_J2Z",profileImage:"profileCard_profileImage___T91r",logoutBtn:"profileCard_logoutBtn__Ymai0"}},6591:function(e,r,a){"use strict";var t=a(67294),o=t&&"object"==typeof t&&"default"in t?t:{default:t},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},s=function(e,r){var a={};for(var t in e)!(r.indexOf(t)>=0)&&Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a},l=function(e){var r=e.color,a=e.size,t=void 0===a?24:a,l=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",n({},l,{className:c,width:t,height:t,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M3 10H2V4.003C2 3.449 2.455 3 2.992 3h18.016A.99.99 0 0 1 22 4.003V10h-1v10.001a.996.996 0 0 1-.993.999H3.993A.996.996 0 0 1 3 20.001V10zm16 0H5v9h14v-9zM4 5v3h16V5H4zm5 7h6v2H9v-2z"}))},c=o.default.memo?o.default.memo(l):l;e.exports=c},72275:function(e,r,a){"use strict";var t=a(67294),o=t&&"object"==typeof t&&"default"in t?t:{default:t},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},s=function(e,r){var a={};for(var t in e)!(r.indexOf(t)>=0)&&Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a},l=function(e){var r=e.color,a=e.size,t=void 0===a?24:a,l=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",n({},l,{className:c,width:t,height:t,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12.001 4.529c2.349-2.109 5.979-2.039 8.242.228 2.262 2.268 2.34 5.88.236 8.236l-8.48 8.492-8.478-8.492c-2.104-2.356-2.025-5.974.236-8.236 2.265-2.264 5.888-2.34 8.244-.228zm6.826 1.641c-1.5-1.502-3.92-1.563-5.49-.153l-1.335 1.198-1.336-1.197c-1.575-1.412-3.99-1.35-5.494.154-1.49 1.49-1.565 3.875-.192 5.451L12 18.654l7.02-7.03c1.374-1.577 1.299-3.959-.193-5.454z"}))},c=o.default.memo?o.default.memo(l):l;e.exports=c},83439:function(e,r,a){"use strict";var t=a(67294),o=t&&"object"==typeof t&&"default"in t?t:{default:t},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},s=function(e,r){var a={};for(var t in e)!(r.indexOf(t)>=0)&&Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a},l=function(e){var r=e.color,a=e.size,t=void 0===a?24:a,l=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",n({},l,{className:c,width:t,height:t,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12h2c0 4.418 3.582 8 8 8s8-3.582 8-8-3.582-8-8-8C9.25 4 6.824 5.387 5.385 7.5H8v2H2v-6h2V6c1.824-2.43 4.729-4 8-4zm1 5v4.585l3.243 3.243-1.415 1.415L11 12.413V7h2z"}))},c=o.default.memo?o.default.memo(l):l;e.exports=c},13597:function(e,r,a){"use strict";var t=a(67294),o=t&&"object"==typeof t&&"default"in t?t:{default:t},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},s=function(e,r){var a={};for(var t in e)!(r.indexOf(t)>=0)&&Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a},l=function(e){var r=e.color,a=e.size,t=void 0===a?24:a,l=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",n({},l,{className:c,width:t,height:t,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2a9.985 9.985 0 0 1 8 4h-2.71a8 8 0 1 0 .001 12h2.71A9.985 9.985 0 0 1 12 22zm7-6v-3h-8v-2h8V8l5 4-5 4z"}))},c=o.default.memo?o.default.memo(l):l;e.exports=c},93506:function(e,r,a){"use strict";var t=a(67294),o=t&&"object"==typeof t&&"default"in t?t:{default:t},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},s=function(e,r){var a={};for(var t in e)!(r.indexOf(t)>=0)&&Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a},l=function(e){var r=e.color,a=e.size,t=void 0===a?24:a,l=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",n({},l,{className:c,width:t,height:t,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 23.728l-6.364-6.364a9 9 0 1 1 12.728 0L12 23.728zm4.95-7.778a7 7 0 1 0-9.9 0L12 20.9l4.95-4.95zM12 13a2 2 0 1 1 0-4 2 2 0 0 1 0 4z"}))},c=o.default.memo?o.default.memo(l):l;e.exports=c},87456:function(e,r,a){"use strict";var t=a(67294),o=t&&"object"==typeof t&&"default"in t?t:{default:t},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},s=function(e,r){var a={};for(var t in e)!(r.indexOf(t)>=0)&&Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a},l=function(e){var r=e.color,a=e.size,t=void 0===a?24:a,l=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",n({},l,{className:c,width:t,height:t,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-1-5h2v2h-2v-2zm2-1.645V14h-2v-1.5a1 1 0 0 1 1-1 1.5 1.5 0 1 0-1.471-1.794l-1.962-.393A3.501 3.501 0 1 1 13 13.355z"}))},c=o.default.memo?o.default.memo(l):l;e.exports=c},94577:function(e,r,a){"use strict";var t=a(67294),o=t&&"object"==typeof t&&"default"in t?t:{default:t},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},s=function(e,r){var a={};for(var t in e)!(r.indexOf(t)>=0)&&Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a},l=function(e){var r=e.color,a=e.size,t=void 0===a?24:a,l=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",n({},l,{className:c,width:t,height:t,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M3.34 17a10.018 10.018 0 0 1-.978-2.326 3 3 0 0 0 .002-5.347A9.99 9.99 0 0 1 4.865 4.99a3 3 0 0 0 4.631-2.674 9.99 9.99 0 0 1 5.007.002 3 3 0 0 0 4.632 2.672c.579.59 1.093 1.261 1.525 2.01.433.749.757 1.53.978 2.326a3 3 0 0 0-.002 5.347 9.99 9.99 0 0 1-2.501 4.337 3 3 0 0 0-4.631 2.674 9.99 9.99 0 0 1-5.007-.002 3 3 0 0 0-4.632-2.672A10.018 10.018 0 0 1 3.34 17zm5.66.196a4.993 4.993 0 0 1 2.25 2.77c.499.047 1 .048 1.499.001A4.993 4.993 0 0 1 15 17.197a4.993 4.993 0 0 1 3.525-.565c.29-.408.54-.843.748-1.298A4.993 4.993 0 0 1 18 12c0-1.26.47-2.437 1.273-3.334a8.126 8.126 0 0 0-.75-1.298A4.993 4.993 0 0 1 15 6.804a4.993 4.993 0 0 1-2.25-2.77c-.499-.047-1-.048-1.499-.001A4.993 4.993 0 0 1 9 6.803a4.993 4.993 0 0 1-3.525.565 7.99 7.99 0 0 0-.748 1.298A4.993 4.993 0 0 1 6 12c0 1.26-.47 2.437-1.273 3.334a8.126 8.126 0 0 0 .75 1.298A4.993 4.993 0 0 1 9 17.196zM12 15a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0-2a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"}))},c=o.default.memo?o.default.memo(l):l;e.exports=c},40341:function(e,r,a){"use strict";var t=a(67294),o=t&&"object"==typeof t&&"default"in t?t:{default:t},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},s=function(e,r){var a={};for(var t in e)!(r.indexOf(t)>=0)&&Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a},l=function(e){var r=e.color,a=e.size,t=void 0===a?24:a,l=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",n({},l,{className:c,width:t,height:t,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 14v2a6 6 0 0 0-6 6H4a8 8 0 0 1 8-8zm0-1c-3.315 0-6-2.685-6-6s2.685-6 6-6 6 2.685 6 6-2.685 6-6 6zm0-2c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm6 10.5l-2.939 1.545.561-3.272-2.377-2.318 3.286-.478L18 14l1.47 2.977 3.285.478-2.377 2.318.56 3.272L18 21.5z"}))},c=o.default.memo?o.default.memo(l):l;e.exports=c},5458:function(e,r,a){"use strict";var t=a(67294),o=t&&"object"==typeof t&&"default"in t?t:{default:t},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},s=function(e,r){var a={};for(var t in e)!(r.indexOf(t)>=0)&&Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a},l=function(e){var r=e.color,a=e.size,t=void 0===a?24:a,l=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",n({},l,{className:c,width:t,height:t,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M22 7h1v10h-1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v3zm-2 10h-6a5 5 0 0 1 0-10h6V5H4v14h16v-2zm1-2V9h-7a3 3 0 0 0 0 6h7zm-7-4h3v2h-3v-2z"}))},c=o.default.memo?o.default.memo(l):l;e.exports=c},24654:function(){}}]);