(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1578],{21578:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return D}});var r=a(85893),o=a(98396),d=a(47567),s=a(21014),l=a(60149),n=a.n(l),u=a(6734),i=a(82175),m=a(27484),c=a.n(m),p=a(38033),f=a(50720),Y=a(10586),h=a(77262),b=a(73714),v=a(88767),_=a(94098);function M(e){let{orderId:t,onClose:a}=e,{t:o}=(0,u.$G)(),{i18n:d}=(0,u.$G)(),s=d.language,l=(0,v.useQueryClient)(),{isLoading:m,mutate:M}=(0,v.useMutation)({mutationFn:e=>_.Z.autoRepeat(e.orderId,e.data),onSuccess(){l.invalidateQueries(["order",t,s]),(0,b.Vp)(o("auto.repeat.order.success"))},onError(e){var t;(0,b.vU)((null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.message)||o("auto.repeat.order.error"))},onSettled(){a()}}),x=(0,i.TA)({initialValues:{from:c()().add(1,"day").format("YYYY-MM-DD"),to:c()().add(2,"day").format("YYYY-MM-DD")},onSubmit(e){if(c()(null==e?void 0:e.from).isAfter(c()(null==e?void 0:e.to)))return(0,b.vU)(o("start.date.should.be.before.end.date"));M({orderId:t,data:e})}});return(0,r.jsx)("div",{className:n().wrapper,children:(0,r.jsxs)("form",{id:"autoRepeatOrder",onSubmit:x.handleSubmit,children:[(0,r.jsx)("h1",{className:n().title,children:o("select.dates.for.auto.repeat")}),(0,r.jsx)("div",{className:n().body,children:(0,r.jsxs)(f._,{dateAdapter:Y.y,children:[(0,r.jsx)(p.M,{label:o("start.date"),disablePast:!0,value:c()(x.values.from),onChange(e){x.setFieldValue("from",c()(e).format("YYYY-MM-DD"))},format:"YYYY-MM-DD",className:n().item}),(0,r.jsx)(p.M,{label:o("end.date"),disablePast:!0,value:c()(x.values.to),onChange(e){x.setFieldValue("to",c()(e).format("YYYY-MM-DD"))},format:"YYYY-MM-DD",className:n().item})]})}),(0,r.jsx)(h.Z,{type:"submit",loading:m,children:o("submit")})]})})}var x=a(11163);function D(e){let{open:t,onClose:a}=e,l=(0,o.Z)("(min-width:1140px)"),{query:n}=(0,x.useRouter)(),u=Number(n.id);return l?(0,r.jsx)(d.default,{open:t,onClose:a,children:(0,r.jsx)(M,{orderId:u,onClose:a})}):(0,r.jsx)(s.default,{open:t,onClose:a,children:(0,r.jsx)(M,{orderId:u,onClose:a})})}},60149:function(e){e.exports={wrapper:"autoRepeatOrder_wrapper__7aQUQ",title:"autoRepeatOrder_title__TrhO5",body:"autoRepeatOrder_body__kKopM",item:"autoRepeatOrder_item__KE0ND"}}}]);