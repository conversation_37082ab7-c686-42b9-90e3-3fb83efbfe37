(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5851],{45851:function(e,a,r){"use strict";r.r(a),r.d(a,{default:function(){return p}});var s=r(85893);r(67294);var t=r(80865),l=r(86718),n=r.n(l),i=r(34349),o=r(5215),_=r(6734);let c=["trust_you","best_sale","high_rating","low_sale","low_rating"];function p(e){let{handleClose:a}=e,{t:r}=(0,_.$G)(),{order_by:l}=(0,i.C)(o.qs),p=(0,i.T)(),h=e=>{p((0,o.Ec)(e.target.value)),a()},u=e=>({checked:l===e,onChange:h,value:e,id:e,name:"sorting",inputProps:{"aria-label":e}});return(0,s.jsx)("div",{className:n().wrapper,children:c.map(e=>(0,s.jsxs)("div",{className:n().row,children:[(0,s.jsx)(t.Z,{...u(e)}),(0,s.jsx)("label",{className:n().label,htmlFor:e,children:(0,s.jsx)("span",{className:n().text,children:r(e)})})]},e))})}},86718:function(e){e.exports={wrapper:"shopSorting_wrapper__vG7cs",row:"shopSorting_row__UYxWp",label:"shopSorting_label__kDRzD",text:"shopSorting_text__e7Hzi"}}}]);