"use strict";
exports.id = 3075;
exports.ids = [3075];
exports.modules = {

/***/ 3075:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "$6": () => (/* binding */ STORAGE_BUCKET),
/* harmony export */   "$h": () => (/* binding */ API_KEY),
/* harmony export */   "AC": () => (/* binding */ DYNAMIC_LINK_DOMAIN),
/* harmony export */   "AF": () => (/* binding */ APP_ID),
/* harmony export */   "BN": () => (/* binding */ BRAND_LOGO),
/* harmony export */   "Bt": () => (/* binding */ defaultUser),
/* harmony export */   "KM": () => (/* binding */ META_DESCRIPTION),
/* harmony export */   "Kc": () => (/* binding */ DYNAMIC_LINK_WEB_KEY),
/* harmony export */   "Mr": () => (/* binding */ DYNAMIC_LINK_IOS),
/* harmony export */   "NX": () => (/* binding */ PROJECT_ID),
/* harmony export */   "PX": () => (/* binding */ DEFAULT_LOCATION),
/* harmony export */   "RN": () => (/* binding */ AUTH_DOMAIN),
/* harmony export */   "T5": () => (/* binding */ META_IMAGE),
/* harmony export */   "U$": () => (/* binding */ MEASUREMENT_ID),
/* harmony export */   "cU": () => (/* binding */ META_KEYWORDS),
/* harmony export */   "dK": () => (/* binding */ DYNAMIC_LINK_ANDROID),
/* harmony export */   "ft": () => (/* binding */ VAPID_KEY),
/* harmony export */   "k$": () => (/* binding */ DEFAULT_LANGUAGE),
/* harmony export */   "k5": () => (/* binding */ META_TITLE),
/* harmony export */   "sq": () => (/* binding */ BRAND_LOGO_ROUNDED),
/* harmony export */   "uO": () => (/* binding */ MESSAGING_SENDER_ID),
/* harmony export */   "wc": () => (/* binding */ BRAND_LOGO_DARK)
/* harmony export */ });
// Firebase config
const API_KEY = "AIzaSyAmFXFl9tbsycdwbx-RmpS0TIgeZyjn-uc";
const AUTH_DOMAIN = "foodyman-4025e.firebaseapp.com";
const PROJECT_ID = "foodyman-4025e";
const STORAGE_BUCKET = "foodyman-4025e.firebasestorage.app";
const MESSAGING_SENDER_ID = "298095398948";
const APP_ID = "1:298095398948:web:a74fed6104c279cf5ac8b1";
const MEASUREMENT_ID = "G-NN1YV8NXGD";
const VAPID_KEY = "BKZzW8v_40eneZmoQsvt-ReFt6zNlQTcB9Q0mLrcd-YGXpomyKliaxJ52U3bmyGGa1jnYH7t93WSLAMcBZ8wFNc";
// Default config
const DEFAULT_LOCATION = "-23.5505,-46.6333"; // latitude,longitude
const DEFAULT_LANGUAGE = "pt-BR";
// SEO
const META_TITLE = "TicketFlow - Delivery Brasil";
const META_DESCRIPTION = "Plataforma de delivery de comida e mercado no Brasil - Peça e receba em casa";
const META_IMAGE = "https://app.ticketflow.chat/images/brand_logo.svg";
const META_KEYWORDS = "Delivery,Comida,Restaurante,Brasil,Entrega,Mercado,Pedidos";
const BRAND_LOGO = "https://app.ticketflow.chat/images/brand_logo.svg";
const BRAND_LOGO_DARK = "https://app.ticketflow.chat/images/brand_logo_dark.svg";
const BRAND_LOGO_ROUNDED = "https://app.ticketflow.chat/images/brand_logo_rounded.svg";
// Dynamic Link
const DYNAMIC_LINK_DOMAIN = "https://ticketflow.page.link";
const DYNAMIC_LINK_ANDROID = "";
const DYNAMIC_LINK_IOS = "";
const DYNAMIC_LINK_WEB_KEY = "AIzaSyAmFXFl9tbsycdwbx-RmpS0TIgeZyjn-uc";
const defaultUser = {
    login: "<EMAIL>",
    password: "githubit"
};


/***/ })

};
;