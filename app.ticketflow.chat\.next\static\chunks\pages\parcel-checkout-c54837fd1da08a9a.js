(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3419,6060],{86642:function(e,r,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/parcel-checkout",function(){return a(61815)}])},80956:function(e,r,a){"use strict";a.d(r,{Z:function(){return l}});var t=a(85893);a(67294);var n=a(90948),i=a(23048);let o=(0,n.ZP)(i.w)({"& .MuiPickersDay-root":{fontFamily:"'Inter', sans-serif","&:hover":{backgroundColor:"var(--primary-transparent)"},"&.Mui-selected":{backgroundColor:"var(--primary)",color:"var(--dark-blue)","&:hover":{backgroundColor:"var(--primary)"}},"&.MuiPickersDay-today":{border:"1px solid var(--dark-blue)"}}});function l(e){let{value:r,onChange:a,displayStaticWrapperAs:n="desktop",openTo:i="day",disablePast:l=!0}=e;return(0,t.jsx)(o,{displayStaticWrapperAs:n,openTo:i,value:r,onChange:a,disablePast:l})}},91662:function(e,r,a){"use strict";a.d(r,{Z:function(){return l}});var t=a(85893);a(67294);var n=a(90948),i=a(45843);let o=(0,n.ZP)(i.Z)({width:60,height:30,padding:0,"& .MuiSwitch-switchBase":{padding:0,margin:"5px 7px",transitionDuration:"300ms","&.Mui-checked":{transform:"translateX(26px)",color:"#fff","& + .MuiSwitch-track":{backgroundColor:"#83EA00",opacity:1,border:"0.8px solid #76D003 !important"},"&.Mui-disabled + .MuiSwitch-track":{opacity:.5}},"&.Mui-focusVisible .MuiSwitch-thumb":{color:"#33cf4d",border:"6px solid #fff"},"&.Mui-disabled .MuiSwitch-thumb":{color:"#E7E7E7"},"&.Mui-disabled + .MuiSwitch-track":{opacity:.7}},"& .MuiSwitch-thumb":{boxSizing:"border-box",position:"relative",width:20,height:20,backgroundColor:"var(--secondary-bg)",boxShadow:"0px 2px 2px rgba(66, 113, 6, 0.4)","&::after":{content:"''",position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:2,height:6,borderRadius:100,backgroundColor:"var(--grey)"}},"& .MuiSwitch-track":{borderRadius:54,backgroundColor:"var(--border)",opacity:1,transition:"background-color 0.5s",border:"0 !important"}});function l(e){return(0,t.jsx)(o,{...e,disableRipple:!0})}},24285:function(e,r,a){"use strict";a.d(r,{Z:function(){return l}});var t=a(85893);a(67294);var n=a(90948),i=a(61903);let o=(0,n.ZP)(i.Z)({width:"100%","& .MuiInputLabel-root":{fontSize:16,lineHeight:"14px",fontWeight:600,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{backgroundColor:"var(--primary-bg)",borderRadius:"10px",padding:"28px 20px",fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{display:"none"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{display:"none"},"& .MuiInput-root::after":{display:"none"}});function l(e){return(0,t.jsx)(o,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},68554:function(e,r,a){"use strict";a.d(r,{Z:function(){return m}});var t=a(85893);a(67294);var n=a(15744),i=a.n(n),o=a(58287),l=a(10076),s=a.n(l),d=a(56060),c=a(80865),u=a(18074);function m(e){var r;let{value:a,name:n,onChange:l,options:m,label:p,error:h,type:_="outlined",icon:v,placeholder:x}=e,{t:g}=(0,u.Z)(),[j,f,y,b]=(0,o.Z)(),w=e=>{l&&l(e,void 0),b()},Z=e=>({checked:String(a)===e,onChange:w,value:e,id:e,name:n,inputProps:{"aria-label":e}});return(0,t.jsxs)("div",{className:"".concat(i().container," ").concat(i()[_]),children:[!!p&&(0,t.jsx)("h4",{className:i().title,children:p}),(0,t.jsxs)("div",{className:"".concat(i().wrapper," ").concat(h?i().error:""),onClick:y,children:[(0,t.jsxs)("div",{className:i().iconWrapper,children:[v,(0,t.jsx)("span",{className:i().text,children:(null===(r=null==m?void 0:m.find(e=>e.value==a))||void 0===r?void 0:r.label)||x})]}),(0,t.jsx)(s(),{})]}),(0,t.jsx)(d.default,{open:j,anchorEl:f,onClose:b,children:(0,t.jsxs)("div",{className:i().body,children:[null==m?void 0:m.map((e,r)=>(0,t.jsxs)("div",{className:i().row,children:[(0,t.jsx)(c.Z,{...Z(String(e.value))}),(0,t.jsx)("label",{className:i().label,htmlFor:String(e.value),children:(0,t.jsx)("span",{className:i().text,children:e.label})})]},"".concat(n,"-").concat(r))),!(null==m?void 0:m.length)&&(0,t.jsx)("div",{className:i().row,children:g("not.found")})]})})]})}},83188:function(e,r,a){"use strict";a.d(r,{Z:function(){return d}});var t=a(85893),n=a(67294),i=a(97669),o=a.n(i),l=a(98396),s=a(86886);function d(e){let{children:r,formik:a,lang:i,xs:d,md:c,lg:u,title:m,loading:p,sticky:h,selectedType:_}=e,v=(0,l.Z)("(min-width:900px)");return(0,t.jsx)(s.ZP,{item:!0,xs:d,md:c,lg:u,order:h&&!v?2:void 0,children:(0,t.jsx)("div",{className:h?o().sticky:"",children:(0,t.jsxs)("div",{className:o().wrapper,children:[!!m&&(0,t.jsx)("div",{className:o().header,children:(0,t.jsx)("h3",{className:o().title,children:m})}),n.Children.map(r,e=>n.cloneElement(e,{formik:a,lang:i,loading:p,selectedType:_}))]})})})}},9031:function(e,r,a){"use strict";a.d(r,{Z:function(){return d}});var t=a(85893);a(67294);var n=a(87901),i=a.n(n),o=a(6734),l=a(77262),s=a(11163);function d(e){let{text:r}=e,{t:a}=(0,o.$G)(),{push:n}=(0,s.useRouter)();return(0,t.jsxs)("div",{className:i().wrapper,children:[(0,t.jsx)("img",{src:"/images/delivery.webp",alt:a("unauthorized")}),(0,t.jsx)("p",{className:i().text,children:r}),(0,t.jsx)("div",{className:i().actions,children:(0,t.jsx)(l.Z,{onClick:()=>n("/login"),children:a("login.or.create.account")})})]})}},55642:function(e,r,a){"use strict";a.d(r,{Z:function(){return s}});var t=a(85893);a(67294);var n=a(86555),i=a(47301),o=a.n(i),l=a(88078);function s(e){var r,a;let{data:i,loading:s=!1,fullHeight:d,price:c,drawLine:u}=e,m={lat:Number(null==i?void 0:null===(r=i.location)||void 0===r?void 0:r.latitude)||0,lng:Number(null==i?void 0:null===(a=i.location)||void 0===a?void 0:a.longitude)||0};return(0,t.jsx)("div",{className:"".concat(o().wrapper," ").concat(d?o().fullHeight:""),children:s?(0,t.jsx)(l.Z,{variant:"rectangular",className:o().shimmer}):(0,t.jsx)(n.default,{location:m,defaultZoom:11,drawLine:u,price:c,readOnly:!0,shop:(null==i?void 0:i.delivery_type)==="pickup"?void 0:null==i?void 0:i.shop})})}},56060:function(e,r,a){"use strict";a.r(r),a.d(r,{default:function(){return l}});var t=a(85893);a(67294);var n=a(14564),i=a(90948);let o=(0,i.ZP)(n.ZP)(()=>({"& .MuiBackdrop-root":{backgroundColor:"rgba(0, 0, 0, 0)"},"& .MuiPaper-root":{backgroundColor:"var(--secondary-bg)",boxShadow:"var(--popover-box-shadow)",borderRadius:"10px",maxWidth:"100%"}}));function l(e){let{children:r,...a}=e;return(0,t.jsx)(o,{anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"},...a,children:r})}},61815:function(e,r,a){"use strict";a.r(r),a.d(r,{default:function(){return eT}});var t=a(85893),n=a(67294),i=a(84169),o=a(88767),l=a(18074),s=a(47763),d=a(83188),c=a(86886),u=a(68554),m=a(17662),p=a(15744),h=a.n(p),_=a(58287),v=a(10076),x=a.n(v),g=a(91762),j=a.n(g),f=a(56060),y=a(80865),b=a(47567),w=a(5765),Z=a.n(w),N=a(37562),k=a(95785);function C(e){let{data:r}=e,{t:a}=(0,l.Z)();return(0,t.jsxs)("div",{className:Z().wrapper,children:[(0,t.jsx)("h1",{className:Z().title,children:null==r?void 0:r.type}),(0,t.jsxs)("div",{className:Z().flex,children:[(0,t.jsx)("aside",{className:Z().aside,children:(0,t.jsx)("div",{className:Z().imageWrapper,children:(0,t.jsx)(N.Z,{fill:!0,src:(0,k.Z)(null==r?void 0:r.img),alt:null==r?void 0:r.type,sizes:"320px",quality:90})})}),(0,t.jsx)("main",{className:Z().main,children:(0,t.jsxs)("div",{className:Z().body,children:[(0,t.jsxs)("div",{className:Z().rowItem,children:[(0,t.jsxs)("strong",{children:[a("weight"),": "]}),a("up.to.weight",{number:Number(null==r?void 0:r.max_g)/1e3})]}),(0,t.jsxs)("div",{className:Z().rowItem,children:[(0,t.jsxs)("strong",{children:[a("length"),": "]}),a("up.to.length",{number:Number(null==r?void 0:r.max_length)/100})]}),(0,t.jsxs)("div",{className:Z().rowItem,children:[(0,t.jsxs)("strong",{children:[a("height"),": "]}),a("up.to.length",{number:Number(null==r?void 0:r.max_height)/100})]}),(0,t.jsxs)("div",{className:Z().rowItem,children:[(0,t.jsxs)("strong",{children:[a("width"),": "]}),a("up.to.length",{number:Number(null==r?void 0:r.max_width)/100})]})]})})]})]})}function F(e){var r;let{value:a,name:i,onChange:o,options:s,label:d,error:c,type:u="outlined",placeholder:m,icon:p}=e,{t:v}=(0,l.Z)(),[g,w]=(0,n.useState)(void 0),[Z,N,k,F]=(0,_.Z)(),M=null===(r=null==s?void 0:s.find(e=>e.value==a))||void 0===r?void 0:r.data,S=e=>{o&&o(e,void 0),F()},P=e=>({checked:String(a)===e,onChange:S,value:e,id:e,name:i,inputProps:{"aria-label":e}});return(0,t.jsxs)("div",{className:"".concat(h().container," ").concat(h()[u]),children:[!!d&&(0,t.jsx)("h4",{className:h().title,children:d}),(0,t.jsxs)("div",{className:"".concat(h().wrapper," ").concat(c?h().error:""),onClick:k,children:[(0,t.jsxs)("div",{className:h().iconWrapper,children:[p,(0,t.jsxs)("span",{className:h().text,children:[null==M?void 0:M.type," ",M?(0,t.jsxs)("span",{className:"".concat(h().muted," ").concat(h().text),children:["(",v("up.to.weight",{number:Number(null==M?void 0:M.max_g)/1e3}),")"]}):(0,t.jsx)("span",{children:m})]})]}),(0,t.jsx)(x(),{})]}),(0,t.jsx)(f.default,{open:Z,anchorEl:N,onClose:F,children:(0,t.jsxs)("div",{className:"".concat(h().body," ").concat(h().wide),children:[null==s?void 0:s.map(e=>{var r;return(0,t.jsxs)("div",{className:h().row,children:[(0,t.jsx)(y.Z,{...P(String(e.value))}),(0,t.jsxs)("label",{className:h().label,htmlFor:String(e.value),children:[(0,t.jsxs)("span",{className:h().text,children:[e.label," ",(0,t.jsxs)("span",{className:h().muted,children:["(",v("up.to.weight",{number:Number(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.max_g)/1e3}),")"]})]}),(0,t.jsx)("button",{onClick:()=>w(null==e?void 0:e.data),children:(0,t.jsx)(j(),{})})]})]},e.value)}),!(null==s?void 0:s.length)&&(0,t.jsx)("div",{className:h().row,children:v("not.found")})]})}),(0,t.jsx)(b.default,{open:Boolean(g),onClose:function(){w(void 0)},children:(0,t.jsx)(C,{data:g})})]})}var M=a(6684),S=a(94682),P=a.n(S),I=a(27484),D=a.n(I),q=a(10586),B=a(50720),W=a(80956),E=a(6734),T=a(86701);function Y(e){let{name:r,date:a,time:n,onDateChange:i,onTimeChange:o,label:l,error:s,type:d="outlined",placeholder:c,icon:u,options:m}=e,[p,v,g,j]=(0,_.Z)(),{t:b}=(0,E.$G)(),w=e=>({checked:String(n)===e,onChange(e){o(e),j()},value:e,id:e,name:r,inputProps:{"aria-label":e}});return(0,t.jsxs)("div",{className:"".concat(h().container," ").concat(h()[d]),children:[!!l&&(0,t.jsx)("h4",{className:h().title,children:l}),(0,t.jsxs)("div",{className:"".concat(h().wrapper," ").concat(s?h().error:""),onClick:g,children:[(0,t.jsxs)("div",{className:h().iconWrapper,children:[u,(0,t.jsxs)("span",{className:h().text,children:[a?(0,T.yG)(a,!1):""," ",n]})]}),(0,t.jsx)(x(),{})]}),(0,t.jsx)(f.default,{open:p,anchorEl:v,onClose:j,children:(0,t.jsx)(B._,{dateAdapter:q.y,children:(0,t.jsxs)("div",{className:h().popover,children:[(0,t.jsx)(W.Z,{displayStaticWrapperAs:"desktop",openTo:"day",value:D()(a,"YYYY-MM-DD"),onChange(e){i(D()(e).format("YYYY-MM-DD")),j()}}),(0,t.jsxs)("div",{className:h().body,children:[null==m?void 0:m.map((e,a)=>(0,t.jsxs)("div",{className:h().row,children:[(0,t.jsx)(y.Z,{...w(String(e.value))}),(0,t.jsx)("label",{className:h().label,htmlFor:String(e.value),children:(0,t.jsx)("span",{className:h().text,children:e.label})})]},"".concat(r,"-").concat(a))),!(null==m?void 0:m.length)&&(0,t.jsx)("div",{className:h().row,children:b("not.found")})]})]})})})]})}var V=a(44165),z=a.n(V),K=a(37490),L=a(53167),H=a(98396);function R(e){let{address:r,location:a,locationKey:n,addressKey:i,formik:o,label:l,error:s,type:d="outlined",placeholder:c,icon:u}=e,[m,p,_]=(0,K.Z)(),v=(0,H.Z)("(min-width:1140px)");return(0,t.jsxs)("div",{className:"".concat(h().container," ").concat(h()[d]),children:[!!l&&(0,t.jsx)("h4",{className:h().title,children:l}),(0,t.jsxs)("div",{className:"".concat(h().wrapper," ").concat(s?h().error:""),onClick:p,children:[(0,t.jsxs)("div",{className:"".concat(h().iconWrapper," ").concat(h().limited),children:[u,(0,t.jsx)("span",{className:h().text,children:r||c})]}),(0,t.jsx)(x(),{})]}),(0,t.jsx)(L.Z,{open:m,checkZone:!1,onClose:_,addressKey:i,locationKey:n,address:r,formik:o,latlng:a,fullScreen:!v})]})}var A=a(50931),Q=a.n(A);function U(e){let{formik:r,types:a,loading:n,payments:i,handleSelectType:o}=e,{t:s}=(0,l.Z)(),{location_from:d,location_to:p,type_id:h,delivery_date:_,delivery_time:v,address_from:x,address_to:g}=r.values;return(0,t.jsxs)(c.ZP,{container:!0,spacing:3,columns:{xs:12,md:12,lg:15},children:[(0,t.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:6,children:(0,t.jsx)(R,{formik:r,address:x,location:d,locationKey:"location_from",addressKey:"address_from",icon:(0,t.jsx)(M.iR,{}),label:s("pickup.from"),type:"outlined"})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:6,children:(0,t.jsx)(R,{formik:r,address:g,location:p,label:s("delivery.to"),locationKey:"location_to",addressKey:"address_to",icon:(0,t.jsx)(Q(),{}),type:"outlined"})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:4,children:(0,t.jsx)(F,{type:"outlined",name:"type_id",icon:(0,t.jsx)(M.el,{}),label:s("type"),value:h,options:a,onChange(e){var t;let n=null===(t=a.find(r=>r.value.toString()===e.target.value))||void 0===t?void 0:t.data;n&&o(n),r.handleChange(e)},error:!!r.errors.type_id&&r.touched.type_id})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:4,children:(0,t.jsx)(Y,{type:"outlined",name:"delivery_time",label:s("delivery.date"),icon:(0,t.jsx)(P(),{}),date:_,onDateChange(e){r.setFieldValue("delivery_date",e)},error:!!r.errors.delivery_date&&r.touched.delivery_date,time:v,options:(0,m.ZP)("06:00","23:00",!1,60).map(e=>({label:e,value:e})),onTimeChange:e=>r.handleChange(e)})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:4,children:(0,t.jsx)(u.Z,{type:"outlined",icon:(0,t.jsx)(z(),{}),name:"payment_type_id",label:s("payment.type"),value:r.values.payment_type_id,options:i,onChange:e=>r.handleChange(e),error:!!r.errors.payment_type_id&&r.touched.payment_type_id})})]})}var G=a(27795),O=a.n(G),X=a(30251),$=a(4778),J=a.n($);function ee(e){let{formik:r}=e,{t:a}=(0,l.Z)(),{username_from:n,phone_from:i,address_from:o,location_from:s}=r.values,[d,u,m]=(0,K.Z)(),p=(0,H.Z)("(min-width:1140px)");return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(c.ZP,{container:!0,spacing:4,children:[(0,t.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,t.jsxs)("button",{type:"button",className:O().rowBtn,onClick:u,children:[(0,t.jsxs)("div",{className:O().item,children:[(0,t.jsx)(M.iR,{}),(0,t.jsxs)("div",{className:O().naming,children:[(0,t.jsx)("div",{className:O().label,children:a("address")}),(0,t.jsx)("div",{className:O().value,children:o})]})]}),(0,t.jsx)("div",{className:O().icon,children:(0,t.jsx)(J(),{})})]})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"phone_from",label:a("phone"),value:i,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.phone_from&&r.touched.phone_from})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"username_from",label:a("username"),value:n,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.username_from&&r.touched.username_from})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"house_from",label:a("house"),value:r.values.house_from,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.house_from&&r.touched.house_from})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"stage_from",label:a("stage"),value:r.values.stage_from,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.stage_from&&r.touched.stage_from})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"room_from",label:a("room"),value:r.values.room_from,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.room_from&&r.touched.room_from})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,children:(0,t.jsx)(X.Z,{name:"note",label:a("comment"),value:r.values.note,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.note&&r.touched.note})})]}),(0,t.jsx)(L.Z,{address:o,addressKey:"address_from",locationKey:"location_from",formik:r,checkZone:!1,open:d,onClose:m,latlng:s,fullScreen:!p,title:"select.address"})]})}var er=a(77262),ea=a(90026),et=a(34349),en=a(64698),ei=a(91662),eo=a(24285),el=a(30719),es=a(21697),ed=a(73714);function ec(e){var r,a;let{formik:n,loading:i,selectedType:d}=e,{t:u}=(0,l.Z)(),m=(0,et.C)(en.j),p=(0,H.Z)("(min-width:1140px)"),{username_to:h,phone_to:_,address_to:v,location_to:x,location_from:g,type_id:j,notify:f,description:y}=n.values,[b,w,Z]=(0,K.Z)(),{location:N}=(0,es.r)(),k={latitude:null==N?void 0:N.split(",")[0],longitude:null==N?void 0:N.split(",")[1]},{data:C,isLoading:F,isError:M}=(0,o.useQuery)(["calculateParcel",g,x,j,m],()=>s.Z.calculate({address_from:g,address_to:x,type_id:j,currency_id:null==m?void 0:m.id}),{enabled:Boolean(j),select:e=>e.data.price,onError(e){var r;(0,ed.vU)(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.message)}}),S=e=>{if(!y||(null==y?void 0:y.trim().length)===0){n.setFieldValue("description",e);return}n.setFieldValue("description","".concat(y,", ").concat(e))};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(c.ZP,{container:!0,spacing:4,children:[(0,t.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,t.jsxs)("button",{type:"button",className:O().rowBtn,onClick:w,children:[(0,t.jsxs)("div",{className:O().item,children:[(0,t.jsx)(Q(),{}),(0,t.jsxs)("div",{className:O().naming,children:[(0,t.jsx)("div",{className:O().label,children:u("address")}),(0,t.jsx)("div",{className:O().value,children:v})]})]}),(0,t.jsx)("div",{className:O().icon,children:(0,t.jsx)(J(),{})})]})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"phone_to",label:u("phone"),value:_,onChange:n.handleChange,placeholder:u("type.here"),error:!!n.errors.phone_to&&n.touched.phone_to})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"username_to",label:u("username"),value:h,onChange:n.handleChange,placeholder:u("type.here"),error:!!n.errors.username_to&&n.touched.username_to})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"house_to",label:u("house"),value:n.values.house_to,onChange:n.handleChange,placeholder:u("type.here"),error:!!n.errors.house_to&&n.touched.house_to})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"stage_to",label:u("stage"),value:n.values.stage_to,onChange:n.handleChange,placeholder:u("type.here"),error:!!n.errors.stage_to&&n.touched.stage_to})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,t.jsx)(X.Z,{name:"room_to",label:u("room"),value:n.values.room_to,onChange:n.handleChange,placeholder:u("type.here"),error:!!n.errors.room_to&&n.touched.room_to})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,children:(0,t.jsx)(X.Z,{name:"instructions",label:u("add.instructions"),value:n.values.instructions,onChange:n.handleChange,placeholder:u("type.here"),error:!!n.errors.instructions&&n.touched.instructions})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,t.jsx)(eo.Z,{label:u("item.description"),name:"description",multiline:!0,sx:{".MuiInput-root":{marginTop:"2rem"}},value:n.values.description,onChange:n.handleChange,placeholder:u("what.are.you.sending"),error:!!n.errors.description&&n.touched.description})}),(null==d?void 0:null===(r=d.options)||void 0===r?void 0:r.length)!==0&&(0,t.jsx)(c.ZP,{item:!0,xs:12,children:(0,t.jsx)(el.tq,{spaceBetween:10,slidesPerView:"auto",children:null==d?void 0:null===(a=d.options)||void 0===a?void 0:a.map(e=>{var r,a;let n=null==y?void 0:y.includes((null===(r=e.translation)||void 0===r?void 0:r.title)||"");return(0,t.jsx)(el.o5,{className:O().optionItemWrapper,children:(0,t.jsx)("button",{type:"button",onClick(){var r;return S(null===(r=e.translation)||void 0===r?void 0:r.title)},disabled:n,className:"".concat(O().optionItem," ").concat(n?O().active:""),children:null===(a=e.translation)||void 0===a?void 0:a.title})},e.id)})})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,t.jsx)(eo.Z,{name:"qr_value",multiline:!0,value:n.values.qr_value,onChange:n.handleChange,placeholder:u("item.value.qr"),error:!!n.errors.qr_value&&n.touched.qr_value})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,t.jsxs)("div",{className:O().rowBtn,children:[(0,t.jsx)("div",{className:O().item,children:(0,t.jsxs)("div",{className:O().naming,children:[(0,t.jsx)("div",{className:O().value,children:u("remain.anonymus")}),(0,t.jsx)("div",{className:O().label,children:u("dont.notify.a.recipient")})]})}),(0,t.jsxs)("div",{className:O().switch,children:[(0,t.jsx)(ei.Z,{name:"notify",checked:n.values.notify,onChange:e=>n.setFieldValue("notify",e.target.checked)}),(0,t.jsx)("div",{className:O().value,children:u(f?"on":"off")})]})]})}),(0,t.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,t.jsxs)(er.Z,{type:"submit",disabled:!C||M,loading:F||i,children:[u("pay")," ",(0,t.jsx)(ea.Z,{number:C})]})})]}),(0,t.jsx)(L.Z,{address:v,addressKey:"address_to",locationKey:"location_to",formik:n,checkZone:!1,open:b,onClose:Z,latlng:x||k,fullScreen:!p,title:"select.address"})]})}var eu=a(68053),em=a.n(eu),ep=a(82175),eh=a(29969),e_=a(11163),ev=a(9031),ex=a(85943),eg=a(5848);function ej(e){let{children:r}=e,{t:a}=(0,E.$G)(),i=(0,H.Z)("(min-width:1140px)"),{isAuthenticated:l,user:u}=(0,eh.a)(),{address:m,location:p}=(0,es.r)(),{push:h}=(0,e_.useRouter)(),_=(0,et.C)(en.j),[v,x]=(0,n.useState)(),g=e=>{x(e)},{data:j}=(0,o.useQuery)("payments",()=>ex.Z.getAll()),f=(0,ep.TA)({initialValues:{type_id:"",phone_from:null==u?void 0:u.phone,username_from:[null==u?void 0:u.firstname,null==u?void 0:u.lastname].join(" "),location_from:{latitude:null==p?void 0:p.split(",")[0],longitude:null==p?void 0:p.split(",")[1]},address_from:m,house_from:void 0,stage_from:void 0,room_from:void 0,phone_to:"",username_to:"",location_to:{latitude:null==p?void 0:p.split(",")[0],longitude:(Number(null==p?void 0:p.split(",")[1])+1).toString()},address_to:m,house_to:void 0,stage_to:void 0,room_to:void 0,delivery_date:D()().add(1,"day").format("YYYY-MM-DD"),delivery_time:"13:00",note:"",images:[],payment_type_id:void 0,description:void 0,qr_value:void 0,instructions:void 0},onSubmit(e){var r,a,t,n;let i={currency_id:null==_?void 0:_.id,type_id:e.type_id,rate:null==_?void 0:_.rate,phone_from:e.phone_from,username_from:e.username_from,address_from:{latitude:Number(null===(r=e.location_from)||void 0===r?void 0:r.latitude),longitude:Number(null===(a=e.location_from)||void 0===a?void 0:a.longitude),address:e.address_from,house:e.house_from,stage:e.stage_from,room:e.room_from},phone_to:e.phone_to,username_to:e.username_to,address_to:{latitude:Number(null===(t=e.location_to)||void 0===t?void 0:t.latitude),longitude:Number(null===(n=e.location_to)||void 0===n?void 0:n.longitude),address:e.address_to,house:e.house_to,stage:e.stage_to,room:e.room_to},delivery_date:e.delivery_date,delivery_time:e.delivery_time,note:e.note,images:e.images,description:e.description,instructions:e.instructions,notify:e.notify?1:0,qr_value:e.qr_value};b(i)},validate(e){let r={},t=/^[\+]?[0-9\b]+$/;return e.type_id||(r.type_id=a("required")),e.payment_type_id||(r.payment_type_id=a("required")),e.phone_from?t.test(e.phone_from)||(r.phone_from=a("invalid")):r.phone_from=a("required"),e.username_from||(r.username_from=a("required")),e.address_from||(r.address_from=a("required")),e.phone_to?t.test(e.phone_to)||(r.phone_to=a("invalid")):r.phone_to=a("required"),e.username_to||(r.username_to=a("required")),e.address_to||(r.address_to=a("required")),e.delivery_date||(r.delivery_date=a("required")),e.delivery_time||(r.delivery_time=a("required")),r}}),{isLoading:y,mutate:b}=(0,o.useMutation)({mutationFn:e=>s.Z.create(e),onSuccess(e){var r;let a=f.values.payment_type_id,t={id:e.data.id,payment:{payment_sys_id:a}},n=null===(r=null==j?void 0:j.data.find(e=>e.id==a))||void 0===r?void 0:r.tag;eg.DH.includes(n||"")&&k({name:n,data:{parcel_id:t.id}}),Z(t)},onError(){(0,ed.vU)(a("error.400"))}}),{isLoading:w,mutate:Z}=(0,o.useMutation)({mutationFn:e=>ex.Z.parcelTransaction(e.id,e.payment),onSuccess(e){h("/parcels/".concat(e.data.id))},onError(e){var r;(0,ed.vU)(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.message)}}),{isLoading:N,mutate:k}=(0,o.useMutation)({mutationFn:e=>ex.Z.payExternal(e.name,e.data),onSuccess(e){window.location.replace(e.data.data.url)},onError(e){var r;(0,ed.vU)(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.message)}});return(0,t.jsxs)("div",{className:em().root,children:[(0,t.jsx)("div",{className:em().container,children:(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("div",{className:em().header,children:(0,t.jsx)("h1",{className:em().title,children:a("door.to.door.delivery")})})})}),(0,t.jsx)("div",{className:"container",children:(0,t.jsx)("form",{className:em().wrapper,onSubmit:f.handleSubmit,children:(0,t.jsx)(c.ZP,{container:!0,spacing:i?4:1,children:l?n.Children.map(r,e=>n.cloneElement(e,{formik:f,loading:y||w||N,selectedType:v,handleSelectType:g})):(0,t.jsx)(d.Z,{xs:12,md:8,children:(0,t.jsx)(ev.Z,{text:a("sign.in.parcel.order")})})})})})]})}var ef=a(97669),ey=a.n(ef),eb=a(9473),ew=a(55642);function eZ(e){let{children:r,formik:a,lang:i,xs:l,md:d,lg:u,loading:m,handleSelectType:p}=e,{t:h}=(0,E.$G)(),_=(0,eb.v9)(en.j),v=(0,H.Z)("(min-width:900px)"),{location_from:x,location_to:g,type_id:j}=a.values,{data:f}=(0,o.useQuery)(["calculateParcel",x,g,j,_],()=>s.Z.calculate({address_from:x,address_to:g,type_id:j,currency_id:null==_?void 0:_.id}),{enabled:Boolean(j),select:e=>e.data.price});return(0,t.jsx)(c.ZP,{item:!0,xs:l,md:d,lg:u,children:(0,t.jsxs)("div",{className:ey().container,children:[v&&(0,t.jsx)("div",{className:ey().map,children:(0,t.jsx)(ew.Z,{fullHeight:!0,drawLine:!0,data:{location:x,shop:{id:0,logo_img:"/images/finish.png",location:g,translation:{title:"Finish",locale:"en",description:""},price:0,open:!0}},price:f})}),(0,t.jsxs)("div",{className:ey().heading,children:[(0,t.jsx)("strong",{className:ey().title,children:h("door.to.door.delivery")}),(0,t.jsx)("span",{className:ey().desc,children:h("door.to.door.delivery.description")})]}),(0,t.jsx)("div",{className:ey().wrapper,children:n.Children.map(r,e=>n.cloneElement(e,{formik:a,lang:i,loading:m,handleSelectType:p}))}),f&&(0,t.jsxs)("span",{className:ey().price,children:[h("pay")," ",(0,t.jsx)(ea.Z,{number:f})]})]})})}var eN=a(56861),ek=a.n(eN),eC=a(41664),eF=a.n(eC),eM=a(46205),eS=a.n(eM),eP=a(5152),eI=a.n(eP);let eD=eI()(()=>a.e(4256).then(a.bind(a,64256)),{loadableGenerated:{webpack:()=>[64256]},ssr:!1});function eq(e){let{data:r,list:a}=e,[n,i,o]=(0,K.Z)(),{t:l}=(0,E.$G)(),s=r[0],d=a.filter(e=>{var r;return(null===(r=e[0])||void 0===r?void 0:r.id)!==(null==s?void 0:s.id)}),c=e=>{e.preventDefault(),i()};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eF(),{href:"/",className:eS().story,onClick:c,children:(0,t.jsxs)("div",{className:eS().wrapper,children:[(0,t.jsx)("span",{className:eS().title,children:l(s.title)}),(0,t.jsx)(N.Z,{fill:!0,src:s.img,alt:s.title,sizes:"130px",quality:90,priority:!0})]})}),n&&(0,t.jsx)(eD,{open:n,onClose:o,stories:[r,...d]})]})}let eB={spaceBetween:10,preloadImages:!1,className:"full-width",breakpoints:{1140:{slidesPerView:4,spaceBetween:20},992:{slidesPerView:3.5},576:{slidesPerView:2.5,spaceBetween:10},0:{slidesPerView:2.1}}};function eW(e){let{data:r,loading:a}=e,{t:n}=(0,E.$G)();return(0,t.jsxs)("div",{children:[(0,t.jsx)("h6",{className:ek().title,children:n("how.it.works")}),(0,t.jsx)("div",{className:ek().storyContainer,children:(0,t.jsx)(el.tq,{...eB,slidesPerView:"auto",children:null==r?void 0:r.map((e,a)=>(0,t.jsx)(el.o5,{children:(0,t.jsx)(eq,{data:e,list:r})},a))})})]})}let eE=[[{id:0,img:"/images/parcel/feature1.png",title:"save.time"}],[{id:1,img:"/images/parcel/feature2.png",title:"set.up.delivery"}],[{id:2,img:"/images/parcel/feature3.png",title:"fast.&.secure.delivery"}],[{id:3,img:"/images/parcel/feature4.png",title:"delivery.restrictions"}]];function eT(e){let{}=e,{t:r}=(0,l.Z)(),{data:a}=(0,o.useQuery)("parcelTypes",()=>s.Z.getAllTypes()),{data:n}=(0,o.useQuery)("payments",()=>paymentService.getAll()),u=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.length?e.map(e=>({label:e.type||r(e.tag),value:e.id,data:e})):[]};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.Z,{}),(0,t.jsxs)(ej,{children:[(0,t.jsx)(c.ZP,{item:!0,xs:12,children:(0,t.jsx)(eW,{data:eE})}),(0,t.jsx)(eZ,{xs:12,children:(0,t.jsx)(U,{types:u(null==a?void 0:a.data),payments:u(null==n?void 0:n.data)})}),(0,t.jsx)(d.Z,{title:r("sender.details"),xs:12,md:6,children:(0,t.jsx)(ee,{})}),(0,t.jsx)(d.Z,{title:r("receiver.details"),xs:12,md:6,children:(0,t.jsx)(ec,{})})]})]})}},47763:function(e,r,a){"use strict";var t=a(25728);r.Z={getAll:e=>t.Z.get("/dashboard/user/parcel-orders?".concat(e)),getAllTypes:e=>t.Z.get("/rest/parcel-order/types",{params:e}),getById:(e,r)=>t.Z.get("/dashboard/user/parcel-orders/".concat(e),{params:r}),create:e=>t.Z.post("/dashboard/user/parcel-orders",e),calculate:e=>t.Z.get("/rest/parcel-order/calculate-price",{params:e}),cancel:e=>t.Z.post("/dashboard/user/parcel-orders/".concat(e,"/status/change?status=canceled")),review:(e,r)=>t.Z.post("/dashboard/user/parcel-orders/deliveryman-review/".concat(e),r)}},86701:function(e,r,a){"use strict";a.d(r,{u6:function(){return o},wx:function(){return l},yG:function(){return i}});var t=a(27484),n=a.n(t);function i(e){let r=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!e)return"";let a=n()(e).locale("pt-br");return r?a.format("ddd, DD [de] MMM, HH:mm"):a.format("ddd, DD [de] MMM")}function o(e,r){if(!e)return"";let a=n()(e).locale("pt-br");return r?"".concat(a.format("ddd, DD [de] MMM"),", ").concat(r):a.format("ddd, DD [de] MMM, HH:mm")}function l(e){if(!e)return"";let r=n()(e).locale("pt-br");return r.format("DD [de] MMM, HH:mm")}a(57548)},17662:function(e,r,a){"use strict";a.d(r,{H1:function(){return i},Ps:function(){return o},ZP:function(){return l}});var t=a(27484),n=a.n(t);let i=e=>e.split(":").reduce((e,r)=>60*e+ +r),o=e=>Math.floor(e/60).toLocaleString("en-US",{minimumIntegerDigits:2})+":"+(e%60).toLocaleString("en-US",{minimumIntegerDigits:2});function l(e,r,a){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30,l=i(e),s=i(r),d=a?i(n()().add(t,"minute").format("HH:00")):0;return d>s?[]:(d>l&&(l=d),Array.from({length:Math.floor((s-l)/t)+1},(e,r)=>o(l+r*t)))}},46205:function(e){e.exports={story:"parcelFeatureSingle_story__WQimw",wrapper:"parcelFeatureSingle_wrapper___8CZo",title:"parcelFeatureSingle_title__hw1OX",logo:"parcelFeatureSingle_logo__fTYiO",logoWrapper:"parcelFeatureSingle_logoWrapper__LFDhI",shopTitle:"parcelFeatureSingle_shopTitle__h1TMt"}},27795:function(e){e.exports={wrapper:"parcelForm_wrapper__pYyWp",header:"parcelForm_header__k8amG",title:"parcelForm_title__xE6qU",rowBtn:"parcelForm_rowBtn__iqCkQ",item:"parcelForm_item__bFVUO",naming:"parcelForm_naming__UNlIW",label:"parcelForm_label__IvlSs",value:"parcelForm_value__wLaD_",icon:"parcelForm_icon__p3DPV",switch:"parcelForm_switch__hA9JT",optionItemWrapper:"parcelForm_optionItemWrapper__RwL0S",optionItem:"parcelForm_optionItem__4tmEj",active:"parcelForm_active__Feh1C",spacing:"parcelForm_spacing__d5zvb",map:"parcelForm_map__e2BKo",sticky:"parcelForm_sticky__rMMxp"}},5765:function(e){e.exports={wrapper:"parcelShow_wrapper__UsOmj",title:"parcelShow_title__YiihY",flex:"parcelShow_flex__NfbqK",aside:"parcelShow_aside__j_J8F",imageWrapper:"parcelShow_imageWrapper__3aHKu",main:"parcelShow_main__jfz4X",body:"parcelShow_body__wjQc8",rowItem:"parcelShow_rowItem__4QrlA"}},15744:function(e){e.exports={container:"pickers_container__TB3no",title:"pickers_title__S8luJ",standard:"pickers_standard__lU7vx",outlined:"pickers_outlined__LGPLd",popover:"pickers_popover__3eIRQ",body:"pickers_body__8jDm4",wrapper:"pickers_wrapper___4gAR",error:"pickers_error__Ev8V8",iconWrapper:"pickers_iconWrapper__n7yvB",text:"pickers_text__ObtqW",muted:"pickers_muted__iQ11w",limited:"pickers_limited__WrmYa",wide:"pickers_wide___4gF0",row:"pickers_row__Irlfg",label:"pickers_label__q_hi9",shopWrapper:"pickers_shopWrapper__JxSBV",block:"pickers_block__lxVTK",line:"pickers_line__z0vbc",header:"pickers_header__SReyr",shimmer:"pickers_shimmer__yXFXu"}},97669:function(e){e.exports={wrapper:"shopForm_wrapper__7Uf3y",header:"shopForm_header__GFbkj",title:"shopForm_title__mjJBK",spacing:"shopForm_spacing__Tr2ub",tabs:"shopForm_tabs__Kitlr",tab:"shopForm_tab__3h_af",text:"shopForm_text__6zmMi",active:"shopForm_active__UumR3",map:"shopForm_map__gV3SN",sticky:"shopForm_sticky__5q5u6",container:"shopForm_container__IYNo2",heading:"shopForm_heading__r5ar0",desc:"shopForm_desc__5vfgl",price:"shopForm_price__sJBE0"}},87901:function(e){e.exports={wrapper:"unauthorized_wrapper__fN50q",text:"unauthorized_text__dqTgw",actions:"unauthorized_actions__FBcQz"}},47301:function(e){e.exports={wrapper:"orderMap_wrapper__h__VP",fullHeight:"orderMap_fullHeight__BsYPD",shimmer:"orderMap_shimmer__IX0_w"}},68053:function(e){e.exports={root:"parcelCheckout_root__rLcfp",container:"parcelCheckout_container__Kt_jD",header:"parcelCheckout_header__S8BQz",title:"parcelCheckout_title__bRiKk",wrapper:"parcelCheckout_wrapper___9Mzc",alert:"parcelCheckout_alert__F5txB"}},56861:function(e){e.exports={title:"parcelFeatureList_title__LQEfy",storyContainer:"parcelFeatureList_storyContainer__dYj7Z"}}},function(e){e.O(0,[4564,6886,2175,719,1903,2598,224,6725,6216,5389,9774,2888,179],function(){return e(e.s=86642)}),_N_E=e.O()}]);