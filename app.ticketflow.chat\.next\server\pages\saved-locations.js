(() => {
var exports = {};
exports.id = 3067;
exports.ids = [3067];
exports.modules = {

/***/ 33859:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "savedLocationCard_wrapper__B8MpS",
	"body": "savedLocationCard_body__M3LhE",
	"badge": "savedLocationCard_badge__PNl_w",
	"active": "savedLocationCard_active__tHvai",
	"content": "savedLocationCard_content__djhp4",
	"title": "savedLocationCard_title__Oc_h0",
	"text": "savedLocationCard_text__dYdkD",
	"action": "savedLocationCard_action__AtKjJ",
	"footer": "savedLocationCard_footer___0wbe",
	"flex": "savedLocationCard_flex__r_cx5",
	"ratingIcon": "savedLocationCard_ratingIcon__LwM_Z",
	"greenDot": "savedLocationCard_greenDot__93PSa",
	"dot": "savedLocationCard_dot__KMQKd",
	"actionButton": "savedLocationCard_actionButton__NbVtM",
	"dropDownButton": "savedLocationCard_dropDownButton__frbkv",
	"shopLogo": "savedLocationCard_shopLogo__KovC3"
};


/***/ }),

/***/ 69:
/***/ ((module) => {

// Exports
module.exports = {
	"container": "savedLocationsContainer_container__vU45A",
	"header": "savedLocationsContainer_header__7hxMJ",
	"title": "savedLocationsContainer_title___8w2W",
	"shimmer": "savedLocationsContainer_shimmer__rkTVv"
};


/***/ }),

/***/ 35571:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ SavedLocationCard)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(33859);
/* harmony import */ var _savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var remixicon_react_EqualizerFillIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10865);
/* harmony import */ var remixicon_react_EqualizerFillIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_EqualizerFillIcon__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var remixicon_react_MapPin2LineIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(80110);
/* harmony import */ var remixicon_react_MapPin2LineIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_MapPin2LineIcon__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var remixicon_react_CheckDoubleLineIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(991);
/* harmony import */ var remixicon_react_CheckDoubleLineIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CheckDoubleLineIcon__WEBPACK_IMPORTED_MODULE_4__);






function SavedLocationCard({ address , onSelectAddress  }) {
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: `${(_savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper)}`,
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5___default().body),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: `${(_savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5___default().badge)} ${address.active ? (_savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5___default().active) : ""}`,
                        children: !address.active ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_MapPin2LineIcon__WEBPACK_IMPORTED_MODULE_3___default()), {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_CheckDoubleLineIcon__WEBPACK_IMPORTED_MODULE_4___default()), {})
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5___default().content),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h3", {
                                className: (_savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5___default().title),
                                children: address.title
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                                className: (_savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text),
                                children: address.address?.address
                            })
                        ]
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                onClick: ()=>onSelectAddress(address),
                className: (_savedLocationCard_module_scss__WEBPACK_IMPORTED_MODULE_5___default().action),
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_EqualizerFillIcon__WEBPACK_IMPORTED_MODULE_2___default()), {
                    size: 16
                })
            })
        ]
    });
}


/***/ }),

/***/ 59623:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ SavedLocationsContainer)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var components_savedLocationCard_savedLocationCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(35571);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57987);
/* harmony import */ var _savedLocationsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(69);
/* harmony import */ var _savedLocationsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_savedLocationsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_5__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_4__]);
react_i18next__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];







const AddressModal = next_dynamic__WEBPACK_IMPORTED_MODULE_5___default()(()=>Promise.all(/* import() */[__webpack_require__.e(2078), __webpack_require__.e(5675), __webpack_require__.e(7562), __webpack_require__.e(26), __webpack_require__.e(7567), __webpack_require__.e(1612), __webpack_require__.e(5122), __webpack_require__.e(251), __webpack_require__.e(865), __webpack_require__.e(5567), __webpack_require__.e(6711), __webpack_require__.e(364)]).then(__webpack_require__.bind(__webpack_require__, 26711)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\savedLocationsContainer\\savedLocationsContainer.tsx -> " + "components/addressModal/addressModal"
        ]
    }
});
function SavedLocationsContainer({ data , loading , active  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_1__.useMediaQuery)("(min-width:1140px)");
    const [selectedAddress, setSelectedAddress] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("section", {
        className: "white-bg",
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
            className: "container",
            children: [
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: (_savedLocationsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().container),
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            className: (_savedLocationsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().header),
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h2", {
                                className: (_savedLocationsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),
                                children: t("saved.locations")
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_1__.Stack, {
                            spacing: 2,
                            children: !loading ? data?.map((item)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_savedLocationCard_savedLocationCard__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                                    onSelectAddress: (value)=>setSelectedAddress(value),
                                    address: item
                                }, item.id)) : Array.from(new Array(4)).map((item, idx)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {
                                    variant: "rectangular",
                                    className: (_savedLocationsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().shimmer)
                                }, item))
                        })
                    ]
                }),
                !!selectedAddress && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(AddressModal, {
                    open: !!selectedAddress,
                    onClose: ()=>{
                        setSelectedAddress(null);
                    },
                    latlng: selectedAddress?.location.join(","),
                    address: selectedAddress?.address?.address,
                    fullScreen: !isDesktop,
                    editedAddress: selectedAddress,
                    onClearAddress: ()=>setSelectedAddress(null)
                })
            ]
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 1510:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ SavedLocations),
/* harmony export */   "getServerSideProps": () => (/* binding */ getServerSideProps)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var components_seo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(84169);
/* harmony import */ var containers_savedLocationsContainer_savedLocationsContainer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(59623);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57987);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var services_address__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82027);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([containers_savedLocationsContainer_savedLocationsContainer__WEBPACK_IMPORTED_MODULE_2__, react_i18next__WEBPACK_IMPORTED_MODULE_4__, services_address__WEBPACK_IMPORTED_MODULE_6__]);
([containers_savedLocationsContainer_savedLocationsContainer__WEBPACK_IMPORTED_MODULE_2__, react_i18next__WEBPACK_IMPORTED_MODULE_4__, services_address__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);







function SavedLocations() {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();
    const loader = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);
    const { data , isLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_5__.useQuery)("addresses", ()=>services_address__WEBPACK_IMPORTED_MODULE_6__/* ["default"].getAll */ .Z.getAll({
            perPage: 100
        }));
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_seo__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, {
                title: t("help.center")
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_savedLocationsContainer_savedLocationsContainer__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                data: data,
                loading: isLoading
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                ref: loader
            })
        ]
    });
}
const getServerSideProps = async ()=>{
    const queryClient = new react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClient();
    await queryClient.prefetchInfiniteQuery("addresses", ()=>services_address__WEBPACK_IMPORTED_MODULE_6__/* ["default"].getAll */ .Z.getAll({
            perPage: 10
        }));
    return {
        props: {
            dehydratedState: JSON.parse(JSON.stringify((0,react_query__WEBPACK_IMPORTED_MODULE_5__.dehydrate)(queryClient)))
        }
    };
};

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 82027:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const addressService = {
    create: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post("dashboard/user/addresses", data).then((res)=>res.data),
    update: (id, data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].put */ .Z.put(`dashboard/user/addresses/${id}`, data).then((res)=>res.data),
    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get("dashboard/user/addresses", {
            params
        }).then((res)=>res.data),
    setDefault: (id)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`dashboard/user/address/set-active/${id}`).then((res)=>res.data),
    delete: (id)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"]["delete"] */ .Z["delete"](`dashboard/user/addresses/delete?ids[0]=${id}`).then((res)=>res.data)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (addressService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 65692:
/***/ ((module) => {

"use strict";
module.exports = require("@mui/material");

/***/ }),

/***/ 18442:
/***/ ((module) => {

"use strict";
module.exports = require("@mui/material/styles");

/***/ }),

/***/ 75184:
/***/ ((module) => {

"use strict";
module.exports = require("@reduxjs/toolkit");

/***/ }),

/***/ 2296:
/***/ ((module) => {

"use strict";
module.exports = require("formik");

/***/ }),

/***/ 58557:
/***/ ((module) => {

"use strict";
module.exports = require("google-map-react");

/***/ }),

/***/ 7486:
/***/ ((module) => {

"use strict";
module.exports = require("next-cookies");

/***/ }),

/***/ 94957:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/head.js");

/***/ }),

/***/ 64486:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-blur-svg.js");

/***/ }),

/***/ 50744:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-config-context.js");

/***/ }),

/***/ 35843:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-config.js");

/***/ }),

/***/ 99552:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-loader");

/***/ }),

/***/ 95832:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/loadable.js");

/***/ }),

/***/ 59232:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ 40968:
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ 16689:
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ 61175:
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ 6022:
/***/ ((module) => {

"use strict";
module.exports = require("react-redux");

/***/ }),

/***/ 20997:
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 625:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ArrowLeftLineIcon");

/***/ }),

/***/ 33211:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/Briefcase2FillIcon");

/***/ }),

/***/ 991:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CheckDoubleLineIcon");

/***/ }),

/***/ 4634:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CheckboxCircleLineIcon");

/***/ }),

/***/ 11060:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CloseFillIcon");

/***/ }),

/***/ 5948:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CompassDiscoverLineIcon");

/***/ }),

/***/ 10865:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/EqualizerFillIcon");

/***/ }),

/***/ 53112:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ErrorWarningLineIcon");

/***/ }),

/***/ 12564:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/InformationLineIcon");

/***/ }),

/***/ 80110:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/MapPin2LineIcon");

/***/ }),

/***/ 22069:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/MapPinFillIcon");

/***/ }),

/***/ 78428:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/Search2LineIcon");

/***/ }),

/***/ 86073:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/UserLocationFillIcon");

/***/ }),

/***/ 99648:
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ 22021:
/***/ ((module) => {

"use strict";
module.exports = import("i18next");;

/***/ }),

/***/ 64329:
/***/ ((module) => {

"use strict";
module.exports = import("i18next-http-backend");;

/***/ }),

/***/ 69915:
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ 57987:
/***/ ((module) => {

"use strict";
module.exports = import("react-i18next");;

/***/ }),

/***/ 3590:
/***/ ((module) => {

"use strict";
module.exports = import("react-toastify");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [5152,3075,5728,4169], () => (__webpack_exec__(1510)));
module.exports = __webpack_exports__;

})();