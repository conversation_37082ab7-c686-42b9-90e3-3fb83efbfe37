{"__meta": {"id": "X503f954f003ab99dc8488b6e25cf9f34", "datetime": "2025-08-01 12:37:23", "utime": 1754062643.849458, "method": "GET", "uri": "/api/v1/rest/shops/paginate?type=restaurant&page=1&perPage=12&address%5Blatitude%5D=-18.4942592&address%5Blongitude%5D=-49.47968&lang=pt-BR", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[12:37:23] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1754062643.738443, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754062643.514269, "end": 1754062643.849484, "duration": 0.33521485328674316, "duration_str": "335ms", "measures": [{"label": "Booting", "start": 1754062643.514269, "relative_start": 0, "end": 1754062643.723281, "relative_end": 1754062643.723281, "duration": 0.20901179313659668, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1754062643.72329, "relative_start": 0.20902085304260254, "end": 1754062643.849494, "relative_end": 1.0013580322265625e-05, "duration": 0.1262040138244629, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43233392, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/rest/shops/paginate", "middleware": "api, block.ip", "controller": "App\\Http\\Controllers\\API\\v1\\Rest\\ShopController@paginate", "namespace": null, "prefix": "api/v1/rest", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php&line=50\">\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php:50-68</a>"}, "queries": {"nb_statements": 15, "nb_failed_statements": 0, "accumulated_duration": 0.02887000000000001, "accumulated_duration_str": "28.87ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.02187, "duration_str": "21.87ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 75.753}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 75.753, "width_percent": 1.074}, {"sql": "select count(*) as aggregate from `languages` where `locale` = 'pt-BR'", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 76.827, "width_percent": 1.767}, {"sql": "select * from `settings` where `key` = 'by_subscription' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["by_subscription"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php:52", "connection": "foodyman", "start_percent": 78.594, "width_percent": 1.42}, {"sql": "select * from `shops` where `shops`.`id` = 501 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 332}, {"index": 24, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 330}, {"index": 32, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 105}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 63}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Models\\Shop.php:332", "connection": "foodyman", "start_percent": 80.014, "width_percent": 2.009}, {"sql": "select * from `settings` where `key` = 'by_subscription' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["by_subscription"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 353}, {"index": 23, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 105}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 63}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Models\\Shop.php:353", "connection": "foodyman", "start_percent": 82.023, "width_percent": 1.247}, {"sql": "select count(*) as aggregate from `shops` where `status` = 'approved' and exists (select * from `shop_delivery_zone` where `shops`.`id` = `shop_delivery_zone`.`shop_id` and `shop_delivery_zone`.`deleted_at` is null) and exists (select * from `shop_translations` where `shops`.`id` = `shop_translations`.`shop_id` and `locale` = 'pt-BR' and `shop_translations`.`deleted_at` is null) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["approved", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 206}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 63}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:206", "connection": "foodyman", "start_percent": 83.27, "width_percent": 2.251}, {"sql": "select `shops`.*, (select count(*) from `reviews` where `shops`.`id` = `reviews`.`assignable_id` and `reviews`.`assignable_type` = 'App\\Models\\Shop' and `reviews`.`deleted_at` is null) as `reviews_count`, (select avg(`reviews`.`rating`) from `reviews` where `shops`.`id` = `reviews`.`assignable_id` and `reviews`.`assignable_type` = 'App\\Models\\Shop' and `reviews`.`deleted_at` is null) as `reviews_avg_rating` from `shops` where `status` = 'approved' and exists (select * from `shop_delivery_zone` where `shops`.`id` = `shop_delivery_zone`.`shop_id` and `shop_delivery_zone`.`deleted_at` is null) and exists (select * from `shop_translations` where `shops`.`id` = `shop_translations`.`shop_id` and `locale` = 'pt-BR' and `shop_translations`.`deleted_at` is null) and `shops`.`deleted_at` is null order by `id` desc limit 12 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "App\\Models\\Shop", "approved", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 206}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 63}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:206", "connection": "foodyman", "start_percent": 85.521, "width_percent": 2.563}, {"sql": "select * from `shop_translations` where `shop_translations`.`shop_id` in (501) and `locale` = 'pt-BR' and `shop_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 206}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 63}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:206", "connection": "foodyman", "start_percent": 88.085, "width_percent": 1.178}, {"sql": "select `bonusable_type`, `bonusable_id`, `bonus_quantity`, `bonus_stock_id`, `expired_at`, `value`, `type`, `status` from `bonuses` where `bonuses`.`bonusable_id` in (501) and `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `expired_at` > '2025-08-01 12:37:23' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "2025-08-01 12:37:23", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 206}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 63}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:206", "connection": "foodyman", "start_percent": 89.262, "width_percent": 1.697}, {"sql": "select * from `shop_closed_dates` where `shop_closed_dates`.`shop_id` in (501) and `shop_closed_dates`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 206}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 63}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:206", "connection": "foodyman", "start_percent": 90.959, "width_percent": 1.386}, {"sql": "select * from `shop_working_days` where `shop_working_days`.`shop_id` in (501) and `shop_working_days`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 206}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 63}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:206", "connection": "foodyman", "start_percent": 92.345, "width_percent": 1.974}, {"sql": "select `id`, `shop_id`, `end`, `active` from `discounts` where `discounts`.`shop_id` in (501) and `end` >= '2025-08-01 12:37:23' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-01 12:37:23", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 206}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 63}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:206", "connection": "foodyman", "start_percent": 94.319, "width_percent": 2.217}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 59}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 96.536, "width_percent": 2.355}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 59}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 98.892, "width_percent": 1.108}]}, "models": {"data": {"App\\Models\\ShopWorkingDay": 7, "App\\Models\\ShopTranslation": 1, "App\\Models\\Shop": 2, "App\\Models\\Currency": 1, "App\\Models\\Language": 1}, "count": 12}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f87f414-816a-4fb3-ab1f-adacc83a6871\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/rest/shops/paginate", "status_code": "<pre class=sf-dump id=sf-dump-523595363 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-523595363\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1847010667 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">restaurant</span>\"\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>perPage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-18.4942592</span>\"\n    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">-49.47968</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1847010667\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-584038105 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">restaurant</span>\"\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>perPage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-18.4942592</span>\"\n    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">-49.47968</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-584038105\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-436665201 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436665201\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-147348558 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54619</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"139 characters\">/api/v1/rest/shops/paginate?type=restaurant&amp;page=1&amp;perPage=12&amp;address%5Blatitude%5D=-18.4942592&amp;address%5Blongitude%5D=-49.47968&amp;lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/api/v1/rest/shops/paginate</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/index.php/api/v1/rest/shops/paginate</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"111 characters\">type=restaurant&amp;page=1&amp;perPage=12&amp;address%5Blatitude%5D=-18.4942592&amp;address%5Blongitude%5D=-49.47968&amp;lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754062643.5143</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754062643</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">type=restaurant&amp;page=1&amp;perPage=12&amp;address%5Blatitude%5D=-18.4942592&amp;address%5Blongitude%5D=-49.47968&amp;lang=pt-BR</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147348558\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-539175966 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-539175966\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2106231807 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 15:37:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4991</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106231807\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1237656454 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1237656454\", {\"maxDepth\":0})</script>\n"}}