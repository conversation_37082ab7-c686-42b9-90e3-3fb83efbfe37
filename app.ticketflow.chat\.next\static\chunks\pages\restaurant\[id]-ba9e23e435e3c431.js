(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1415],{21271:function(l,d,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/restaurant/[id]",function(){return o(58901)}])},58901:function(l,d,o){"use strict";o.r(d),o.d(d,{__N_SSP:function(){return N},default:function(){return E}});var i=o(85893),n=o(67294),e=o(84169),a=o(52773),t=o(72363),u=o(4288),v=o(98396),r=o(80427),s=o(88767),c=o(1612),g=o(11163),p=o(32837),h=o(34349),_=o(64698),b=o(6734),m=o(8423),f=o(5152),j=o.n(f),y=o(95785),x=o(48606);let w=j()(()=>Promise.resolve().then(o.bind(o,47567)),{loadableGenerated:{webpack:()=>[47567]}}),C=j()(()=>o.e(5878).then(o.bind(o,35878)),{loadableGenerated:{webpack:()=>[35878]}}),k=j()(()=>Promise.resolve().then(o.bind(o,21014)),{loadableGenerated:{webpack:()=>[21014]}}),S=j()(()=>o.e(3595).then(o.bind(o,3595)),{loadableGenerated:{webpack:()=>[3595]}}),Z=j()(()=>o.e(7746).then(o.bind(o,7746)),{loadableGenerated:{webpack:()=>[7746]}}),P=(l,d,o,e)=>null==l?void 0:l.map(l=>{var o,a;return(0,i.jsx)(n.Fragment,{children:(0,i.jsx)(u.Z,{uuid:l.uuid,title:e||(null===(o=l.translation)||void 0===o?void 0:o.title),products:l.products.concat((null===(a=l.children)||void 0===a?void 0:a.length)>0?l.children.flatMap(l=>l.products):[])||[],loading:d})},l.id)});var N=!0;function E(l){var d,o,f,j,N,E,G,T,O,A,F,B;let{memberState:L}=l,{t:M,i18n:Q}=(0,b.$G)(),X=Q.language,q=(0,v.Z)("(min-width:1140px)"),{query:D,replace:I}=(0,g.useRouter)(),J=Number(D.id),R=(0,h.C)(_.j),{product:V,isOpen:Y}=(0,h.C)(m.Fn),$=(0,h.T)(),z=Boolean(D.product)||Y,H=String(D.product||""),[K,U]=(0,n.useState)(!1),[W,ll]=(0,n.useState)([]),[ld,lo]=(0,n.useState)(""),li=(0,x.Z)(ld,500);(0,n.useEffect)(()=>{var l,d,o;(null==la?void 0:null===(l=la.data)||void 0===l?void 0:null===(d=l.all)||void 0===d?void 0:d.length)&&K&&((null==li?void 0:li.length)?ls(li):ll((null==la?void 0:null===(o=la.data)||void 0===o?void 0:o.all)||[]))},[li]);let{data:ln,error:le}=(0,s.useQuery)(["shop",J,X],()=>c.Z.getById(J),{keepPreviousData:!0}),{data:la,isLoading:lt}=(0,s.useQuery)(["products",J,null==R?void 0:R.id,X,null==D?void 0:D.category_id,null==D?void 0:D.sub_category_id,null==D?void 0:D.brands],()=>{let l={currency_id:null==R?void 0:R.id,category_id:(null==D?void 0:D.sub_category_id)||(null==D?void 0:D.category_id)||void 0};if(null==D?void 0:D.brands){if(Array.isArray(D.brands)){var d;delete l["brand_ids[0]"],l=Object.assign(l,...null==D?void 0:null===(d=D.brands)||void 0===d?void 0:d.map((l,d)=>({["brand_ids[".concat(d,"]")]:l})))}else l=Object.assign(l,{"brand_ids[0]":null==D?void 0:D.brands})}return p.Z.getAllShopProducts(J,l)},{staleTime:0,onSuccess(l){var d;ll((null==l?void 0:null===(d=l.data)||void 0===d?void 0:d.all)||[])}}),lu=(0,n.useMemo)(()=>{var l,d;return null==la?void 0:null===(l=la.data)||void 0===l?void 0:null===(d=l.all)||void 0===d?void 0:d.map(l=>({...l,products:[]}))},[null==la?void 0:null===(d=la.data)||void 0===d?void 0:d.all]),lv=()=>{$((0,m.JY)());let l={id:J};(null==D?void 0:D.category_id)&&(l.category_id=null==D?void 0:D.category_id),(null==D?void 0:D.sub_category_id)&&(l.sub_category_id=null==D?void 0:D.sub_category_id),H&&I({query:l},void 0,{shallow:!0})},lr=()=>{(null==ld?void 0:ld.length)?lo(""):U(!1)},ls=(0,n.useCallback)(function(){var l,d,o,i,n,e,a,t,u,v,r,s,c;let g=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",p=[];for(let h=0;h<(null==la?void 0:null===(l=la.data)||void 0===l?void 0:null===(d=l.all)||void 0===d?void 0:d.length);h++){let _=null==la?void 0:null===(o=la.data)||void 0===o?void 0:null===(i=o.all)||void 0===i?void 0:i[h],b={...null==la?void 0:null===(n=la.data)||void 0===n?void 0:null===(e=n.all)||void 0===e?void 0:e[h],products:[]};for(let m=0;m<(null==_?void 0:null===(a=_.products)||void 0===a?void 0:a.length);m++){let f=null==_?void 0:null===(u=_.products)||void 0===u?void 0:u[m];(null===(s=null==f?void 0:null===(v=f.translation)||void 0===v?void 0:null===(r=v.title)||void 0===r?void 0:r.toLowerCase())||void 0===s?void 0:s.includes(null==g?void 0:g.toLowerCase()))&&(null==b||null===(c=b.products)||void 0===c||c.push(f))}(null==b?void 0:null===(t=b.products)||void 0===t?void 0:t.length)&&(null==p||p.push(b))}ll(p)},[null==la?void 0:null===(o=la.data)||void 0===o?void 0:o.all]);return le?(console.log("error => ",le),I("/"),(0,i.jsx)(S,{})):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e.Z,{title:null==ln?void 0:null===(f=ln.data)||void 0===f?void 0:null===(j=f.translation)||void 0===j?void 0:j.title,description:null==ln?void 0:null===(N=ln.data)||void 0===N?void 0:null===(E=N.translation)||void 0===E?void 0:E.description,image:(0,y.Z)(null==ln?void 0:null===(G=ln.data)||void 0===G?void 0:G.logo_img)}),(0,i.jsxs)(a.Z,{data:null==ln?void 0:ln.data,memberState:L,categories:lu||[],children:[(0,i.jsx)(t.Z,{}),K?(0,i.jsx)(Z,{searchTerm:ld,setSearchTerm:lo,handleClose:lr}):(0,i.jsx)(r.Z,{categories:lu||[],loading:lt,isPopularVisible:!!(null==la?void 0:null===(T=la.data)||void 0===T?void 0:null===(O=T.recommended)||void 0===O?void 0:O.length),openSearch:()=>U(!0)}),(null==la?void 0:null===(A=la.data)||void 0===A?void 0:null===(F=A.recommended)||void 0===F?void 0:F.length)&&!(null==li?void 0:li.length)?(0,i.jsx)(u.Z,{title:M("popular"),products:(null==la?void 0:null===(B=la.data)||void 0===B?void 0:B.recommended)||[],loading:lt}):(0,i.jsx)("div",{}),W?P(W,lt,!!(null==D?void 0:D.category_id)):(0,i.jsx)("div",{}),q?(0,i.jsx)(w,{open:!!z,onClose:lv,children:(0,i.jsx)(C,{handleClose:lv,data:V,uuid:H})}):(0,i.jsx)(k,{open:!!z,onClose:lv,children:(0,i.jsx)(C,{handleClose:lv,data:V,uuid:H})})]})]})}}},function(l){l.O(0,[719,2218,9774,2888,179],function(){return l(l.s=21271)}),_N_E=l.O()}]);