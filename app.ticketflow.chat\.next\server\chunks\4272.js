exports.id = 4272;
exports.ids = [4272];
exports.modules = {

/***/ 45209:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "paymentMethod_wrapper__hDB06",
	"body": "paymentMethod_body__niNGC",
	"row": "paymentMethod_row__pHCIA",
	"label": "paymentMethod_label__FI5nM",
	"text": "paymentMethod_text__cmylm",
	"footer": "paymentMethod_footer__3olxQ",
	"action": "paymentMethod_action__rnLFd"
};


/***/ }),

/***/ 84272:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ PaymentMethod)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57987);
/* harmony import */ var components_inputs_radioInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(80865);
/* harmony import */ var _paymentMethod_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(45209);
/* harmony import */ var _paymentMethod_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_paymentMethod_module_scss__WEBPACK_IMPORTED_MODULE_4__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_2__]);
react_i18next__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];





function PaymentMethod({ value , list , onSubmit , isButtonLoading =false , category  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();
    const [selectedValue, setSelectedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);
    // Define which payment methods belong to each category
    const ONLINE_PAYMENT_METHODS = [
        "mercado-pago",
        "stripe",
        "wallet"
    ];
    const DELIVERY_PAYMENT_METHODS = [
        "cash_delivery",
        "card_delivery",
        "pix_delivery",
        "debit_delivery"
    ];
    // Define the desired order for delivery payment methods
    const DELIVERY_PAYMENT_ORDER = [
        "cash_delivery",
        "pix_delivery",
        "card_delivery",
        "debit_delivery"
    ];
    // Filter and sort payment methods based on selected category
    const filteredList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{
        if (!category) return list;
        if (category === "pay_now") {
            return list.filter((payment)=>ONLINE_PAYMENT_METHODS.includes(payment.tag));
        } else if (category === "pay_on_delivery") {
            const deliveryMethods = list.filter((payment)=>DELIVERY_PAYMENT_METHODS.includes(payment.tag));
            // Sort delivery methods according to the specified order
            return deliveryMethods.sort((a, b)=>{
                const indexA = DELIVERY_PAYMENT_ORDER.indexOf(a.tag);
                const indexB = DELIVERY_PAYMENT_ORDER.indexOf(b.tag);
                // If both methods are in the order array, sort by their position
                if (indexA !== -1 && indexB !== -1) {
                    return indexA - indexB;
                }
                // If only one is in the order array, prioritize it
                if (indexA !== -1) return -1;
                if (indexB !== -1) return 1;
                // If neither is in the order array, maintain original order
                return 0;
            });
        }
        return list;
    }, [
        list,
        category
    ]);
    const handleChange = (event)=>{
        setSelectedValue(event.target.value);
        onSubmit(event.target.value);
    };
    const controlProps = (item)=>({
            checked: selectedValue === item,
            onChange: handleChange,
            value: item,
            id: item,
            name: "payment_method",
            inputProps: {
                "aria-label": item
            }
        });
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        className: (_paymentMethod_module_scss__WEBPACK_IMPORTED_MODULE_4___default().wrapper),
        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
            className: (_paymentMethod_module_scss__WEBPACK_IMPORTED_MODULE_4___default().body),
            children: filteredList.map((item)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: (_paymentMethod_module_scss__WEBPACK_IMPORTED_MODULE_4___default().row),
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_radioInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            ...controlProps(item.tag)
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("label", {
                            className: (_paymentMethod_module_scss__WEBPACK_IMPORTED_MODULE_4___default().label),
                            htmlFor: item.tag,
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                className: (_paymentMethod_module_scss__WEBPACK_IMPORTED_MODULE_4___default().text),
                                children: t(item.tag)
                            })
                        })
                    ]
                }, item.id))
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;