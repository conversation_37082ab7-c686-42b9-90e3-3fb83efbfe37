{"version": 1, "images": {"deviceSizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840], "imageSizes": [16, 32, 48, 64, 96, 128, 256, 384], "path": "/_next/image", "loader": "default", "loaderFile": "", "domains": [], "disableStaticImages": false, "minimumCacheTTL": 3600, "formats": ["image/webp"], "dangerouslyAllowSVG": true, "contentSecurityPolicy": "default-src 'self'; script-src 'none'; sandbox;", "remotePatterns": [{"protocol": "http", "hostname": "^(?:^(?:localhost)$)$", "port": "8000", "pathname": "^(?:(?!\\.)(?:(?:(?!(?:^|[\\\\/])\\.).)*?)[\\\\/]?)$"}, {"protocol": "https", "hostname": "^(?:^(?:demo\\-api\\.foodyman\\.org)$)$", "pathname": "^(?:(?!\\.)(?:(?:(?!(?:^|[\\\\/])\\.).)*?)[\\\\/]?)$"}, {"protocol": "https", "hostname": "^(?:^(?:lh3\\.googleusercontent\\.com)$)$", "pathname": "^(?:(?!\\.)(?:(?:(?!(?:^|[\\\\/])\\.).)*?)[\\\\/]?)$"}, {"protocol": "https", "hostname": "^(?:^(?:app\\.ticketflow\\.chat)$)$", "pathname": "^(?:(?!\\.)(?:(?:(?!(?:^|[\\\\/])\\.).)*?)[\\\\/]?)$"}], "unoptimized": true, "sizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840, 16, 32, 48, 64, 96, 128, 256, 384]}}