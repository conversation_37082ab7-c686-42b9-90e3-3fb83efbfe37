exports.id = 3055;
exports.ids = [3055];
exports.modules = {

/***/ 59345:
/***/ ((module) => {

// Exports
module.exports = {
	"container": "shopInfoDetails_container__bmjzQ",
	"closeBtn": "shopInfoDetails_closeBtn__0zoaO",
	"map": "shopInfoDetails_map__LYND6",
	"wrapper": "shopInfoDetails_wrapper__4kph8",
	"header": "shopInfoDetails_header__EGjVH",
	"title": "shopInfoDetails_title__ywD6c",
	"text": "shopInfoDetails_text__461YG",
	"body": "shopInfoDetails_body__wM6Is",
	"flexBtn": "shopInfoDetails_flexBtn__I5ZX2",
	"flex": "shopInfoDetails_flex__ucX9A",
	"details": "shopInfoDetails_details__utJj_",
	"branch": "shopInfoDetails_branch__iSEfj",
	"content": "shopInfoDetails_content__l3flV"
};


/***/ }),

/***/ 21533:
/***/ ((module) => {

// Exports
module.exports = {
	"flex": "shopInfo_flex__QVVm2",
	"text": "shopInfo_text__g999n",
	"textBtn": "shopInfo_textBtn__ULh6Z"
};


/***/ }),

/***/ 69001:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ShopInfoDetails)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(59345);
/* harmony import */ var _shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17__);
/* harmony import */ var components_map_map__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(25567);
/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(11060);
/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var remixicon_react_MapPin2FillIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57296);
/* harmony import */ var remixicon_react_MapPin2FillIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_MapPin2FillIcon__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var remixicon_react_FileCopyLineIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4979);
/* harmony import */ var remixicon_react_FileCopyLineIcon__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_FileCopyLineIcon__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var remixicon_react_TimeFillIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(33718);
/* harmony import */ var remixicon_react_TimeFillIcon__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_TimeFillIcon__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var remixicon_react_AddLineIcon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(75265);
/* harmony import */ var remixicon_react_AddLineIcon__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_AddLineIcon__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var remixicon_react_SubtractLineIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(93132);
/* harmony import */ var remixicon_react_SubtractLineIcon__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_SubtractLineIcon__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var remixicon_react_StarFillIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(93725);
/* harmony import */ var remixicon_react_StarFillIcon__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_StarFillIcon__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var remixicon_react_Store3FillIcon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(26359);
/* harmony import */ var remixicon_react_Store3FillIcon__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_Store3FillIcon__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(57987);
/* harmony import */ var hooks_useShopWorkingSchedule__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(73444);
/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(74621);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var services_shop__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(1612);
/* harmony import */ var remixicon_react_RoadMapLineIcon__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(32228);
/* harmony import */ var remixicon_react_RoadMapLineIcon__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_RoadMapLineIcon__WEBPACK_IMPORTED_MODULE_16__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_map_map__WEBPACK_IMPORTED_MODULE_2__, react_i18next__WEBPACK_IMPORTED_MODULE_11__, components_alert_toast__WEBPACK_IMPORTED_MODULE_13__, services_shop__WEBPACK_IMPORTED_MODULE_15__]);
([components_map_map__WEBPACK_IMPORTED_MODULE_2__, react_i18next__WEBPACK_IMPORTED_MODULE_11__, components_alert_toast__WEBPACK_IMPORTED_MODULE_13__, services_shop__WEBPACK_IMPORTED_MODULE_15__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);


















function ShopInfoDetails({ data , onClose  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation)();
    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({
        lat: Number(data?.location?.latitude),
        lng: Number(data?.location?.longitude)
    });
    const { workingSchedule , isShopClosed  } = (0,hooks_useShopWorkingSchedule__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z)(data);
    const [openTime, setOpenTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [openBranch, setOpenBranch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const { data: branches  } = (0,react_query__WEBPACK_IMPORTED_MODULE_14__.useQuery)([
        "branches",
        data?.id
    ], ()=>services_shop__WEBPACK_IMPORTED_MODULE_15__/* ["default"].getAllBranches */ .Z.getAllBranches({
            shop_id: data?.id
        }), {
        enabled: !!data?.id
    });
    const copyToClipBoard = async ()=>{
        try {
            await navigator.clipboard.writeText(data?.translation?.address || "");
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_13__/* .success */ .Vp)(t("copied"));
        } catch (err) {
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_13__/* .error */ .vU)("Failed to copy!");
        }
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().container),
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().closeBtn),
                onClick: onClose,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_3___default()), {})
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().map),
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_map_map__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                    location: location,
                    readOnly: true
                })
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().wrapper),
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().header),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h2", {
                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().title),
                                children: data?.translation?.title
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().text),
                                children: data?.tags?.map((item)=>item.translation?.title)?.join(" • ")
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().body),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().flexBtn),
                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                                    className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().flex),
                                    onClick: ()=>setLocation({
                                            lat: Number(data?.location?.latitude),
                                            lng: Number(data?.location?.longitude)
                                        }),
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_MapPin2FillIcon__WEBPACK_IMPORTED_MODULE_4___default()), {}),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                            className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().text),
                                            children: data?.translation?.address
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                                            onClick: (e)=>{
                                                e.stopPropagation();
                                                copyToClipBoard();
                                            },
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_FileCopyLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                                        })
                                    ]
                                })
                            }),
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().flexBtn),
                                children: [
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                                        className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().flex),
                                        onClick: ()=>setOpenTime(!openTime),
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_TimeFillIcon__WEBPACK_IMPORTED_MODULE_6___default()), {}),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().text),
                                                children: isShopClosed ? t("closed") : `${t("open.until")} — ${workingSchedule.to}`
                                            }),
                                            openTime ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_SubtractLineIcon__WEBPACK_IMPORTED_MODULE_8___default()), {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_AddLineIcon__WEBPACK_IMPORTED_MODULE_7___default()), {})
                                        ]
                                    }),
                                    openTime && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("ul", {
                                        className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().details),
                                        children: data?.shop_working_days?.map((item)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("li", {
                                                children: [
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("strong", {
                                                        children: [
                                                            t(item.day),
                                                            ": "
                                                        ]
                                                    }),
                                                    `${item.from} — ${item.to}`
                                                ]
                                            }, "day" + item.id))
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().flexBtn),
                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().flex),
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_StarFillIcon__WEBPACK_IMPORTED_MODULE_9___default()), {}),
                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                                            className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().text),
                                            children: [
                                                data?.rating_avg?.toFixed(1) || 0,
                                                " ",
                                                data?.rating_avg ? `(${data?.reviews_count}+ ${t("ratings")})` : ""
                                            ]
                                        })
                                    ]
                                })
                            }),
                            branches && branches?.data?.length !== 0 && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().flexBtn),
                                children: [
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                                        onClick: ()=>setOpenBranch(!openBranch),
                                        className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().flex),
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_Store3FillIcon__WEBPACK_IMPORTED_MODULE_10___default()), {}),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().text),
                                                children: t("branches")
                                            }),
                                            openBranch ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_SubtractLineIcon__WEBPACK_IMPORTED_MODULE_8___default()), {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_AddLineIcon__WEBPACK_IMPORTED_MODULE_7___default()), {})
                                        ]
                                    }),
                                    openBranch && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("ul", {
                                        className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().details),
                                        children: branches?.data?.map((branch)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("li", {
                                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().branch),
                                                children: [
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                                        className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().content),
                                                        children: [
                                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                                                className: (_shopInfoDetails_module_scss__WEBPACK_IMPORTED_MODULE_17___default().title),
                                                                children: [
                                                                    branch.translation?.title,
                                                                    ":",
                                                                    " "
                                                                ]
                                                            }),
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                                children: branch?.address?.address
                                                            })
                                                        ]
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                                                        onClick: ()=>setLocation({
                                                                lat: Number(branch.location.latitude),
                                                                lng: Number(branch.location.longitude)
                                                            }),
                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_RoadMapLineIcon__WEBPACK_IMPORTED_MODULE_16___default()), {})
                                                    })
                                                ]
                                            }, "branch" + branch.id))
                                    })
                                ]
                            })
                        ]
                    })
                ]
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 45518:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ShopInfo)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _shopInfo_module_scss__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(21533);
/* harmony import */ var _shopInfo_module_scss__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_shopInfo_module_scss__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(37490);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57987);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var containers_modal_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(47567);
/* harmony import */ var containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(30182);
/* harmony import */ var components_shopInfoDetails_shopInfoDetails__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(69001);
/* harmony import */ var components_deliveryTimes_deliveryTimes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(25058);
/* harmony import */ var hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(34349);
/* harmony import */ var redux_slices_order__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(23650);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(1635);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_12__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_shopInfoDetails_shopInfoDetails__WEBPACK_IMPORTED_MODULE_7__, components_deliveryTimes_deliveryTimes__WEBPACK_IMPORTED_MODULE_8__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_shopInfoDetails_shopInfoDetails__WEBPACK_IMPORTED_MODULE_7__, components_deliveryTimes_deliveryTimes__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);














function ShopInfo({ data  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();
    const dispatch = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__/* .useAppDispatch */ .T)();
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_4__.useMediaQuery)("(min-width:1140px)");
    const { order  } = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__/* .useAppSelector */ .C)(redux_slices_order__WEBPACK_IMPORTED_MODULE_10__/* .selectOrder */ .zT);
    const [modal, handleOpen, handleClose] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)();
    const [timeDrawer, handleOpenTimeDrawer, handleCloseTimeDrawer] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)();
    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter)();
    const handleChangeDeliverySchedule = ({ date , time  })=>{
        dispatch((0,redux_slices_order__WEBPACK_IMPORTED_MODULE_10__/* .setDeliveryDate */ .Zp)({
            delivery_time: time,
            delivery_date: date,
            shop_id: data?.id
        }));
    };
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (order.shop_id !== data?.id) {
            dispatch((0,redux_slices_order__WEBPACK_IMPORTED_MODULE_10__/* .clearOrder */ .bn)());
        }
    }, [
        data
    ]);
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_shopInfo_module_scss__WEBPACK_IMPORTED_MODULE_13___default().flex),
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                className: (_shopInfo_module_scss__WEBPACK_IMPORTED_MODULE_13___default().textBtn),
                onClick: handleOpen,
                children: t("more.info")
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                className: (_shopInfo_module_scss__WEBPACK_IMPORTED_MODULE_13___default().textBtn),
                onClick: handleOpenTimeDrawer,
                children: order.delivery_time ? t("edit.schedule") : t("schedule")
            }),
            !!order.delivery_time && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_shopInfo_module_scss__WEBPACK_IMPORTED_MODULE_13___default().text),
                children: [
                    dayjs__WEBPACK_IMPORTED_MODULE_11___default()(order.delivery_date).format("ddd, MMM DD,"),
                    " ",
                    order.delivery_time
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                className: (_shopInfo_module_scss__WEBPACK_IMPORTED_MODULE_13___default().textBtn),
                onClick: ()=>push({
                        pathname: "/recipes",
                        query: {
                            shop_id: data?.id
                        }
                    }),
                children: t("recipes")
            }),
            isDesktop ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_modal_modal__WEBPACK_IMPORTED_MODULE_5__["default"], {
                open: modal,
                onClose: handleClose,
                closable: false,
                children: modal && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_shopInfoDetails_shopInfoDetails__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                    data: data,
                    onClose: handleClose
                })
            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_6__["default"], {
                open: modal,
                onClose: handleClose,
                children: modal && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_shopInfoDetails_shopInfoDetails__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                    data: data,
                    onClose: handleClose
                })
            }),
            isDesktop ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_modal_modal__WEBPACK_IMPORTED_MODULE_5__["default"], {
                open: timeDrawer,
                onClose: handleCloseTimeDrawer,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_deliveryTimes_deliveryTimes__WEBPACK_IMPORTED_MODULE_8__["default"], {
                    data: data,
                    handleClose: handleCloseTimeDrawer,
                    handleChangeDeliverySchedule: handleChangeDeliverySchedule
                })
            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_6__["default"], {
                open: timeDrawer,
                onClose: handleCloseTimeDrawer,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_deliveryTimes_deliveryTimes__WEBPACK_IMPORTED_MODULE_8__["default"], {
                    data: data,
                    handleClose: handleCloseTimeDrawer,
                    handleChangeDeliverySchedule: handleChangeDeliverySchedule
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 23650:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ZP": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   "Zp": () => (/* binding */ setDeliveryDate),
/* harmony export */   "bn": () => (/* binding */ clearOrder),
/* harmony export */   "zT": () => (/* binding */ selectOrder)
/* harmony export */ });
/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(75184);
/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__);

const initialState = {
    order: {}
};
const orderSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({
    name: "order",
    initialState,
    reducers: {
        setDeliveryDate (state, action) {
            const { payload  } = action;
            state.order.delivery_date = payload.delivery_date;
            state.order.delivery_time = payload.delivery_time;
            state.order.shop_id = payload.shop_id;
        },
        clearOrder (state) {
            state.order = {};
        }
    }
});
const { setDeliveryDate , clearOrder  } = orderSlice.actions;
const selectOrder = (state)=>state.order;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (orderSlice.reducer);


/***/ }),

/***/ 59041:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ checkIsDisabledDay)
/* harmony export */ });
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1635);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_0__);

function getSchedule(day, data) {
    return data?.shop_working_days?.find((item)=>item.day?.toLowerCase() === day.format("dddd").toLowerCase());
}
function checkIsDisabledDay(dayIndex, data) {
    const today = dayIndex === 0;
    const day = dayjs__WEBPACK_IMPORTED_MODULE_0___default()().add(dayIndex, "day");
    const date = dayjs__WEBPACK_IMPORTED_MODULE_0___default()().format("YYYY-MM-DD");
    let isTimeAfter = false;
    const foundedSchedule = getSchedule(day, data);
    const isHoliday = data?.shop_closed_date?.some((item)=>dayjs__WEBPACK_IMPORTED_MODULE_0___default()(item.day).isSame(day.format("YYYY-MM-DD")));
    if (today) {
        const closedTime = foundedSchedule?.to.replace("-", ":");
        isTimeAfter = dayjs__WEBPACK_IMPORTED_MODULE_0___default()().isAfter(dayjs__WEBPACK_IMPORTED_MODULE_0___default()(`${date} ${closedTime}`));
    }
    const isDisabled = foundedSchedule?.disabled || isHoliday;
    return isDisabled || isTimeAfter;
}


/***/ }),

/***/ 60291:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "K": () => (/* binding */ getAddressFromLocation)
/* harmony export */ });
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(99648);
/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5848);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);
axios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];


async function getAddressFromLocation(latlng) {
    let params = {
        latlng,
        key: constants_constants__WEBPACK_IMPORTED_MODULE_1__/* .MAP_API_KEY */ .kr
    };
    return axios__WEBPACK_IMPORTED_MODULE_0__["default"].get(`https://maps.googleapis.com/maps/api/geocode/json`, {
        params
    }).then(({ data  })=>data.results[0]?.formatted_address).catch((error)=>{
        console.log(error);
        return "not found";
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;