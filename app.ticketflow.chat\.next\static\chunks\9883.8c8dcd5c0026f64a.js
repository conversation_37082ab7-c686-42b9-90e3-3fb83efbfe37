(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9883,6555],{89883:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return A}});var n=a(85893),r=a(67294),o=a(47567),l=a(86886),s=a(6734),i=a(90480),d=a.n(i),c=a(67899),u=a.n(c),v=a(94660),p=a(86555),m=a(4756),h=a.n(m),f=a(21697),_=a(30251),x=a(82175),g=a(24847),y=a.n(g),b=a(60291),j=a(1612),C=a(88767),M=a(29969),N=a(82027),k=a(73714),Z=a(80892),w=a(43274),S=a.n(w),L=a(80865),P=a(73476),B=a.n(P),z=a(21514),E=a.n(z),I=a(95114),T=a.n(I);let O=[{value:"home",icon:B(),translationKey:"home"},{value:"work",icon:E(),translationKey:"work"},{value:"other",icon:T(),translationKey:"other"}];function W(e){let{value:t,onChange:a,name:r="addressType"}=e,{t:o}=(0,s.$G)(),l=e=>{a(e)},i=e=>({checked:t===e,onChange:()=>l(e),value:e,id:"".concat(r,"-").concat(e),name:r,inputProps:{"aria-label":e}});return(0,n.jsxs)("div",{className:S().container,children:[(0,n.jsx)("h4",{className:S().title,children:o("address.type")}),(0,n.jsx)("div",{className:S().options,children:O.map(e=>{let a=e.icon;return(0,n.jsxs)("div",{className:"".concat(S().option," ").concat(t===e.value?S().selected:""),onClick:()=>l(e.value),children:[(0,n.jsx)(L.Z,{...i(e.value)}),(0,n.jsxs)("div",{className:S().content,children:[(0,n.jsx)("div",{className:S().iconWrapper,children:(0,n.jsx)(a,{size:20})}),(0,n.jsx)("span",{className:S().label,children:o(e.translationKey)})]})]},e.value)})})]})}function A(e){var t,a,i,c;let{address:m,latlng:g,editedAddress:w,onClearAddress:S,...L}=e,{t:P}=(0,s.$G)(),{user:B}=(0,M.a)(),{updateAddress:z,updateLocation:E,location_id:I,updateLocationId:T}=(0,f.r)(),[O,A]=(0,r.useState)({lat:Number(g.split(",")[0]),lng:Number(g.split(",")[1])}),F=(0,r.useRef)(),{isSuccess:G}=(0,C.useQuery)(["shopZones",O],()=>j.Z.checkZone({address:{latitude:O.lat,longitude:O.lng}})),K=(0,C.useQueryClient)(),{mutate:V,isLoading:Q}=(0,C.useMutation)({mutationFn:e=>N.Z.create(e)}),{mutate:q,isLoading:R}=(0,C.useMutation)({mutationFn:e=>N.Z.update((null==w?void 0:w.id)||0,e)}),{mutate:U,isLoading:D}=(0,C.useMutation)({mutationFn:e=>N.Z.delete(e),async onMutate(e){await K.cancelQueries("addresses");let t=K.getQueryData("addresses");return K.setQueryData("addresses",a=>a?a.flatMap(e=>e).filter(t=>t.id!==e):t),{prevAddresses:t}},onError(e,t,a){K.setQueryData("addresses",null==a?void 0:a.prevAddresses)},onSettled(){L.onClose&&L.onClose({},"backdropClick")}}),H=(0,x.TA)({initialValues:{entrance:null==w?void 0:null===(t=w.address)||void 0===t?void 0:t.entrance,floor:(null==w?void 0:null===(a=w.address)||void 0===a?void 0:a.floor)||"",apartment:(null==w?void 0:null===(i=w.address)||void 0===i?void 0:i.house)||"",comment:null==w?void 0:null===(c=w.address)||void 0===c?void 0:c.comment,title:null==w?void 0:w.title,type:(null==w?void 0:w.type)||"home"},onSubmit(e){!function(e){var t,a,n;if(w){q({title:e.title,type:e.type,location:[O.lat,O.lng],address:{address:(null===(t=F.current)||void 0===t?void 0:t.value)||"",floor:e.floor,house:e.apartment,entrance:e.entrance,comment:e.comment||""},active:w.active},{onSuccess(){(0,k.Vp)(P("successfully.updated")),K.invalidateQueries("addresses")},onError(){(0,k.vU)(P("unable.to.save"))},onSettled(){if(I===(null==w?void 0:w.id.toString())){var e;z(null===(e=F.current)||void 0===e?void 0:e.value),E("".concat(O.lat,",").concat(O.lng))}L.onClose&&L.onClose({},"backdropClick")}});return}B?V({title:e.title,type:e.type,location:[O.lat,O.lng],address:{address:(null===(a=F.current)||void 0===a?void 0:a.value)||"",floor:e.floor,house:e.apartment,entrance:e.entrance,comment:e.comment},active:1},{onSuccess(e){(0,k.Vp)(P("successfully.saved")),K.invalidateQueries("addresses"),T(e.id.toString())},onError(){(0,k.vU)(P("unable.to.save"))},onSettled(){var e;z(null===(e=F.current)||void 0===e?void 0:e.value),E("".concat(O.lat,",").concat(O.lng)),L.onClose&&L.onClose({},"backdropClick")}}):(z(null===(n=F.current)||void 0===n?void 0:n.value),E("".concat(O.lat,",").concat(O.lng)),L.onClose&&L.onClose({},"backdropClick"))}(e)},validate:()=>({})});async function $(e){let{coords:t}=e,a="".concat(t.latitude,",").concat(t.longitude),n=await (0,b.K)(a);F.current&&(F.current.value=n);let r={lat:t.latitude,lng:t.longitude};A(r)}return(0,n.jsx)(o.default,{...L,children:(0,n.jsxs)("div",{className:d().wrapper,children:[(0,n.jsxs)("div",{className:d().header,children:[(0,n.jsx)("h1",{className:d().title,children:P("enter.delivery.address")}),(0,n.jsxs)("div",{className:d().flex,children:[(0,n.jsxs)("div",{className:d().search,children:[(0,n.jsx)("label",{htmlFor:"search",children:(0,n.jsx)(u(),{})}),(0,n.jsx)("input",{type:"text",id:"search",name:"search",ref:F,placeholder:P("search"),autoComplete:"off",defaultValue:m})]}),(0,n.jsx)("div",{className:d().btnWrapper,children:(0,n.jsx)(v.Z,{onClick:function(){window.navigator.geolocation.getCurrentPosition($,console.log)},children:(0,n.jsx)(y(),{})})})]})]}),(0,n.jsx)("div",{className:d().body,children:(0,n.jsx)(p.default,{location:O,setLocation:A,inputRef:F})}),(0,n.jsx)("div",{className:d().form,children:(0,n.jsxs)(l.ZP,{container:!0,spacing:2,children:[(0,n.jsx)(l.ZP,{item:!0,xs:12,children:(0,n.jsx)(W,{value:H.values.type,onChange:e=>H.setFieldValue("type",e),name:"type"})}),(0,n.jsx)(l.ZP,{item:!0,xs:12,children:(0,n.jsx)(_.Z,{name:"title",label:P("title"),placeholder:P("type.here"),value:H.values.title,onChange:H.handleChange,error:!!H.errors.title&&!!H.touched.title})}),(0,n.jsx)(l.ZP,{item:!0,xs:4,children:(0,n.jsx)(_.Z,{name:"entrance",label:P("entrance"),placeholder:P("type.here"),value:H.values.entrance,onChange:H.handleChange})}),(0,n.jsx)(l.ZP,{item:!0,xs:4,children:(0,n.jsx)(_.Z,{name:"floor",label:P("floor"),placeholder:P("type.here"),value:H.values.floor,onChange:H.handleChange})}),(0,n.jsx)(l.ZP,{item:!0,xs:4,children:(0,n.jsx)(_.Z,{name:"apartment",label:P("apartment"),placeholder:P("type.here"),value:H.values.apartment,onChange:H.handleChange})}),(0,n.jsx)(l.ZP,{item:!0,xs:12,children:(0,n.jsx)(_.Z,{name:"comment",label:P("comment"),placeholder:P("type.here"),value:H.values.comment,onChange:H.handleChange})}),w&&I!==w.id.toString()&&(0,n.jsx)(l.ZP,{item:!0,xs:6,children:(0,n.jsx)(Z.Z,{type:"button",loading:D,onClick:()=>U(w.id),children:P("delete.address")})}),(0,n.jsx)(l.ZP,{item:!0,xs:w&&I!==w.id.toString()?6:12,children:(0,n.jsx)(v.Z,{type:"button",loading:Q||R,onClick(){var e;if(!(null===(e=F.current)||void 0===e?void 0:e.value))return(0,k.Kp)(P("enter.delivery.address"));H.submitForm()},disabled:!G,children:G?P("submit"):P("delivery.zone.not.available")})})]})}),(0,n.jsx)("div",{className:d().footer,children:(0,n.jsx)("button",{className:d().circleBtn,onClick(e){L.onClose&&L.onClose(e,"backdropClick")},children:(0,n.jsx)(h(),{})})})]})})}},30251:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var n=a(85893);a(67294);var r=a(90948),o=a(61903);let l=(0,r.ZP)(o.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function s(e){return(0,n.jsx)(l,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},86555:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return h}});var n=a(85893),r=a(67294),o=a(76725),l=a(9730),s=a.n(l),i=a(5848),d=a(60291),c=a(45122),u=a(90026);let v=e=>(0,n.jsx)("div",{className:s().point,children:(0,n.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),p=e=>(0,n.jsxs)("div",{className:s().floatCard,children:[(null==e?void 0:e.price)&&(0,n.jsx)("span",{className:s().price,children:(0,n.jsx)(u.Z,{number:e.price})}),(0,n.jsx)("div",{className:s().marker,children:(0,n.jsx)(c.Z,{data:e.shop,size:"small"})})]}),m={fields:["address_components","geometry"],types:["address"]};function h(e){var t,a;let{location:l,setLocation:c=()=>{},readOnly:u=!1,shop:h,inputRef:f,setAddress:_,price:x,drawLine:g,defaultZoom:y=15}=e,b=(0,r.useRef)(),[j,C]=(0,r.useState)(),[M,N]=(0,r.useState)();async function k(e){var t;if(u)return;let a={lat:e.center.lat(),lng:e.center.lng()};c(a);let n=await (0,d.K)("".concat(a.lat,",").concat(a.lng));(null==f?void 0:null===(t=f.current)||void 0===t?void 0:t.value)&&(f.current.value=n),_&&_(n)}let Z=(e,t)=>{if(f&&(b.current=new t.places.Autocomplete(f.current,m),b.current.addListener("place_changed",async function(){let e=await b.current.getPlace(),t=function(e){let t={street_number:"streetNumber",route:"streetName",sublocality_level_1:"city",locality:"city1",administrative_area_level_1:"state",postal_code:"postalCode",country:"country"},a={};e.address_components.forEach(e=>{a[t[e.types[0]]]=e.long_name});let n=[null==a?void 0:a.streetName,null==a?void 0:a.city1,null==a?void 0:a.country];return n.join(", ")}(e),a={lat:e.geometry.location.lat(),lng:e.geometry.location.lng()};c(a),_&&_(t)})),N(e),C(t),h){let a={lat:Number(null===(o=h.location)||void 0===o?void 0:o.latitude)||0,lng:Number(null===(s=h.location)||void 0===s?void 0:s.longitude)||0},n=[l,a],r=new t.LatLngBounds;for(var o,s,i=0;i<n.length;i++)r.extend(n[i]);e.fitBounds(r)}};return(0,r.useEffect)(()=>{if(h&&j){var e,t;let a={lat:Number(null===(e=h.location)||void 0===e?void 0:e.latitude)||0,lng:Number(null===(t=h.location)||void 0===t?void 0:t.longitude)||0},n=[l,a],r=new j.LatLngBounds;for(var o=0;o<n.length;o++)r.extend(n[o]);M.fitBounds(r)}},[l,null==h?void 0:h.location,g,M,j]),(0,n.jsxs)("div",{className:s().root,children:[!u&&(0,n.jsx)("div",{className:s().marker,children:(0,n.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),(0,n.jsxs)(o.ZP,{bootstrapURLKeys:{key:i.kr||"",libraries:["places"]},zoom:y,center:l,onDragEnd:k,yesIWantToUseGoogleMapApiInternals:!0,onGoogleApiLoaded(e){let{map:t,maps:a}=e;return Z(t,a)},options:{fullscreenControl:u},children:[u&&(0,n.jsx)(v,{lat:l.lat,lng:l.lng}),!!h&&(0,n.jsx)(p,{lat:(null===(t=h.location)||void 0===t?void 0:t.latitude)||0,lng:(null===(a=h.location)||void 0===a?void 0:a.longitude)||0,shop:h,price:x})]})]})}},90480:function(e){e.exports={wrapper:"addressModal_wrapper__wd8fr",header:"addressModal_header__NR1NL",title:"addressModal_title__cgd_V",flex:"addressModal_flex__r_MIU",search:"addressModal_search__gcs6f",btnWrapper:"addressModal_btnWrapper__xIPVy",body:"addressModal_body__VAc7I",form:"addressModal_form__lEtUl",footer:"addressModal_footer__VwwZM",circleBtn:"addressModal_circleBtn__Gf8_7",request:"addressModal_request__KdXvo",requestWrapper:"addressModal_requestWrapper__bxgG7",addressButton:"addressModal_addressButton__oTMD5",location:"addressModal_location__nknyf",addressTitle:"addressModal_addressTitle__x7P0a",address:"addressModal_address__AF16M",addressList:"addressModal_addressList__Evyu6",buttonActive:"addressModal_buttonActive__gNbbM"}},43274:function(e){e.exports={container:"addressTypeSelector_container__7jbCM",title:"addressTypeSelector_title__1td5o",options:"addressTypeSelector_options__niYR4",option:"addressTypeSelector_option__GtHbB",selected:"addressTypeSelector_selected__3IGOw",content:"addressTypeSelector_content__89Exs",iconWrapper:"addressTypeSelector_iconWrapper__8haOm",label:"addressTypeSelector_label__cen7J"}},9730:function(e){e.exports={root:"map_root__3qcrq",marker:"map_marker__EnBz1",floatCard:"map_floatCard__1zZP1",price:"map_price__CTP0I",point:"map_point__GfLMi"}},4756:function(e,t,a){"use strict";var n=a(67294),r=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},l=function(e,t){var a={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(a[n]=e[n]);return a},s=function(e){var t=e.color,a=e.size,n=void 0===a?24:a,s=(e.children,l(e,["color","size","children"])),i="remixicon-icon "+(s.className||"");return r.default.createElement("svg",o({},s,{className:i,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),r.default.createElement("path",{d:"M7.828 11H20v2H7.828l5.364 5.364-1.414 1.414L4 12l7.778-7.778 1.414 1.414z"}))},i=r.default.memo?r.default.memo(s):s;e.exports=i},24847:function(e,t,a){"use strict";var n=a(67294),r=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},l=function(e,t){var a={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(a[n]=e[n]);return a},s=function(e){var t=e.color,a=e.size,n=void 0===a?24:a,s=(e.children,l(e,["color","size","children"])),i="remixicon-icon "+(s.className||"");return r.default.createElement("svg",o({},s,{className:i,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),r.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-5-8.5L16 8l-3.5 9.002L11 13l-4-1.5z"}))},i=r.default.memo?r.default.memo(s):s;e.exports=i}}]);