(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8006],{21173:function(e,r,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/reset-password",function(){return s(91107)}])},30251:function(e,r,s){"use strict";s.d(r,{Z:function(){return o}});var t=s(85893);s(67294);var a=s(90948),i=s(61903);let n=(0,a.ZP)(i.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function o(e){return(0,t.jsx)(n,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},91107:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return y}});var t=s(85893),a=s(67294),i=s(84169),n=s(52259),o=s(20679),l=s.n(o),c=s(6734),d=s(30251),u=s(77262),m=s(82175),h=s(73714),p=s(41137),v=s(11163),f=s(29969);function x(e){let{onSuccess:r,changeView:s}=e,{t:a}=(0,c.$G)(),{push:i}=(0,v.useRouter)(),{phoneNumberSignIn:n}=(0,f.a)(),o=(0,m.TA)({initialValues:{email:""},onSubmit(e,t){var o,l;let{setSubmitting:c}=t;if(null===(o=e.email)||void 0===o?void 0:o.includes("@"))p.Z.forgotPasswordEmail(e).then(r=>{i({pathname:"/verify-phone",query:{email:e.email}}),(0,h.Vp)(r.message)}).catch(e=>(0,h.vU)(a(e.statusCode))).finally(()=>c(!1));else{let d=null===(l=e.email)||void 0===l?void 0:l.replace(/[^0-9]/g,"");n(d||"").then(e=>{s("VERIFY"),r({phone:d,callback:e})}).catch(()=>(0,h.vU)(a("sms.not.sent"))).finally(()=>c(!1))}},validate(e){var r,s,t;let i={};return e.email||(i.email=a("required")),(null===(r=e.email)||void 0===r?void 0:r.includes(" "))&&(i.email=a("should.not.includes.empty.space")),(null===(s=e.email)||void 0===s?void 0:s.includes("@"))?/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(e.email)||(i.email=a("should.be.valid")):/^998([378]{2}|(9[013-57-9]))\d{7}$/i.test((null===(t=e.email)||void 0===t?void 0:t.replace("+",""))||"")||(console.log("email"),i.email=a("should.be.valid")),i}});return(0,t.jsxs)("form",{className:l().wrapper,onSubmit:o.handleSubmit,children:[(0,t.jsxs)("div",{className:l().header,children:[(0,t.jsx)("h1",{className:l().title,children:a("reset.password")}),(0,t.jsx)("p",{className:l().text,children:a("reset.password.text")})]}),(0,t.jsx)("div",{className:l().space}),(0,t.jsx)(d.Z,{name:"email",label:a("email.or.phone"),placeholder:a("type.here"),value:o.values.email,onChange:o.handleChange,error:!!o.errors.email}),(0,t.jsx)("div",{className:l().space}),(0,t.jsx)("div",{className:l().action,children:(0,t.jsx)(u.Z,{id:"sign-in-button",type:"submit",loading:o.isSubmitting,children:a("send")})})]})}var _=s(40523),b=s.n(_),j=s(6952),w=s(20512),g=s(21697),N=s(10626);function S(e){var r;let{phone:s,callback:i,setCallback:n,verifyId:o,onSuccess:l}=e,{t:d}=(0,c.$G)(),{settings:x}=(0,g.r)(),_=60*x.otp_expire_time||60,[S,y,k,I]=(0,w.h)(_),{push:P}=(0,v.useRouter)(),{phoneNumberSignIn:Z,setUserData:F}=(0,f.a)(),E=(0,m.TA)({initialValues:{code:""},onSubmit(e,r){let{setSubmitting:t}=r;i.confirm(e.code||"").then(()=>{p.Z.forgotPasswordPhone({phone:s,type:"firebase"}).then(e=>{let{data:r}=e,s="Bearer "+r.token;(0,N.d8)("access_token",s),F(r.user),P("/update-password")}).catch(()=>(0,h.vU)(d("verify.error"))).finally(()=>t(!1))}).catch(()=>{(0,h.vU)(d("verify.error")),t(!1)})},validate(e){let r={};return e.code||(r.code=d("required")),r}});console.log("phone",s);let C=()=>{Z(s).then(e=>{I(),y(),(0,h.Vp)(d("verify.send")),n&&n(e)}).catch(()=>(0,h.vU)(d("sms.not.sent")))};return(0,a.useEffect)(()=>{y()},[y]),(0,t.jsxs)("form",{className:b().wrapper,onSubmit:E.handleSubmit,children:[(0,t.jsxs)("div",{className:b().header,children:[(0,t.jsx)("h1",{className:b().title,children:d("enter.otp.code")}),(0,t.jsx)("p",{className:b().text,children:d("enter.code.text",{phone:s})})]}),(0,t.jsx)("div",{className:b().space}),(0,t.jsx)(j.Z,{value:E.values.code,onChange:e=>E.setFieldValue("code",e),numInputs:6,isInputNum:!0,containerStyle:b().otpContainer,hasErrored:!!E.errors.code}),(0,t.jsxs)("p",{className:b().text,children:[d("verify.didntRecieveCode")," ",0===S?(0,t.jsx)("span",{id:"sign-in-button",onClick:C,className:b().resend,children:d("resend")}):(0,t.jsxs)("span",{className:b().text,children:[S," s"]})]}),(0,t.jsx)("div",{className:b().space}),(0,t.jsx)("div",{className:b().actions,children:(0,t.jsx)("div",{className:b().item,children:(0,t.jsx)(u.Z,{type:"submit",disabled:6>Number(null===(r=E.values.code)||void 0===r?void 0:r.length),loading:E.isSubmitting,children:d("confirm")})})})]})}function y(e){let{}=e,[r,s]=(0,a.useState)("RESET"),[o,l]=(0,a.useState)(()=>{}),[c,d]=(0,a.useState)(),[u,m]=(0,a.useState)(""),h=e=>s(e);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.Z,{}),(0,t.jsx)(n.Z,{children:(()=>{switch(r){case"RESET":return(0,t.jsx)(x,{changeView:h,onSuccess(e){let{phone:r,callback:s,verifyId:t}=e;m(r),l(s),d(t)}});case"VERIFY":return(0,t.jsx)(S,{changeView:h,phone:u,callback:o,setCallback:l,verifyId:c,onSuccess(e){let{phone:r,callback:s,verifyId:t}=e;m(r),l(s),d(t)}});default:return(0,t.jsx)(x,{changeView:h,onSuccess(e){let{phone:r,callback:s}=e;m(r),l(s)}})}})()})]})}},20679:function(e){e.exports={wrapper:"resetPasswordForm_wrapper__Ix1s2",header:"resetPasswordForm_header__Jbwnh",title:"resetPasswordForm_title__yp5I9",text:"resetPasswordForm_text__jjTbX",space:"resetPasswordForm_space__gJmGe",flex:"resetPasswordForm_flex__S7h4r",item:"resetPasswordForm_item__a4_ZO",label:"resetPasswordForm_label__idtr9",action:"resetPasswordForm_action__YoZjS"}}},function(e){e.O(0,[4564,2175,1903,6843,9774,2888,179],function(){return e(e.s=21173)}),_N_E=e.O()}]);