(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5650],{93946:function(e,t,r){"use strict";r.d(t,{Z:function(){return w}});var a=r(63366),o=r(87462),n=r(67294),i=r(86010),s=r(94780),l=r(41796),d=r(90948),c=r(71657),p=r(49990),u=r(98216),m=r(1588),h=r(34867);function v(e){return(0,h.Z)("MuiIconButton",e)}let f=(0,m.Z)("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge"]);var g=r(85893);let _=["edge","children","className","color","disabled","disableFocusRipple","size"],x=e=>{let{classes:t,disabled:r,color:a,edge:o,size:n}=e,i={root:["root",r&&"disabled","default"!==a&&`color${(0,u.Z)(a)}`,o&&`edge${(0,u.Z)(o)}`,`size${(0,u.Z)(n)}`]};return(0,s.Z)(i,v,t)},b=(0,d.ZP)(p.Z,{name:"MuiIconButton",slot:"Root",overridesResolver(e,t){let{ownerState:r}=e;return[t.root,"default"!==r.color&&t[`color${(0,u.Z)(r.color)}`],r.edge&&t[`edge${(0,u.Z)(r.edge)}`],t[`size${(0,u.Z)(r.size)}`]]}})(({theme:e,ownerState:t})=>(0,o.Z)({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",overflow:"visible",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest})},!t.disableRipple&&{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,l.Fq)(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}}},"start"===t.edge&&{marginLeft:"small"===t.size?-3:-12},"end"===t.edge&&{marginRight:"small"===t.size?-3:-12}),({theme:e,ownerState:t})=>{var r;let a=null==(r=(e.vars||e).palette)?void 0:r[t.color];return(0,o.Z)({},"inherit"===t.color&&{color:"inherit"},"inherit"!==t.color&&"default"!==t.color&&(0,o.Z)({color:null==a?void 0:a.main},!t.disableRipple&&{"&:hover":(0,o.Z)({},a&&{backgroundColor:e.vars?`rgba(${a.mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,l.Fq)(a.main,e.palette.action.hoverOpacity)},{"@media (hover: none)":{backgroundColor:"transparent"}})}),"small"===t.size&&{padding:5,fontSize:e.typography.pxToRem(18)},"large"===t.size&&{padding:12,fontSize:e.typography.pxToRem(28)},{[`&.${f.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled}})}),y=n.forwardRef(function(e,t){let r=(0,c.Z)({props:e,name:"MuiIconButton"}),{edge:n=!1,children:s,className:l,color:d="default",disabled:p=!1,disableFocusRipple:u=!1,size:m="medium"}=r,h=(0,a.Z)(r,_),v=(0,o.Z)({},r,{edge:n,color:d,disabled:p,disableFocusRipple:u,size:m}),f=x(v);return(0,g.jsx)(b,(0,o.Z)({className:(0,i.Z)(f.root,l),centerRipple:!0,focusRipple:!u,disabled:p,ref:t,ownerState:v},h,{children:s}))});var w=y},87109:function(e,t,r){"use strict";r.d(t,{Z:function(){return z}});var a,o=r(63366),n=r(87462),i=r(67294),s=r(86010),l=r(94780),d=r(98216),c=r(15861),p=r(47167),u=r(74423),m=r(90948),h=r(1588),v=r(34867);function f(e){return(0,v.Z)("MuiInputAdornment",e)}let g=(0,h.Z)("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]);var _=r(71657),x=r(85893);let b=["children","className","component","disablePointerEvents","disableTypography","position","variant"],y=(e,t)=>{let{ownerState:r}=e;return[t.root,t[`position${(0,d.Z)(r.position)}`],!0===r.disablePointerEvents&&t.disablePointerEvents,t[r.variant]]},w=e=>{let{classes:t,disablePointerEvents:r,hiddenLabel:a,position:o,size:n,variant:i}=e,s={root:["root",r&&"disablePointerEvents",o&&`position${(0,d.Z)(o)}`,i,a&&"hiddenLabel",n&&`size${(0,d.Z)(n)}`]};return(0,l.Z)(s,f,t)},j=(0,m.ZP)("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:y})(({theme:e,ownerState:t})=>(0,n.Z)({display:"flex",height:"0.01em",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(e.vars||e).palette.action.active},"filled"===t.variant&&{[`&.${g.positionStart}&:not(.${g.hiddenLabel})`]:{marginTop:16}},"start"===t.position&&{marginRight:8},"end"===t.position&&{marginLeft:8},!0===t.disablePointerEvents&&{pointerEvents:"none"})),Z=i.forwardRef(function(e,t){let r=(0,_.Z)({props:e,name:"MuiInputAdornment"}),{children:l,className:d,component:m="div",disablePointerEvents:h=!1,disableTypography:v=!1,position:f,variant:g}=r,y=(0,o.Z)(r,b),Z=(0,u.Z)()||{},z=g;g&&Z.variant,Z&&!z&&(z=Z.variant);let N=(0,n.Z)({},r,{hiddenLabel:Z.hiddenLabel,size:Z.size,disablePointerEvents:h,position:f,variant:z}),M=w(N);return(0,x.jsx)(p.Z.Provider,{value:null,children:(0,x.jsx)(j,(0,n.Z)({as:m,ownerState:N,className:(0,s.Z)(M.root,d),ref:t},y,{children:"string"!=typeof l||v?(0,x.jsxs)(i.Fragment,{children:["start"===f?a||(a=(0,x.jsx)("span",{className:"notranslate",children:"​"})):null,l]}):(0,x.jsx)(c.Z,{color:"text.secondary",children:l})}))})});var z=Z},15861:function(e,t,r){"use strict";r.d(t,{Z:function(){return j}});var a=r(63366),o=r(87462),n=r(67294),i=r(86010),s=r(39707),l=r(94780),d=r(90948),c=r(71657),p=r(98216),u=r(1588),m=r(34867);function h(e){return(0,m.Z)("MuiTypography",e)}(0,u.Z)("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);var v=r(85893);let f=["align","className","component","gutterBottom","noWrap","paragraph","variant","variantMapping"],g=e=>{let{align:t,gutterBottom:r,noWrap:a,paragraph:o,variant:n,classes:i}=e,s={root:["root",n,"inherit"!==e.align&&`align${(0,p.Z)(t)}`,r&&"gutterBottom",a&&"noWrap",o&&"paragraph"]};return(0,l.Z)(s,h,i)},_=(0,d.ZP)("span",{name:"MuiTypography",slot:"Root",overridesResolver(e,t){let{ownerState:r}=e;return[t.root,r.variant&&t[r.variant],"inherit"!==r.align&&t[`align${(0,p.Z)(r.align)}`],r.noWrap&&t.noWrap,r.gutterBottom&&t.gutterBottom,r.paragraph&&t.paragraph]}})(({theme:e,ownerState:t})=>(0,o.Z)({margin:0},t.variant&&e.typography[t.variant],"inherit"!==t.align&&{textAlign:t.align},t.noWrap&&{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},t.gutterBottom&&{marginBottom:"0.35em"},t.paragraph&&{marginBottom:16})),x={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},b={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},y=e=>b[e]||e,w=n.forwardRef(function(e,t){let r=(0,c.Z)({props:e,name:"MuiTypography"}),n=y(r.color),l=(0,s.Z)((0,o.Z)({},r,{color:n})),{align:d="inherit",className:p,component:u,gutterBottom:m=!1,noWrap:h=!1,paragraph:b=!1,variant:w="body1",variantMapping:j=x}=l,Z=(0,a.Z)(l,f),z=(0,o.Z)({},l,{align:d,color:n,className:p,component:u,gutterBottom:m,noWrap:h,paragraph:b,variant:w,variantMapping:j}),N=u||(b?"p":j[w]||x[w])||"span",M=g(z);return(0,v.jsx)(_,(0,o.Z)({as:N,ref:t,ownerState:z,className:(0,i.Z)(M.root,p)},Z))});var j=w},88416:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/update-password",function(){return r(85631)}])},32913:function(e,t,r){"use strict";r.d(t,{Z:function(){return h}});var a=r(85893),o=r(67294),n=r(90948),i=r(61903),s=r(87109),l=r(93946),d=r(25039),c=r.n(d),p=r(58773),u=r.n(p);let m=(0,n.ZP)(i.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function h(e){let[t,r]=(0,o.useState)(!1),n=()=>{r(e=>!e)};return(0,a.jsx)(m,{variant:"standard",type:t?"text":"password",InputLabelProps:{shrink:!0},InputProps:{endAdornment:(0,a.jsx)(s.Z,{position:"end",children:(0,a.jsx)(l.Z,{onClick:n,disableRipple:!0,children:t?(0,a.jsx)(u(),{}):(0,a.jsx)(c(),{})})})},...e})}},84169:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var a=r(85893);r(67294);var o=r(9008),n=r.n(o),i=r(5848),s=r(3075);function l(e){let{title:t,description:r=s.KM,image:o=s.T5,keywords:l=s.cU}=e,d=i.o6,c=t?t+" | "+s.k5:s.k5;return(0,a.jsxs)(n(),{children:[(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,a.jsx)("meta",{charSet:"utf-8"}),(0,a.jsx)("title",{children:c}),(0,a.jsx)("meta",{name:"description",content:r}),(0,a.jsx)("meta",{name:"keywords",content:l}),(0,a.jsx)("meta",{property:"og:type",content:"Website"}),(0,a.jsx)("meta",{name:"title",property:"og:title",content:c}),(0,a.jsx)("meta",{name:"description",property:"og:description",content:r}),(0,a.jsx)("meta",{name:"author",property:"og:author",content:d}),(0,a.jsx)("meta",{property:"og:site_name",content:d}),(0,a.jsx)("meta",{name:"image",property:"og:image",content:o}),(0,a.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,a.jsx)("meta",{name:"twitter:title",content:c}),(0,a.jsx)("meta",{name:"twitter:description",content:r}),(0,a.jsx)("meta",{name:"twitter:site",content:d}),(0,a.jsx)("meta",{name:"twitter:creator",content:d}),(0,a.jsx)("meta",{name:"twitter:image",content:o}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},52259:function(e,t,r){"use strict";r.d(t,{Z:function(){return f}});var a=r(85893),o=r(67294),n=r(6684),i=r(25675),s=r.n(i),l=r(4580),d=r.n(l),c=r(80108),p=r(41664),u=r.n(p),m=r(88767),h=r(49073),v=r(21697);function f(e){let{children:t}=e,{isDarkMode:r}=(0,o.useContext)(c.N),{updateSettings:i}=(0,v.r)();return(0,m.useQuery)("settings",()=>h.Z.getSettings(),{onSuccess(e){let t=function(e){let t=e.map(e=>({[e.key]:e.value}));return Object.assign({},...t)}(e.data);i({payment_type:t.payment_type,instagram_url:t.instagram,facebook_url:t.facebook,twitter_url:t.twitter,referral_active:t.referral_active,otp_expire_time:t.otp_expire_time,customer_app_android:t.customer_app_android,customer_app_ios:t.customer_app_ios,delivery_app_android:t.delivery_app_android,delivery_app_ios:t.delivery_app_ios,vendor_app_android:t.vendor_app_android,vendor_app_ios:t.vendor_app_ios,group_order:t.group_order,footer_text:t.footer_text,reservation_enable_for_user:t.reservation_enable_for_user})}}),(0,a.jsxs)("div",{className:d().container,children:[(0,a.jsx)("div",{className:d().authForm,children:(0,a.jsxs)("div",{className:d().formWrapper,children:[(0,a.jsx)("div",{className:d().header,children:(0,a.jsx)(u(),{href:"/",style:{display:"block"},children:r?(0,a.jsx)(n.$C,{}):(0,a.jsx)(n.Oc,{})})}),(0,a.jsx)("div",{className:d().body,children:t})]})}),(0,a.jsx)("div",{className:d().hero,children:(0,a.jsx)("div",{className:d().imgWrapper,children:(0,a.jsx)(s(),{fill:!0,src:"/images/welcome.jpg",alt:"Welcome to foodyman"})})})]})}},85631:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return f}});var a=r(85893);r(67294);var o=r(84169),n=r(52259),i=r(59407),s=r.n(i),l=r(6734),d=r(77262),c=r(82175),p=r(32913),u=r(45641),m=r(11163),h=r(73714);function v(e){let{}=e,{t}=(0,l.$G)(),{push:r}=(0,m.useRouter)(),o=(0,c.TA)({initialValues:{password:"",password_confirmation:""},onSubmit(e,a){let{setSubmitting:o}=a;u.Z.passwordUpdate(e).then(()=>{r("/")}).catch(e=>(0,h.vU)(t(e.message))).finally(()=>o(!1))},validate(e){let r={};return e.password||(r.password=t("required")),e.password_confirmation||(r.password_confirmation=t("required")),r}});return(0,a.jsxs)("form",{className:s().wrapper,onSubmit:o.handleSubmit,children:[(0,a.jsx)("div",{className:s().header,children:(0,a.jsx)("h1",{className:s().title,children:t("update.password")})}),(0,a.jsx)("div",{className:s().space}),(0,a.jsx)(p.Z,{name:"password",label:t("password"),placeholder:t("type.here"),value:o.values.password,onChange:o.handleChange}),(0,a.jsx)("div",{className:s().space}),(0,a.jsx)(p.Z,{name:"password_confirmation",label:t("password.confirmation"),placeholder:t("type.here"),value:o.values.password_confirmation,onChange:o.handleChange}),(0,a.jsx)("div",{className:s().space}),(0,a.jsx)("div",{className:s().action,children:(0,a.jsx)(d.Z,{type:"submit",loading:o.isSubmitting,children:t("submit")})})]})}function f(e){let{}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z,{}),(0,a.jsx)(n.Z,{children:(0,a.jsx)(v,{})})]})}},59407:function(e){e.exports={wrapper:"updatePasswordForm_wrapper__wEejj",header:"updatePasswordForm_header__wCmcv",title:"updatePasswordForm_title__YAhnd",text:"updatePasswordForm_text__8RbQD",space:"updatePasswordForm_space__grAXg",flex:"updatePasswordForm_flex__v4VkZ",item:"updatePasswordForm_item__B2Eb8",action:"updatePasswordForm_action__TOeIz"}},4580:function(e){e.exports={container:"auth_container__VKhNq",authForm:"auth_authForm__reJrL",formWrapper:"auth_formWrapper__VKjb4",header:"auth_header__JdGZq",body:"auth_body__rwKbX",hero:"auth_hero__W40NG",imgWrapper:"auth_imgWrapper__EtHM7"}},9008:function(e,t,r){e.exports=r(83121)},25039:function(e,t,r){"use strict";var a=r(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},i=function(e,t){var r={};for(var a in e)!(t.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a]);return r},s=function(e){var t=e.color,r=e.size,a=void 0===r?24:r,s=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return o.default.createElement("svg",n({},s,{className:l,width:a,height:a,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 3c5.392 0 9.878 3.88 10.819 9-.94 5.12-5.427 9-10.819 9-5.392 0-9.878-3.88-10.819-9C2.121 6.88 6.608 3 12 3zm0 16a9.005 9.005 0 0 0 8.777-7 9.005 9.005 0 0 0-17.554 0A9.005 9.005 0 0 0 12 19zm0-2.5a4.5 4.5 0 1 1 0-9 4.5 4.5 0 0 1 0 9zm0-2a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"}))},l=o.default.memo?o.default.memo(s):s;e.exports=l},58773:function(e,t,r){"use strict";var a=r(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},i=function(e,t){var r={};for(var a in e)!(t.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a]);return r},s=function(e){var t=e.color,r=e.size,a=void 0===r?24:r,s=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return o.default.createElement("svg",n({},s,{className:l,width:a,height:a,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M17.882 19.297A10.949 10.949 0 0 1 12 21c-5.392 0-9.878-3.88-10.819-9a10.982 10.982 0 0 1 3.34-6.066L1.392 2.808l1.415-1.415 19.799 19.8-1.415 1.414-3.31-3.31zM5.935 7.35A8.965 8.965 0 0 0 3.223 12a9.005 9.005 0 0 0 13.201 5.838l-2.028-2.028A4.5 4.5 0 0 1 8.19 9.604L5.935 7.35zm6.979 6.978l-3.242-3.242a2.5 2.5 0 0 0 3.241 3.241zm7.893 2.264l-1.431-1.43A8.935 8.935 0 0 0 20.777 12 9.005 9.005 0 0 0 9.552 5.338L7.974 3.76C9.221 3.27 10.58 3 12 3c5.392 0 9.878 3.88 10.819 9a10.947 10.947 0 0 1-2.012 4.592zm-9.084-9.084a4.5 4.5 0 0 1 4.769 4.769l-4.77-4.769z"}))},l=o.default.memo?o.default.memo(s):s;e.exports=l}},function(e){e.O(0,[4564,2175,1903,9774,2888,179],function(){return e(e.s=88416)}),_N_E=e.O()}]);