(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7885,520],{20520:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return d}});var r=n(85893);n(67294);var a=n(91491),i=n.n(a),o=n(77262),l=n(11163),s=n(6734);function d(e){let{text:t,buttonText:n,link:a="/"}=e,{push:d}=(0,l.useRouter)(),{t:u}=(0,s.$G)();return(0,r.jsx)("div",{className:"container",children:(0,r.jsxs)("div",{className:i().wrapper,children:[(0,r.jsx)("img",{src:"/images/delivery.webp",alt:u("empty")}),(0,r.jsx)("p",{className:i().text,children:t}),!!n&&(0,r.jsx)("div",{className:i().actions,children:(0,r.jsx)(o.Z,{onClick:()=>d(a),children:n})})]})})}},17885:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return R}});var r=n(85893),a=n(67294),i=n(77748),o=n.n(i),l=n(56457),s=n(88767),d=n(6734),u=n(11163),c=n(5152),p=n.n(c),v=n(84169),f=n(95785),g=n(1612),b=n(97334),y=n.n(b),m=n(34349),h=n(5215),x=n(37935),j=n(20520),_=n(98396),w=n(2950);let C=p()(()=>Promise.all([n.e(719),n.e(6186)]).then(n.bind(n,56186)),{loadableGenerated:{webpack:()=>[56186]}}),k=p()(()=>Promise.all([n.e(6886),n.e(9829)]).then(n.bind(n,59829)),{loadableGenerated:{webpack:()=>[59829]}}),I=p()(()=>Promise.all([n.e(6694),n.e(6519)]).then(n.bind(n,48709)),{loadableGenerated:{webpack:()=>[48709]}}),N=p()(()=>n.e(8253).then(n.bind(n,28253)),{loadableGenerated:{webpack:()=>[28253]}});function R(){var e,t,n,i,c,p,b;let{t:R,i18n:O}=(0,d.$G)(),A=O.language,P=(0,a.useRef)(null),{query:U}=(0,u.useRouter)(),Z=(0,w.Z)(),G=(0,_.Z)("(min-width:1140px)"),{newest:S,order_by:E,group:q}=(0,m.C)(h.qs),{data:K,isLoading:L}=(0,s.useQuery)(["category",U.id,A],()=>l.Z.getById(String(U.id),{active:1})),{data:M,isLoading:Q,isFetchingNextPage:$,fetchNextPage:B,hasNextPage:F}=(0,s.useInfiniteQuery)(["shops",A,null==K?void 0:K.data.id,E,q,Z,S,null==U?void 0:U.sub],e=>{var t;let{pageParam:n=1}=e;return g.Z.getAllShops(y().stringify({page:n,category_id:(null==U?void 0:U.sub)||(null==K?void 0:K.data.id),order_by:S?"new":E,free_delivery:q.free_delivery,take:q.tag,rating:null===(t=q.rating)||void 0===t?void 0:t.split(","),prices:q.prices,address:Z,open:Number(q.open)||void 0,deals:q.deals}))},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1}}),H=(null==M?void 0:null===(e=M.pages)||void 0===e?void 0:e.flatMap(e=>e.data))||[],T=(0,a.useCallback)(e=>{let t=e[0];t.isIntersecting&&F&&B()},[B,F]);return(0,a.useEffect)(()=>{let e=new IntersectionObserver(T,{root:null,rootMargin:"20px",threshold:0});P.current&&e.observe(P.current)},[T,F,B]),(0,r.jsxs)("section",{className:o().container,children:[(0,r.jsx)(v.Z,{title:null==K?void 0:null===(t=K.data)||void 0===t?void 0:null===(n=t.translation)||void 0===n?void 0:n.title,description:null==K?void 0:null===(i=K.data)||void 0===i?void 0:null===(c=i.translation)||void 0===c?void 0:c.description,image:(0,f.Z)(null==K?void 0:null===(p=K.data)||void 0===p?void 0:p.img)}),(0,r.jsx)(C,{data:(null==K?void 0:K.data.children)||[],loading:L,parent:String(U.id)}),G?(0,r.jsx)(I,{data:null==K?void 0:K.data,hideCategories:!0}):(0,r.jsx)(N,{data:null==K?void 0:K.data,hideCategories:!0}),Q||0!==H.length?(0,r.jsx)(k,{shops:H,title:null===(b=null==K?void 0:K.data.translation)||void 0===b?void 0:b.title,loading:Q}):(0,r.jsx)(j.default,{text:R("there.is.no.shops")}),$&&(0,r.jsx)(x.default,{}),(0,r.jsx)("div",{ref:P})]})}},91491:function(e){e.exports={wrapper:"empty_wrapper__nwTin",text:"empty_text__oRHIv",actions:"empty_actions__NNcWA"}},77748:function(e){e.exports={container:"v4_container__nq0i1"}},97334:function(e){!function(){"use strict";var t,n={815:function(e){e.exports=function(e,n,r,a){n=n||"&",r=r||"=";var i={};if("string"!=typeof e||0===e.length)return i;var o=/\+/g;e=e.split(n);var l=1e3;a&&"number"==typeof a.maxKeys&&(l=a.maxKeys);var s=e.length;l>0&&s>l&&(s=l);for(var d=0;d<s;++d){var u,c,p,v,f=e[d].replace(o,"%20"),g=f.indexOf(r);(g>=0?(u=f.substr(0,g),c=f.substr(g+1)):(u=f,c=""),p=decodeURIComponent(u),v=decodeURIComponent(c),Object.prototype.hasOwnProperty.call(i,p))?t(i[p])?i[p].push(v):i[p]=[i[p],v]:i[p]=v}return i};var t=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},577:function(e){var t=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,i,o,l){return(i=i||"&",o=o||"=",null===e&&(e=void 0),"object"==typeof e)?r(a(e),function(a){var l=encodeURIComponent(t(a))+o;return n(e[a])?r(e[a],function(e){return l+encodeURIComponent(t(e))}).join(i):l+encodeURIComponent(t(e[a]))}).join(i):l?encodeURIComponent(t(l))+o+encodeURIComponent(t(e)):""};var n=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function r(e,t){if(e.map)return e.map(t);for(var n=[],r=0;r<e.length;r++)n.push(t(e[r],r));return n}var a=Object.keys||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t}}},r={};function a(e){var t=r[e];if(void 0!==t)return t.exports;var i=r[e]={exports:{}},o=!0;try{n[e](i,i.exports,a),o=!1}finally{o&&delete r[e]}return i.exports}a.ab="//";var i={};(t=i).decode=t.parse=a(815),t.encode=t.stringify=a(577),e.exports=i}()}}]);