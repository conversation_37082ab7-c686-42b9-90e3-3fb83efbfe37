(() => {
var exports = {};
exports.id = 3419;
exports.ids = [3419];
exports.modules = {

/***/ 41587:
/***/ ((module) => {

// Exports
module.exports = {
	"story": "parcelFeatureSingle_story__WQimw",
	"wrapper": "parcelFeatureSingle_wrapper___8CZo",
	"title": "parcelFeatureSingle_title__hw1OX",
	"logo": "parcelFeatureSingle_logo__fTYiO",
	"logoWrapper": "parcelFeatureSingle_logoWrapper__LFDhI",
	"shopTitle": "parcelFeatureSingle_shopTitle__h1TMt"
};


/***/ }),

/***/ 15464:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "parcelForm_wrapper__pYyWp",
	"header": "parcelForm_header__k8amG",
	"title": "parcelForm_title__xE6qU",
	"rowBtn": "parcelForm_rowBtn__iqCkQ",
	"item": "parcelForm_item__bFVUO",
	"naming": "parcelForm_naming__UNlIW",
	"label": "parcelForm_label__IvlSs",
	"value": "parcelForm_value__wLaD_",
	"icon": "parcelForm_icon__p3DPV",
	"switch": "parcelForm_switch__hA9JT",
	"optionItemWrapper": "parcelForm_optionItemWrapper__RwL0S",
	"optionItem": "parcelForm_optionItem__4tmEj",
	"active": "parcelForm_active__Feh1C",
	"spacing": "parcelForm_spacing__d5zvb",
	"map": "parcelForm_map__e2BKo",
	"sticky": "parcelForm_sticky__rMMxp"
};


/***/ }),

/***/ 7863:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "parcelShow_wrapper__UsOmj",
	"title": "parcelShow_title__YiihY",
	"flex": "parcelShow_flex__NfbqK",
	"aside": "parcelShow_aside__j_J8F",
	"imageWrapper": "parcelShow_imageWrapper__3aHKu",
	"main": "parcelShow_main__jfz4X",
	"body": "parcelShow_body__wjQc8",
	"rowItem": "parcelShow_rowItem__4QrlA"
};


/***/ }),

/***/ 73747:
/***/ ((module) => {

// Exports
module.exports = {
	"root": "parcelCheckout_root__rLcfp",
	"container": "parcelCheckout_container__Kt_jD",
	"header": "parcelCheckout_header__S8BQz",
	"title": "parcelCheckout_title__bRiKk",
	"wrapper": "parcelCheckout_wrapper___9Mzc",
	"alert": "parcelCheckout_alert__F5txB"
};


/***/ }),

/***/ 87939:
/***/ ((module) => {

// Exports
module.exports = {
	"title": "parcelFeatureList_title__LQEfy",
	"storyContainer": "parcelFeatureList_storyContainer__dYj7Z"
};


/***/ }),

/***/ 3058:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ParcelFeatureSingle)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(41664);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _parcelFeatureSingle_module_scss__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(41587);
/* harmony import */ var _parcelFeatureSingle_module_scss__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_parcelFeatureSingle_module_scss__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(37490);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(37562);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(57987);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_5__, react_i18next__WEBPACK_IMPORTED_MODULE_6__]);
([components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_5__, react_i18next__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);








const StoryContainer = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(null, {
    loadableGenerated: {
        modules: [
            "..\\components\\parcelFeaturesingle\\parcelFeatureSingle.tsx -> " + "components/parcelFeatureContainer/parcelFeatureContainer"
        ]
    },
    ssr: false
});
function ParcelFeatureSingle({ data , list  }) {
    const [open, handleOpen, handleClose] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)();
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();
    const firstStory = data[0];
    const filteredList = list.filter((item)=>item[0]?.id !== firstStory?.id);
    const goToStory = (event)=>{
        event.preventDefault();
        handleOpen();
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                href: "/",
                className: (_parcelFeatureSingle_module_scss__WEBPACK_IMPORTED_MODULE_7___default().story),
                onClick: goToStory,
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: (_parcelFeatureSingle_module_scss__WEBPACK_IMPORTED_MODULE_7___default().wrapper),
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                            className: (_parcelFeatureSingle_module_scss__WEBPACK_IMPORTED_MODULE_7___default().title),
                            children: t(firstStory.title)
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
                            fill: true,
                            src: firstStory.img,
                            alt: firstStory.title,
                            sizes: "130px",
                            quality: 90,
                            priority: true
                        })
                    ]
                })
            }),
            open && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(StoryContainer, {
                open: open,
                onClose: handleClose,
                stories: [
                    data,
                    ...filteredList
                ]
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 28922:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ParcelForm)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(18074);
/* harmony import */ var components_pickers_rcSelect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(68554);
/* harmony import */ var utils_getTimeSlots__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(17662);
/* harmony import */ var components_pickers_rcParcelPicker__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(98916);
/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6684);
/* harmony import */ var remixicon_react_TimeLineIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(2923);
/* harmony import */ var remixicon_react_TimeLineIcon__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_TimeLineIcon__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var components_pickers_rcDateTimePicker__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(2310);
/* harmony import */ var remixicon_react_BankCardLineIcon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(10931);
/* harmony import */ var remixicon_react_BankCardLineIcon__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_BankCardLineIcon__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var components_pickers_rcAddressPicker__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(43661);
/* harmony import */ var remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(99893);
/* harmony import */ var remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_12__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__, components_pickers_rcSelect__WEBPACK_IMPORTED_MODULE_4__, components_pickers_rcParcelPicker__WEBPACK_IMPORTED_MODULE_6__, components_icons__WEBPACK_IMPORTED_MODULE_7__, components_pickers_rcDateTimePicker__WEBPACK_IMPORTED_MODULE_9__, components_pickers_rcAddressPicker__WEBPACK_IMPORTED_MODULE_11__]);
([hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__, components_pickers_rcSelect__WEBPACK_IMPORTED_MODULE_4__, components_pickers_rcParcelPicker__WEBPACK_IMPORTED_MODULE_6__, components_icons__WEBPACK_IMPORTED_MODULE_7__, components_pickers_rcDateTimePicker__WEBPACK_IMPORTED_MODULE_9__, components_pickers_rcAddressPicker__WEBPACK_IMPORTED_MODULE_11__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);













function ParcelForm({ formik , types , loading , payments , handleSelectType  }) {
    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)();
    const { location_from , location_to , type_id , delivery_date , delivery_time , address_from , address_to  } = formik.values;
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
        container: true,
        spacing: 3,
        columns: {
            xs: 12,
            md: 12,
            lg: 15
        },
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                item: true,
                xs: 12,
                lg: 3,
                md: 6,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_pickers_rcAddressPicker__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
                    formik: formik,
                    address: address_from,
                    location: location_from,
                    locationKey: "location_from",
                    addressKey: "address_from",
                    icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_7__/* .PickupFromIcon */ .iR, {}),
                    label: t("pickup.from"),
                    type: "outlined"
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                item: true,
                xs: 12,
                lg: 3,
                md: 6,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_pickers_rcAddressPicker__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
                    formik: formik,
                    address: address_to,
                    location: location_to,
                    label: t("delivery.to"),
                    locationKey: "location_to",
                    addressKey: "address_to",
                    icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_12___default()), {}),
                    type: "outlined"
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                item: true,
                xs: 12,
                lg: 3,
                md: 4,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_pickers_rcParcelPicker__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                    type: "outlined",
                    name: "type_id",
                    icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_7__/* .BoxLineIcon */ .el, {}),
                    label: t("type"),
                    value: type_id,
                    options: types,
                    onChange: (event)=>{
                        const findedType = types.find((type)=>type.value.toString() === event.target.value)?.data;
                        if (findedType) {
                            handleSelectType(findedType);
                        }
                        formik.handleChange(event);
                    },
                    error: !!formik.errors.type_id && formik.touched.type_id
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                item: true,
                xs: 12,
                lg: 3,
                md: 4,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_pickers_rcDateTimePicker__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                    type: "outlined",
                    name: "delivery_time",
                    label: t("delivery.date"),
                    icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_TimeLineIcon__WEBPACK_IMPORTED_MODULE_8___default()), {}),
                    date: delivery_date,
                    onDateChange: (event)=>{
                        formik.setFieldValue("delivery_date", event);
                    },
                    error: !!formik.errors.delivery_date && formik.touched.delivery_date,
                    time: delivery_time,
                    options: (0,utils_getTimeSlots__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .ZP)("06:00", "23:00", false, 60).map((el)=>({
                            label: el,
                            value: el
                        })),
                    onTimeChange: (event)=>formik.handleChange(event)
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                item: true,
                xs: 12,
                lg: 3,
                md: 4,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_pickers_rcSelect__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
                    type: "outlined",
                    icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_BankCardLineIcon__WEBPACK_IMPORTED_MODULE_10___default()), {}),
                    name: "payment_type_id",
                    label: t("payment.type"),
                    value: formik.values.payment_type_id,
                    options: payments,
                    onChange: (event)=>formik.handleChange(event),
                    error: !!formik.errors.payment_type_id && formik.touched.payment_type_id
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 47274:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ParcelReceiverForm)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(15464);
/* harmony import */ var _parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(30251);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(18074);
/* harmony import */ var components_button_primaryButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(77262);
/* harmony import */ var components_price_price__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(90026);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var services_parcel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(47763);
/* harmony import */ var hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(34349);
/* harmony import */ var redux_slices_currency__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(64698);
/* harmony import */ var remixicon_react_PencilFillIcon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(81330);
/* harmony import */ var remixicon_react_PencilFillIcon__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_PencilFillIcon__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(99893);
/* harmony import */ var remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(37490);
/* harmony import */ var components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(27914);
/* harmony import */ var components_inputs_switchInput__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(91662);
/* harmony import */ var components_inputs_textArea__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(24285);
/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(53015);
/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(21697);
/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(74621);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__, services_parcel__WEBPACK_IMPORTED_MODULE_8__, components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_14__, swiper_react__WEBPACK_IMPORTED_MODULE_17__, components_alert_toast__WEBPACK_IMPORTED_MODULE_19__]);
([hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__, services_parcel__WEBPACK_IMPORTED_MODULE_8__, components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_14__, swiper_react__WEBPACK_IMPORTED_MODULE_17__, components_alert_toast__WEBPACK_IMPORTED_MODULE_19__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);





















function ParcelReceiverForm({ formik , loading , selectedType  }) {
    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)();
    const currency = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__/* .useAppSelector */ .C)(redux_slices_currency__WEBPACK_IMPORTED_MODULE_10__/* .selectCurrency */ .j);
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)("(min-width:1140px)");
    const { username_to , phone_to , address_to , location_to , location_from , type_id , notify , description  } = formik.values;
    const [addressModal, handleOpenAddressModal, handleCloseAddressModal] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z)();
    const { location  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_18__/* .useSettings */ .r)();
    const defaultLocation = {
        latitude: location?.split(",")[0],
        longitude: location?.split(",")[1]
    };
    const { data: price , isLoading , isError  } = (0,react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)([
        "calculateParcel",
        location_from,
        location_to,
        type_id,
        currency
    ], ()=>services_parcel__WEBPACK_IMPORTED_MODULE_8__/* ["default"].calculate */ .Z.calculate({
            address_from: location_from,
            address_to: location_to,
            type_id,
            currency_id: currency?.id
        }), {
        enabled: Boolean(type_id),
        select: (data)=>data.data.price,
        onError: (e)=>{
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_19__/* .error */ .vU)(e?.data?.message);
        }
    });
    const handleAddToDescription = (value)=>{
        const oldDescription = description;
        if (!oldDescription || oldDescription?.trim().length === 0) {
            formik.setFieldValue("description", value);
            return;
        }
        formik.setFieldValue("description", `${oldDescription}, ${value}`);
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                container: true,
                spacing: 4,
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 8,
                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                            type: "button",
                            className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().rowBtn),
                            onClick: handleOpenAddressModal,
                            children: [
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().item),
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_12___default()), {}),
                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                            className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().naming),
                                            children: [
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().label),
                                                    children: t("address")
                                                }),
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().value),
                                                    children: address_to
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().icon),
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_PencilFillIcon__WEBPACK_IMPORTED_MODULE_11___default()), {})
                                })
                            ]
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "phone_to",
                            label: t("phone"),
                            value: phone_to,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.phone_to && formik.touched.phone_to
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "username_to",
                            label: t("username"),
                            value: username_to,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.username_to && formik.touched.username_to
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "house_to",
                            label: t("house"),
                            value: formik.values.house_to,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.house_to && formik.touched.house_to
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "stage_to",
                            label: t("stage"),
                            value: formik.values.stage_to,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.stage_to && formik.touched.stage_to
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "room_to",
                            label: t("room"),
                            value: formik.values.room_to,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.room_to && formik.touched.room_to
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "instructions",
                            label: t("add.instructions"),
                            value: formik.values.instructions,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.instructions && formik.touched.instructions
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 8,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textArea__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
                            label: t("item.description"),
                            name: "description",
                            multiline: true,
                            sx: {
                                ".MuiInput-root": {
                                    marginTop: "2rem"
                                }
                            },
                            value: formik.values.description,
                            onChange: formik.handleChange,
                            placeholder: t("what.are.you.sending"),
                            error: !!formik.errors.description && formik.touched.description
                        })
                    }),
                    selectedType?.options?.length !== 0 && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(swiper_react__WEBPACK_IMPORTED_MODULE_17__.Swiper, {
                            spaceBetween: 10,
                            slidesPerView: "auto",
                            children: selectedType?.options?.map((option)=>{
                                const stringContains = description?.includes(option.translation?.title || "");
                                return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(swiper_react__WEBPACK_IMPORTED_MODULE_17__.SwiperSlide, {
                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().optionItemWrapper),
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                                        type: "button",
                                        onClick: ()=>handleAddToDescription(option.translation?.title),
                                        disabled: stringContains,
                                        className: `${(_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().optionItem)} ${stringContains ? (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().active) : ""}`,
                                        children: option.translation?.title
                                    })
                                }, option.id);
                            })
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 8,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textArea__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
                            name: "qr_value",
                            multiline: true,
                            value: formik.values.qr_value,
                            onChange: formik.handleChange,
                            placeholder: t("item.value.qr"),
                            error: !!formik.errors.qr_value && formik.touched.qr_value
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 8,
                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().rowBtn),
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().item),
                                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                        className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().naming),
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                                className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().value),
                                                children: t("remain.anonymus")
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                                className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().label),
                                                children: t("dont.notify.a.recipient")
                                            })
                                        ]
                                    })
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default()["switch"]),
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_switchInput__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
                                            name: "notify",
                                            checked: formik.values.notify,
                                            onChange: (e)=>formik.setFieldValue("notify", e.target.checked)
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                            className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_20___default().value),
                                            children: notify ? t("on") : t("off")
                                        })
                                    ]
                                })
                            ]
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 8,
                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(components_button_primaryButton__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
                            type: "submit",
                            disabled: !price || isError,
                            loading: isLoading || loading,
                            children: [
                                t("pay"),
                                " ",
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_price_price__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                                    number: price
                                })
                            ]
                        })
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
                address: address_to,
                addressKey: "address_to",
                locationKey: "location_to",
                formik: formik,
                checkZone: false,
                open: addressModal,
                onClose: handleCloseAddressModal,
                latlng: location_to || defaultLocation,
                fullScreen: !isDesktop,
                title: "select.address"
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 40658:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ParcelSenderForm)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(15464);
/* harmony import */ var _parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(30251);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(18074);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(37490);
/* harmony import */ var components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(27914);
/* harmony import */ var remixicon_react_PencilFillIcon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(81330);
/* harmony import */ var remixicon_react_PencilFillIcon__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_PencilFillIcon__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(6684);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__, components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_6__, components_icons__WEBPACK_IMPORTED_MODULE_8__]);
([hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__, components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_6__, components_icons__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);










function ParcelSenderForm({ formik  }) {
    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)();
    const { username_from , phone_from , address_from , location_from  } = formik.values;
    const [addressModal, handleOpenAddressModal, handleCloseAddressModal] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)();
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)("(min-width:1140px)");
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                container: true,
                spacing: 4,
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 8,
                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                            type: "button",
                            className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_9___default().rowBtn),
                            onClick: handleOpenAddressModal,
                            children: [
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_9___default().item),
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_8__/* .PickupFromIcon */ .iR, {}),
                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                            className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_9___default().naming),
                                            children: [
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_9___default().label),
                                                    children: t("address")
                                                }),
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_9___default().value),
                                                    children: address_from
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                    className: (_parcelForm_module_scss__WEBPACK_IMPORTED_MODULE_9___default().icon),
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_PencilFillIcon__WEBPACK_IMPORTED_MODULE_7___default()), {})
                                })
                            ]
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "phone_from",
                            label: t("phone"),
                            value: phone_from,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.phone_from && formik.touched.phone_from
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "username_from",
                            label: t("username"),
                            value: username_from,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.username_from && formik.touched.username_from
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "house_from",
                            label: t("house"),
                            value: formik.values.house_from,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.house_from && formik.touched.house_from
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "stage_from",
                            label: t("stage"),
                            value: formik.values.stage_from,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.stage_from && formik.touched.stage_from
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        md: 5,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "room_from",
                            label: t("room"),
                            value: formik.values.room_from,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.room_from && formik.touched.room_from
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                        item: true,
                        xs: 12,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_textInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            name: "note",
                            label: t("comment"),
                            value: formik.values.note,
                            onChange: formik.handleChange,
                            placeholder: t("type.here"),
                            error: !!formik.errors.note && formik.touched.note
                        })
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                address: address_from,
                addressKey: "address_from",
                locationKey: "location_from",
                formik: formik,
                checkZone: false,
                open: addressModal,
                onClose: handleCloseAddressModal,
                latlng: location_from,
                fullScreen: !isDesktop,
                title: "select.address"
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 1125:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ParcelShow)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7863);
/* harmony import */ var _parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(37562);
/* harmony import */ var utils_getImage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(95785);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(18074);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__]);
([components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);






function ParcelShow({ data  }) {
    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)();
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().wrapper),
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h1", {
                className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().title),
                children: data?.type
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().flex),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("aside", {
                        className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().aside),
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().imageWrapper),
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                                fill: true,
                                src: (0,utils_getImage__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)(data?.img),
                                alt: data?.type,
                                sizes: "320px",
                                quality: 90
                            })
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("main", {
                        className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().main),
                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().body),
                            children: [
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().rowItem),
                                    children: [
                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("strong", {
                                            children: [
                                                t("weight"),
                                                ": "
                                            ]
                                        }),
                                        t("up.to.weight", {
                                            number: Number(data?.max_g) / 1000
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().rowItem),
                                    children: [
                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("strong", {
                                            children: [
                                                t("length"),
                                                ": "
                                            ]
                                        }),
                                        t("up.to.length", {
                                            number: Number(data?.max_length) / 100
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().rowItem),
                                    children: [
                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("strong", {
                                            children: [
                                                t("height"),
                                                ": "
                                            ]
                                        }),
                                        t("up.to.length", {
                                            number: Number(data?.max_height) / 100
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    className: (_parcelShow_module_scss__WEBPACK_IMPORTED_MODULE_4___default().rowItem),
                                    children: [
                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("strong", {
                                            children: [
                                                t("width"),
                                                ": "
                                            ]
                                        }),
                                        t("up.to.length", {
                                            number: Number(data?.max_width) / 100
                                        })
                                    ]
                                })
                            ]
                        })
                    })
                ]
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 43661:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ RcAddressPicker)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _pickers_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(88297);
/* harmony import */ var _pickers_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(44237);
/* harmony import */ var remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(37490);
/* harmony import */ var components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(27914);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_5__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_4__]);
components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];







function RcAddressPicker({ address , location , locationKey , addressKey , formik , label , error , type ="outlined" , placeholder , icon  }) {
    const [open, handleOpen, handleClose] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)();
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_5__.useMediaQuery)("(min-width:1140px)");
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: `${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_6___default().container)} ${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_6___default())[type]}`,
        children: [
            !!label && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),
                children: label
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: `${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper)} ${error ? (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_6___default().error) : ""}`,
                onClick: handleOpen,
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: `${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_6___default().iconWrapper)} ${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_6___default().limited)}`,
                        children: [
                            icon,
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_6___default().text),
                                children: address ? address : placeholder
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_2___default()), {})
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_addressModal_deliveryAddressModal__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
                open: open,
                checkZone: false,
                onClose: handleClose,
                addressKey: addressKey,
                locationKey: locationKey,
                address: address,
                formik: formik,
                latlng: location,
                fullScreen: !isDesktop
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 2310:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ RcDateTimePicker)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _pickers_module_scss__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(88297);
/* harmony import */ var _pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(44237);
/* harmony import */ var remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1635);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var hooks_usePopover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(58287);
/* harmony import */ var containers_popover_popover__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(56060);
/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(10298);
/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(85753);
/* harmony import */ var _mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var components_inputs_staticDatepicker__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(80956);
/* harmony import */ var components_inputs_radioInput__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(80865);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(57987);
/* harmony import */ var utils_formatBrazilianDate__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(86701);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_10__]);
react_i18next__WEBPACK_IMPORTED_MODULE_10__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];













function RcDateTimePicker({ name , date , time , onDateChange , onTimeChange , label , error , type ="outlined" , placeholder , icon , options  }) {
    const [open, anchor, handleOpen, handleClose] = (0,hooks_usePopover__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)();
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_10__.useTranslation)();
    const controlProps = (item)=>({
            checked: String(time) === item,
            onChange: (e)=>{
                onTimeChange(e);
                handleClose();
            },
            value: item,
            id: item,
            name,
            inputProps: {
                "aria-label": item
            }
        });
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: `${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().container)} ${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default())[type]}`,
        children: [
            !!label && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().title),
                children: label
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: `${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().wrapper)} ${error ? (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().error) : ""}`,
                onClick: handleOpen,
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().iconWrapper),
                        children: [
                            icon,
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                                className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text),
                                children: [
                                    date ? (0,utils_formatBrazilianDate__WEBPACK_IMPORTED_MODULE_11__/* .formatBrazilianDate */ .yG)(date, false) : "",
                                    " ",
                                    time
                                ]
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_2___default()), {})
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_popover_popover__WEBPACK_IMPORTED_MODULE_5__["default"], {
                open: open,
                anchorEl: anchor,
                onClose: handleClose,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_7__.LocalizationProvider, {
                    dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_6__.AdapterDayjs,
                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().popover),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_staticDatepicker__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
                                displayStaticWrapperAs: "desktop",
                                openTo: "day",
                                value: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(date, "YYYY-MM-DD"),
                                onChange: (event)=>{
                                    onDateChange(dayjs__WEBPACK_IMPORTED_MODULE_3___default()(event).format("YYYY-MM-DD"));
                                    handleClose();
                                }
                            }),
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().body),
                                children: [
                                    options?.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                            className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().row),
                                            children: [
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_radioInput__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                                                    ...controlProps(String(item.value))
                                                }),
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("label", {
                                                    className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().label),
                                                    htmlFor: String(item.value),
                                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                        className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text),
                                                        children: item.label
                                                    })
                                                })
                                            ]
                                        }, `${name}-${idx}`)),
                                    !options?.length && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                        className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_12___default().row),
                                        children: t("not.found")
                                    })
                                ]
                            })
                        ]
                    })
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 98916:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ RcParcelPicker)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _pickers_module_scss__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(88297);
/* harmony import */ var _pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var hooks_usePopover__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(58287);
/* harmony import */ var remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(44237);
/* harmony import */ var remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var remixicon_react_ErrorWarningLineIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(53112);
/* harmony import */ var remixicon_react_ErrorWarningLineIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ErrorWarningLineIcon__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var containers_popover_popover__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(56060);
/* harmony import */ var components_inputs_radioInput__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(80865);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(18074);
/* harmony import */ var containers_modal_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(47567);
/* harmony import */ var components_parcelShow_parcelShow__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(1125);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_7__, components_parcelShow_parcelShow__WEBPACK_IMPORTED_MODULE_9__]);
([hooks_useLocale__WEBPACK_IMPORTED_MODULE_7__, components_parcelShow_parcelShow__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);











function RcParcelPicker({ value , name , onChange , options , label , error , type ="outlined" , placeholder , icon  }) {
    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)();
    const [parcel, setParcel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);
    const [open, anchor, handleOpen, handleClose] = (0,hooks_usePopover__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)();
    const data = options?.find((el)=>el.value == value)?.data;
    const handleChange = (event)=>{
        if (onChange) onChange(event, undefined);
        handleClose();
    };
    const controlProps = (item)=>({
            checked: String(value) === item,
            onChange: handleChange,
            value: item,
            id: item,
            name,
            inputProps: {
                "aria-label": item
            }
        });
    function handleCloseInfo() {
        setParcel(undefined);
    }
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: `${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().container)} ${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default())[type]}`,
        children: [
            !!label && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().title),
                children: label
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: `${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().wrapper)} ${error ? (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().error) : ""}`,
                onClick: handleOpen,
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().iconWrapper),
                        children: [
                            icon,
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                                className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().text),
                                children: [
                                    data?.type,
                                    " ",
                                    !!data ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                                        className: `${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().muted)} ${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().text)}`,
                                        children: [
                                            "(",
                                            t("up.to.weight", {
                                                number: Number(data?.max_g) / 1000
                                            }),
                                            ")"
                                        ]
                                    }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        children: placeholder
                                    })
                                ]
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowDownSLineIcon__WEBPACK_IMPORTED_MODULE_3___default()), {})
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_popover_popover__WEBPACK_IMPORTED_MODULE_5__["default"], {
                open: open,
                anchorEl: anchor,
                onClose: handleClose,
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: `${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().body)} ${(_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().wide)}`,
                    children: [
                        options?.map((item)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().row),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_radioInput__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                                        ...controlProps(String(item.value))
                                    }),
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", {
                                        className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().label),
                                        htmlFor: String(item.value),
                                        children: [
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                                                className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().text),
                                                children: [
                                                    item.label,
                                                    " ",
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                                                        className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().muted),
                                                        children: [
                                                            "(",
                                                            t("up.to.weight", {
                                                                number: Number(item?.data?.max_g) / 1000
                                                            }),
                                                            ")"
                                                        ]
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                                                onClick: ()=>setParcel(item?.data),
                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ErrorWarningLineIcon__WEBPACK_IMPORTED_MODULE_4___default()), {})
                                            })
                                        ]
                                    })
                                ]
                            }, item.value)),
                        !options?.length && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            className: (_pickers_module_scss__WEBPACK_IMPORTED_MODULE_10___default().row),
                            children: t("not.found")
                        })
                    ]
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_modal_modal__WEBPACK_IMPORTED_MODULE_8__["default"], {
                open: Boolean(parcel),
                onClose: handleCloseInfo,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_parcelShow_parcelShow__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                    data: parcel
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 99959:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ParcelHeaderForm)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(66714);
/* harmony import */ var _shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57987);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6022);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var redux_slices_currency__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(64698);
/* harmony import */ var services_parcel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(47763);
/* harmony import */ var components_price_price__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(90026);
/* harmony import */ var containers_orderMap_orderMap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(55642);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_3__, services_parcel__WEBPACK_IMPORTED_MODULE_7__, containers_orderMap_orderMap__WEBPACK_IMPORTED_MODULE_9__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_3__, services_parcel__WEBPACK_IMPORTED_MODULE_7__, containers_orderMap_orderMap__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);











function ParcelHeaderForm({ children , formik , lang , xs , md , lg , loading , handleSelectType  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();
    const currency = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__.useSelector)(redux_slices_currency__WEBPACK_IMPORTED_MODULE_6__/* .selectCurrency */ .j);
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)("(min-width:900px)");
    const { location_from , location_to , type_id  } = formik.values;
    const { data: price  } = (0,react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)([
        "calculateParcel",
        location_from,
        location_to,
        type_id,
        currency
    ], ()=>services_parcel__WEBPACK_IMPORTED_MODULE_7__/* ["default"].calculate */ .Z.calculate({
            address_from: location_from,
            address_to: location_to,
            type_id,
            currency_id: currency?.id
        }), {
        enabled: Boolean(type_id),
        select: (data)=>data.data.price
    });
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
        item: true,
        xs: xs,
        md: md,
        lg: lg,
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
            className: (_shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10___default().container),
            children: [
                isDesktop && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    className: (_shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10___default().map),
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_orderMap_orderMap__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                        fullHeight: true,
                        drawLine: true,
                        data: {
                            location: location_from,
                            shop: {
                                id: 0,
                                logo_img: "/images/finish.png",
                                location: location_to,
                                translation: {
                                    title: "Finish",
                                    locale: "en",
                                    description: ""
                                },
                                price: 0,
                                open: true
                            }
                        },
                        price: price
                    })
                }),
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: (_shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10___default().heading),
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("strong", {
                            className: (_shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10___default().title),
                            children: t("door.to.door.delivery")
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                            className: (_shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10___default().desc),
                            children: t("door.to.door.delivery.description")
                        })
                    ]
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    className: (_shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10___default().wrapper),
                    children: react__WEBPACK_IMPORTED_MODULE_1___default().Children.map(children, (child)=>{
                        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(child, {
                            formik,
                            lang,
                            loading,
                            handleSelectType
                        });
                    })
                }),
                price && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                    className: (_shopForm_module_scss__WEBPACK_IMPORTED_MODULE_10___default().price),
                    children: [
                        t("pay"),
                        " ",
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_price_price__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
                            number: price
                        })
                    ]
                })
            ]
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 94271:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ParcelCheckoutContainer)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _parcelCheckout_module_scss__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(73747);
/* harmony import */ var _parcelCheckout_module_scss__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(_parcelCheckout_module_scss__WEBPACK_IMPORTED_MODULE_18__);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1635);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2296);
/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(formik__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57987);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(29969);
/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(21697);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(74621);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var components_shopForm_shopForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(83188);
/* harmony import */ var components_unauthorized_unauthorized__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(9031);
/* harmony import */ var services_parcel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(47763);
/* harmony import */ var hooks_useRedux__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34349);
/* harmony import */ var redux_slices_currency__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(64698);
/* harmony import */ var services_payment__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(85943);
/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(5848);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_4__, components_alert_toast__WEBPACK_IMPORTED_MODULE_9__, components_unauthorized_unauthorized__WEBPACK_IMPORTED_MODULE_12__, services_parcel__WEBPACK_IMPORTED_MODULE_13__, services_payment__WEBPACK_IMPORTED_MODULE_16__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_4__, components_alert_toast__WEBPACK_IMPORTED_MODULE_9__, components_unauthorized_unauthorized__WEBPACK_IMPORTED_MODULE_12__, services_parcel__WEBPACK_IMPORTED_MODULE_13__, services_payment__WEBPACK_IMPORTED_MODULE_16__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);



















function ParcelCheckoutContainer({ children  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_7__.useMediaQuery)("(min-width:1140px)");
    const { isAuthenticated , user  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_5__/* .useAuth */ .a)();
    const { address , location  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_6__/* .useSettings */ .r)();
    const latlng = location;
    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();
    const currency = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_14__/* .useAppSelector */ .C)(redux_slices_currency__WEBPACK_IMPORTED_MODULE_15__/* .selectCurrency */ .j);
    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();
    const handleSelectType = (value)=>{
        setSelectedType(value);
    };
    const { data: payments  } = (0,react_query__WEBPACK_IMPORTED_MODULE_8__.useQuery)("payments", ()=>services_payment__WEBPACK_IMPORTED_MODULE_16__/* ["default"].getAll */ .Z.getAll());
    const formik = (0,formik__WEBPACK_IMPORTED_MODULE_3__.useFormik)({
        initialValues: {
            type_id: "",
            phone_from: user?.phone,
            username_from: [
                user?.firstname,
                user?.lastname
            ].join(" "),
            location_from: {
                latitude: latlng?.split(",")[0],
                longitude: latlng?.split(",")[1]
            },
            address_from: address,
            house_from: undefined,
            stage_from: undefined,
            room_from: undefined,
            phone_to: "",
            username_to: "",
            location_to: {
                latitude: latlng?.split(",")[0],
                longitude: (Number(latlng?.split(",")[1]) + 1).toString()
            },
            address_to: address,
            house_to: undefined,
            stage_to: undefined,
            room_to: undefined,
            delivery_date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().add(1, "day").format("YYYY-MM-DD"),
            delivery_time: "13:00",
            note: "",
            images: [],
            payment_type_id: undefined,
            description: undefined,
            qr_value: undefined,
            instructions: undefined
        },
        onSubmit: (values)=>{
            const body = {
                currency_id: currency?.id,
                type_id: values.type_id,
                rate: currency?.rate,
                phone_from: values.phone_from,
                username_from: values.username_from,
                address_from: {
                    latitude: Number(values.location_from?.latitude),
                    longitude: Number(values.location_from?.longitude),
                    address: values.address_from,
                    house: values.house_from,
                    stage: values.stage_from,
                    room: values.room_from
                },
                phone_to: values.phone_to,
                username_to: values.username_to,
                address_to: {
                    latitude: Number(values.location_to?.latitude),
                    longitude: Number(values.location_to?.longitude),
                    address: values.address_to,
                    house: values.house_to,
                    stage: values.stage_to,
                    room: values.room_to
                },
                delivery_date: values.delivery_date,
                delivery_time: values.delivery_time,
                note: values.note,
                images: values.images,
                description: values.description,
                instructions: values.instructions,
                notify: values.notify ? 1 : 0,
                qr_value: values.qr_value
            };
            mutate(body);
        },
        validate: (values)=>{
            const errors = {};
            const re = /^[\+]?[0-9\b]+$/;
            if (!values.type_id) {
                errors.type_id = t("required");
            }
            if (!values.payment_type_id) {
                errors.payment_type_id = t("required");
            }
            if (!values.phone_from) {
                errors.phone_from = t("required");
            } else if (!re.test(values.phone_from)) {
                errors.phone_from = t("invalid");
            }
            if (!values.username_from) {
                errors.username_from = t("required");
            }
            if (!values.address_from) {
                errors.address_from = t("required");
            }
            if (!values.phone_to) {
                errors.phone_to = t("required");
            } else if (!re.test(values.phone_to)) {
                errors.phone_to = t("invalid");
            }
            if (!values.username_to) {
                errors.username_to = t("required");
            }
            if (!values.address_to) {
                errors.address_to = t("required");
            }
            if (!values.delivery_date) {
                errors.delivery_date = t("required");
            }
            if (!values.delivery_time) {
                errors.delivery_time = t("required");
            }
            return errors;
        }
    });
    const { isLoading , mutate  } = (0,react_query__WEBPACK_IMPORTED_MODULE_8__.useMutation)({
        mutationFn: (data)=>services_parcel__WEBPACK_IMPORTED_MODULE_13__/* ["default"].create */ .Z.create(data),
        onSuccess: (data)=>{
            const paymentId = formik.values.payment_type_id;
            const payload = {
                id: data.data.id,
                payment: {
                    payment_sys_id: paymentId
                }
            };
            const paymentType = payments?.data.find((item)=>item.id == paymentId)?.tag;
            if (constants_constants__WEBPACK_IMPORTED_MODULE_17__/* .EXTERNAL_PAYMENTS.includes */ .DH.includes(paymentType || "")) {
                externalPay({
                    name: paymentType,
                    data: {
                        parcel_id: payload.id
                    }
                });
            }
            transactionCreate(payload);
        },
        onError: ()=>{
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_9__/* .error */ .vU)(t("error.400"));
        }
    });
    const { isLoading: isLoadingTransaction , mutate: transactionCreate  } = (0,react_query__WEBPACK_IMPORTED_MODULE_8__.useMutation)({
        mutationFn: (data)=>services_payment__WEBPACK_IMPORTED_MODULE_16__/* ["default"].parcelTransaction */ .Z.parcelTransaction(data.id, data.payment),
        onSuccess: (data)=>{
            push(`/parcels/${data.data.id}`);
        },
        onError: (err)=>{
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_9__/* .error */ .vU)(err?.data?.message);
        }
    });
    const { isLoading: externalPayLoading , mutate: externalPay  } = (0,react_query__WEBPACK_IMPORTED_MODULE_8__.useMutation)({
        mutationFn: (payload)=>services_payment__WEBPACK_IMPORTED_MODULE_16__/* ["default"].payExternal */ .Z.payExternal(payload.name, payload.data),
        onSuccess: (data)=>{
            window.location.replace(data.data.data.url);
        },
        onError: (err)=>{
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_9__/* .error */ .vU)(err?.data?.message);
        }
    });
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_parcelCheckout_module_scss__WEBPACK_IMPORTED_MODULE_18___default().root),
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: (_parcelCheckout_module_scss__WEBPACK_IMPORTED_MODULE_18___default().container),
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    className: "container",
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_parcelCheckout_module_scss__WEBPACK_IMPORTED_MODULE_18___default().header),
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h1", {
                            className: (_parcelCheckout_module_scss__WEBPACK_IMPORTED_MODULE_18___default().title),
                            children: t("door.to.door.delivery")
                        })
                    })
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: "container",
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("form", {
                    className: (_parcelCheckout_module_scss__WEBPACK_IMPORTED_MODULE_18___default().wrapper),
                    onSubmit: formik.handleSubmit,
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {
                        container: true,
                        spacing: isDesktop ? 4 : 1,
                        children: isAuthenticated ? react__WEBPACK_IMPORTED_MODULE_1___default().Children.map(children, (child)=>{
                            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(child, {
                                formik,
                                loading: isLoading || isLoadingTransaction || externalPayLoading,
                                selectedType,
                                handleSelectType
                            });
                        }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_shopForm_shopForm__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
                            xs: 12,
                            md: 8,
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_unauthorized_unauthorized__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
                                text: t("sign.in.parcel.order")
                            })
                        })
                    })
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 10972:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ParcelFeatureList)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _parcelFeatureList_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(87939);
/* harmony import */ var _parcelFeatureList_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_parcelFeatureList_module_scss__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(53015);
/* harmony import */ var components_parcelFeaturesingle_parcelFeatureSingle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3058);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57987);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swiper_react__WEBPACK_IMPORTED_MODULE_2__, components_parcelFeaturesingle_parcelFeatureSingle__WEBPACK_IMPORTED_MODULE_3__, react_i18next__WEBPACK_IMPORTED_MODULE_4__]);
([swiper_react__WEBPACK_IMPORTED_MODULE_2__, components_parcelFeaturesingle_parcelFeatureSingle__WEBPACK_IMPORTED_MODULE_3__, react_i18next__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);






const storySettings = {
    spaceBetween: 10,
    preloadImages: false,
    className: "full-width",
    breakpoints: {
        1140: {
            slidesPerView: 4,
            spaceBetween: 20
        },
        992: {
            slidesPerView: 3.5
        },
        576: {
            slidesPerView: 2.5,
            spaceBetween: 10
        },
        0: {
            slidesPerView: 2.1
        }
    }
};
function ParcelFeatureList({ data , loading  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h6", {
                className: (_parcelFeatureList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().title),
                children: t("how.it.works")
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: (_parcelFeatureList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().storyContainer),
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(swiper_react__WEBPACK_IMPORTED_MODULE_2__.Swiper, {
                    ...storySettings,
                    slidesPerView: "auto",
                    children: data?.map((item, idx)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(swiper_react__WEBPACK_IMPORTED_MODULE_2__.SwiperSlide, {
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_parcelFeaturesingle_parcelFeatureSingle__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                                data: item,
                                list: data
                            })
                        }, idx))
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 53689:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ParcelCheckout)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var components_seo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(84169);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(18074);
/* harmony import */ var services_parcel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(47763);
/* harmony import */ var components_shopForm_shopForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(83188);
/* harmony import */ var components_parcelForm_parcelForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(28922);
/* harmony import */ var components_parcelForm_parcelSender__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(40658);
/* harmony import */ var components_parcelForm_parcelReceiver__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(47274);
/* harmony import */ var containers_parcelCheckout_parcelCheckout__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(94271);
/* harmony import */ var components_shopForm_parcelHeaderForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(99959);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var containers_parcelFeatureList_parcelFeatureList__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(10972);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__, services_parcel__WEBPACK_IMPORTED_MODULE_5__, components_parcelForm_parcelForm__WEBPACK_IMPORTED_MODULE_7__, components_parcelForm_parcelSender__WEBPACK_IMPORTED_MODULE_8__, components_parcelForm_parcelReceiver__WEBPACK_IMPORTED_MODULE_9__, containers_parcelCheckout_parcelCheckout__WEBPACK_IMPORTED_MODULE_10__, components_shopForm_parcelHeaderForm__WEBPACK_IMPORTED_MODULE_11__, containers_parcelFeatureList_parcelFeatureList__WEBPACK_IMPORTED_MODULE_13__]);
([hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__, services_parcel__WEBPACK_IMPORTED_MODULE_5__, components_parcelForm_parcelForm__WEBPACK_IMPORTED_MODULE_7__, components_parcelForm_parcelSender__WEBPACK_IMPORTED_MODULE_8__, components_parcelForm_parcelReceiver__WEBPACK_IMPORTED_MODULE_9__, containers_parcelCheckout_parcelCheckout__WEBPACK_IMPORTED_MODULE_10__, components_shopForm_parcelHeaderForm__WEBPACK_IMPORTED_MODULE_11__, containers_parcelFeatureList_parcelFeatureList__WEBPACK_IMPORTED_MODULE_13__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);
//@ts-nocheck














const features = [
    [
        {
            id: 0,
            img: "/images/parcel/feature1.png",
            title: "save.time"
        }
    ],
    [
        {
            id: 1,
            img: "/images/parcel/feature2.png",
            title: "set.up.delivery"
        }
    ],
    [
        {
            id: 2,
            img: "/images/parcel/feature3.png",
            title: "fast.&.secure.delivery"
        }
    ],
    [
        {
            id: 3,
            img: "/images/parcel/feature4.png",
            title: "delivery.restrictions"
        }
    ]
];
function ParcelCheckout({}) {
    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)();
    const { data: types  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)("parcelTypes", ()=>services_parcel__WEBPACK_IMPORTED_MODULE_5__/* ["default"].getAllTypes */ .Z.getAllTypes());
    const { data: payments  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)("payments", ()=>paymentService.getAll());
    const formatCategories = (list = [])=>{
        if (!list.length) {
            return [];
        }
        return list.map((item)=>({
                label: item.type || t(item.tag),
                value: item.id,
                data: item
            }));
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_seo__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {}),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(containers_parcelCheckout_parcelCheckout__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_12__.Grid, {
                        item: true,
                        xs: 12,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_parcelFeatureList_parcelFeatureList__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
                            data: features
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_shopForm_parcelHeaderForm__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
                        xs: 12,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_parcelForm_parcelForm__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                            types: formatCategories(types?.data),
                            payments: formatCategories(payments?.data)
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_shopForm_shopForm__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                        title: t("sender.details"),
                        xs: 12,
                        md: 6,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_parcelForm_parcelSender__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {})
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_shopForm_shopForm__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                        title: t("receiver.details"),
                        xs: 12,
                        md: 6,
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_parcelForm_parcelReceiver__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {})
                    })
                ]
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 86701:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "u6": () => (/* binding */ formatBrazilianDateTime),
/* harmony export */   "wx": () => (/* binding */ formatBrazilianOrderDate),
/* harmony export */   "yG": () => (/* binding */ formatBrazilianDate)
/* harmony export */ });
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1635);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var dayjs_locale_pt_br__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(45288);
/* harmony import */ var dayjs_locale_pt_br__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_pt_br__WEBPACK_IMPORTED_MODULE_1__);


/**
 * Formats a date in Brazilian Portuguese format
 * @param date - Date string or Date object to format
 * @param includeTime - Whether to include time in the format (default: true)
 * @returns Formatted date string like "Sex, 25 de jul, 02:30" or "Sex, 25 de jul"
 */ function formatBrazilianDate(date, includeTime = true) {
    if (!date) return "";
    const dayjsDate = dayjs__WEBPACK_IMPORTED_MODULE_0___default()(date).locale("pt-br");
    if (includeTime) {
        // Format: "Sex, 25 de jul, 02:30"
        return dayjsDate.format("ddd, DD [de] MMM, HH:mm");
    } else {
        // Format: "Sex, 25 de jul"
        return dayjsDate.format("ddd, DD [de] MMM");
    }
}
/**
 * Formats a date with time in Brazilian Portuguese format
 * @param date - Date string or Date object to format
 * @param time - Time string to append (optional)
 * @returns Formatted date string like "Sex, 25 de jul, 02:30"
 */ function formatBrazilianDateTime(date, time) {
    if (!date) return "";
    const dayjsDate = dayjs__WEBPACK_IMPORTED_MODULE_0___default()(date).locale("pt-br");
    if (time) {
        // Format: "Sex, 25 de jul, 02:30" (using provided time)
        return `${dayjsDate.format("ddd, DD [de] MMM")}, ${time}`;
    } else {
        // Format: "Sex, 25 de jul, 02:30" (using date's time)
        return dayjsDate.format("ddd, DD [de] MMM, HH:mm");
    }
}
/**
 * Formats a date for order creation display
 * @param date - Date string or Date object to format
 * @returns Formatted date string like "25 de jul, 02:30"
 */ function formatBrazilianOrderDate(date) {
    if (!date) return "";
    const dayjsDate = dayjs__WEBPACK_IMPORTED_MODULE_0___default()(date).locale("pt-br");
    return dayjsDate.format("DD [de] MMM, HH:mm");
}


/***/ }),

/***/ 65692:
/***/ ((module) => {

"use strict";
module.exports = require("@mui/material");

/***/ }),

/***/ 18442:
/***/ ((module) => {

"use strict";
module.exports = require("@mui/material/styles");

/***/ }),

/***/ 73280:
/***/ ((module) => {

"use strict";
module.exports = require("@mui/x-date-pickers");

/***/ }),

/***/ 10298:
/***/ ((module) => {

"use strict";
module.exports = require("@mui/x-date-pickers/AdapterDayjs");

/***/ }),

/***/ 85753:
/***/ ((module) => {

"use strict";
module.exports = require("@mui/x-date-pickers/LocalizationProvider");

/***/ }),

/***/ 75184:
/***/ ((module) => {

"use strict";
module.exports = require("@reduxjs/toolkit");

/***/ }),

/***/ 1635:
/***/ ((module) => {

"use strict";
module.exports = require("dayjs");

/***/ }),

/***/ 45288:
/***/ ((module) => {

"use strict";
module.exports = require("dayjs/locale/pt-br");

/***/ }),

/***/ 2296:
/***/ ((module) => {

"use strict";
module.exports = require("formik");

/***/ }),

/***/ 58557:
/***/ ((module) => {

"use strict";
module.exports = require("google-map-react");

/***/ }),

/***/ 7486:
/***/ ((module) => {

"use strict";
module.exports = require("next-cookies");

/***/ }),

/***/ 3280:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/app-router-context.js");

/***/ }),

/***/ 92796:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/head-manager-context.js");

/***/ }),

/***/ 94957:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/head.js");

/***/ }),

/***/ 34014:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/i18n/normalize-locale-path.js");

/***/ }),

/***/ 64486:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-blur-svg.js");

/***/ }),

/***/ 50744:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-config-context.js");

/***/ }),

/***/ 35843:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-config.js");

/***/ }),

/***/ 99552:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-loader");

/***/ }),

/***/ 78524:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ 95832:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/loadable.js");

/***/ }),

/***/ 78020:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/mitt.js");

/***/ }),

/***/ 64406:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/page-path/denormalize-page-path.js");

/***/ }),

/***/ 24964:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router-context.js");

/***/ }),

/***/ 11751:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix.js");

/***/ }),

/***/ 46220:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/compare-states.js");

/***/ }),

/***/ 10299:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-next-pathname-info.js");

/***/ }),

/***/ 23938:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-url.js");

/***/ }),

/***/ 29565:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/get-asset-path-from-route.js");

/***/ }),

/***/ 35789:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/get-next-pathname-info.js");

/***/ }),

/***/ 1897:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot.js");

/***/ }),

/***/ 1428:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-dynamic.js");

/***/ }),

/***/ 28854:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/parse-path.js");

/***/ }),

/***/ 91292:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/parse-relative-url.js");

/***/ }),

/***/ 34567:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/path-has-prefix.js");

/***/ }),

/***/ 80979:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/querystring.js");

/***/ }),

/***/ 93297:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash.js");

/***/ }),

/***/ 36052:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/resolve-rewrites.js");

/***/ }),

/***/ 84226:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/route-matcher.js");

/***/ }),

/***/ 95052:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/route-regex.js");

/***/ }),

/***/ 59232:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ 40968:
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ 71853:
/***/ ((module) => {

"use strict";
module.exports = require("next/router");

/***/ }),

/***/ 16689:
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ 66405:
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ 61175:
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ 6022:
/***/ ((module) => {

"use strict";
module.exports = require("react-redux");

/***/ }),

/***/ 20997:
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 44237:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ArrowDownSLineIcon");

/***/ }),

/***/ 625:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ArrowLeftLineIcon");

/***/ }),

/***/ 10931:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/BankCardLineIcon");

/***/ }),

/***/ 4634:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CheckboxCircleLineIcon");

/***/ }),

/***/ 11060:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CloseFillIcon");

/***/ }),

/***/ 5948:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CompassDiscoverLineIcon");

/***/ }),

/***/ 53112:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ErrorWarningLineIcon");

/***/ }),

/***/ 12564:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/InformationLineIcon");

/***/ }),

/***/ 99893:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/MapPinRangeLineIcon");

/***/ }),

/***/ 81330:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/PencilFillIcon");

/***/ }),

/***/ 78428:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/Search2LineIcon");

/***/ }),

/***/ 2923:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/TimeLineIcon");

/***/ }),

/***/ 99648:
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ 22021:
/***/ ((module) => {

"use strict";
module.exports = import("i18next");;

/***/ }),

/***/ 64329:
/***/ ((module) => {

"use strict";
module.exports = import("i18next-http-backend");;

/***/ }),

/***/ 69915:
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ 57987:
/***/ ((module) => {

"use strict";
module.exports = import("react-i18next");;

/***/ }),

/***/ 3590:
/***/ ((module) => {

"use strict";
module.exports = import("react-toastify");;

/***/ }),

/***/ 53015:
/***/ ((module) => {

"use strict";
module.exports = import("swiper/react");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [2078,5675,676,1664,5152,3075,5728,4169,7562,7262,6684,26,7567,1612,5122,251,865,6323,5567,1662,6393,7685,9850,5642,9907,9056,3188], () => (__webpack_exec__(53689)));
module.exports = __webpack_exports__;

})();