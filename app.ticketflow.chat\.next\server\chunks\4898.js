exports.id = 4898;
exports.ids = [4898];
exports.modules = {

/***/ 9168:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "appDrawer_wrapper___1El3",
	"actions": "appDrawer_actions__jyq1_",
	"body": "appDrawer_body__LPlrC",
	"row": "appDrawer_row__X1Fh_",
	"rowItem": "appDrawer_rowItem__1QEiZ",
	"text": "appDrawer_text__NXg06",
	"bold": "appDrawer_bold__d9w5Z",
	"badge": "appDrawer_badge__EVHwN",
	"footer": "appDrawer_footer__7mtZ2",
	"flex": "appDrawer_flex__V3PdX",
	"item": "appDrawer_item__Qrjni",
	"imgWrapper": "appDrawer_imgWrapper__FdDRs",
	"iconBtn": "appDrawer_iconBtn__EEkIQ"
};


/***/ }),

/***/ 86349:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "profileCard_wrapper__1K0Rd",
	"profile": "profileCard_profile__2Iz7f",
	"naming": "profileCard_naming__sQ6sV",
	"link": "profileCard_link__y_J2Z",
	"profileImage": "profileCard_profileImage___T91r",
	"logoutBtn": "profileCard_logoutBtn__Ymai0"
};


/***/ }),

/***/ 44898:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ AppDrawer)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var containers_drawer_drawer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(68181);
/* harmony import */ var _appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(9168);
/* harmony import */ var _appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57987);
/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(6684);
/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(80892);
/* harmony import */ var components_button_darkButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(94660);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(80108);
/* harmony import */ var remixicon_react_MoonFillIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(92379);
/* harmony import */ var remixicon_react_MoonFillIcon__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_MoonFillIcon__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var remixicon_react_SunFillIcon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(88873);
/* harmony import */ var remixicon_react_SunFillIcon__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_SunFillIcon__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(29969);
/* harmony import */ var _mobileAppDrawer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(58347);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(21697);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_icons__WEBPACK_IMPORTED_MODULE_4__, _mobileAppDrawer__WEBPACK_IMPORTED_MODULE_12__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_icons__WEBPACK_IMPORTED_MODULE_4__, _mobileAppDrawer__WEBPACK_IMPORTED_MODULE_12__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);
/* eslint-disable @next/next/no-img-element */ 















function AppDrawer({ open , handleClose  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();
    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();
    const isMobile = (0,_mui_material__WEBPACK_IMPORTED_MODULE_13__.useMediaQuery)("(max-width:1139px)");
    const { isDarkMode , toggleDarkMode , direction  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_8__/* .ThemeContext */ .N);
    const { isAuthenticated  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_11__/* .useAuth */ .a)();
    const { settings  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_14__/* .useSettings */ .r)();
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(containers_drawer_drawer__WEBPACK_IMPORTED_MODULE_2__["default"], {
        anchor: direction === "rtl" ? "right" : "left",
        open: open,
        onClose: handleClose,
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().iconBtn),
                onClick: toggleDarkMode,
                children: isDarkMode ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_MoonFillIcon__WEBPACK_IMPORTED_MODULE_9___default()), {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_SunFillIcon__WEBPACK_IMPORTED_MODULE_10___default()), {})
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().wrapper),
                children: [
                    isMobile && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mobileAppDrawer__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
                        handleClose: handleClose
                    }),
                    !isAuthenticated ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().actions),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_button_darkButton__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                                onClick: ()=>{
                                    push("/register");
                                    handleClose();
                                },
                                children: t("sign.up")
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
                                onClick: ()=>{
                                    push("/login");
                                    handleClose();
                                },
                                children: t("login")
                            })
                        ]
                    }) : "",
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().footer),
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().flex),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_4__/* .BrandLogoRounded */ .NV, {}),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().text),
                                        children: t("app.text")
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().flex),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: settings?.customer_app_ios,
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().item),
                                        target: "_blank",
                                        rel: "noopener noreferrer",
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                            className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().imgWrapper),
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("img", {
                                                src: "/images/app-store.webp",
                                                alt: "App store"
                                            })
                                        })
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: settings?.customer_app_android,
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().item),
                                        target: "_blank",
                                        rel: "noopener noreferrer",
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                            className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_15___default().imgWrapper),
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("img", {
                                                src: "/images/google-play.webp",
                                                alt: "Google play"
                                            })
                                        })
                                    })
                                ]
                            })
                        ]
                    })
                ]
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 58347:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ MobileAppDrawer)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(9168);
/* harmony import */ var _appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(41664);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57987);
/* harmony import */ var components_profileCard_profileCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(64360);
/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(51406);
/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var remixicon_react_HeartLineIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(26582);
/* harmony import */ var remixicon_react_HeartLineIcon__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_HeartLineIcon__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var remixicon_react_HistoryLineIcon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(81812);
/* harmony import */ var remixicon_react_HistoryLineIcon__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_HistoryLineIcon__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var remixicon_react_ArchiveLineIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(89224);
/* harmony import */ var remixicon_react_ArchiveLineIcon__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArchiveLineIcon__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var remixicon_react_Wallet3LineIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(907);
/* harmony import */ var remixicon_react_Wallet3LineIcon__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_Wallet3LineIcon__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var remixicon_react_QuestionLineIcon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(97240);
/* harmony import */ var remixicon_react_QuestionLineIcon__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_QuestionLineIcon__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var remixicon_react_Settings3LineIcon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(78740);
/* harmony import */ var remixicon_react_Settings3LineIcon__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_Settings3LineIcon__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var remixicon_react_UserStarLineIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(29932);
/* harmony import */ var remixicon_react_UserStarLineIcon__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_UserStarLineIcon__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(29969);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(37490);
/* harmony import */ var containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(30182);
/* harmony import */ var components_languagePopover_languagePopover__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(45049);
/* harmony import */ var components_currencyList_currencyList__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(1729);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_18__);
/* harmony import */ var services_order__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(94098);
/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(87104);
/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(qs__WEBPACK_IMPORTED_MODULE_20__);
/* harmony import */ var constants_status__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(16346);
/* harmony import */ var components_price_price__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(90026);
/* harmony import */ var remixicon_react_MapPin2LineIcon__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(80110);
/* harmony import */ var remixicon_react_MapPin2LineIcon__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_MapPin2LineIcon__WEBPACK_IMPORTED_MODULE_22__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_profileCard_profileCard__WEBPACK_IMPORTED_MODULE_4__, components_languagePopover_languagePopover__WEBPACK_IMPORTED_MODULE_16__, components_currencyList_currencyList__WEBPACK_IMPORTED_MODULE_17__, services_order__WEBPACK_IMPORTED_MODULE_19__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_profileCard_profileCard__WEBPACK_IMPORTED_MODULE_4__, components_languagePopover_languagePopover__WEBPACK_IMPORTED_MODULE_16__, components_currencyList_currencyList__WEBPACK_IMPORTED_MODULE_17__, services_order__WEBPACK_IMPORTED_MODULE_19__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);

























function MobileAppDrawer({ handleClose  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();
    const { user , isAuthenticated  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_13__/* .useAuth */ .a)();
    const [langDrawer, handleOpenLangDrawer, handleCloseLangDrawer] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)();
    const [currencyDrawer, handleOpenCurrencyDrawer, handleCloseCurrencyDrawer] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)();
    const { data: activeOrders  } = (0,react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)("activeOrders", ()=>services_order__WEBPACK_IMPORTED_MODULE_19__/* ["default"].getAll */ .Z.getAll(qs__WEBPACK_IMPORTED_MODULE_20___default().stringify({
            order_statuses: true,
            statuses: constants_status__WEBPACK_IMPORTED_MODULE_23__/* .activeOrderStatuses */ .j
        })), {
        retry: false,
        enabled: isAuthenticated
    });
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().body),
                children: [
                    isAuthenticated && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_profileCard_profileCard__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
                        data: user,
                        handleClose: handleClose
                    }),
                    isAuthenticated && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                        href: "/wallet",
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().row),
                        onClick: handleClose,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().rowItem),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_Wallet3LineIcon__WEBPACK_IMPORTED_MODULE_9___default()), {}),
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().text),
                                        children: [
                                            t("wallet"),
                                            ":"
                                        ]
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().bold),
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_price_price__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {
                                            number: user.wallet?.price,
                                            symbol: user.wallet?.symbol
                                        })
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                        ]
                    }),
                    isAuthenticated && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                        href: "/orders",
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().row),
                        onClick: handleClose,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().rowItem),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_HistoryLineIcon__WEBPACK_IMPORTED_MODULE_7___default()), {}),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().text),
                                        children: t("orders")
                                    }),
                                    activeOrders?.meta?.total > 0 && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().badge),
                                        children: activeOrders?.meta?.total
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                        ]
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                        href: "/be-seller",
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().row),
                        onClick: handleClose,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().rowItem),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_UserStarLineIcon__WEBPACK_IMPORTED_MODULE_12___default()), {}),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().text),
                                        children: t("be.seller")
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                        ]
                    }),
                    isAuthenticated && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                        href: "/parcels",
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().row),
                        onClick: handleClose,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().rowItem),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArchiveLineIcon__WEBPACK_IMPORTED_MODULE_8___default()), {}),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().text),
                                        children: t("parcels")
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                        ]
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                        href: "/liked",
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().row),
                        onClick: handleClose,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().rowItem),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_HeartLineIcon__WEBPACK_IMPORTED_MODULE_6___default()), {}),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().text),
                                        children: t("liked")
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                        ]
                    }),
                    isAuthenticated && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                        href: "/settings/notification",
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().row),
                        onClick: handleClose,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().rowItem),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_Settings3LineIcon__WEBPACK_IMPORTED_MODULE_11___default()), {}),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().text),
                                        children: t("settings")
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                        ]
                    }),
                    isAuthenticated && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                        href: "/saved-locations",
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().row),
                        onClick: handleClose,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().rowItem),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_MapPin2LineIcon__WEBPACK_IMPORTED_MODULE_22___default()), {}),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().text),
                                        children: t("delivery.addresses")
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                        ]
                    }),
                    isAuthenticated && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                        href: "/help",
                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().row),
                        onClick: handleClose,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().rowItem),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_QuestionLineIcon__WEBPACK_IMPORTED_MODULE_10___default()), {}),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_appDrawer_module_scss__WEBPACK_IMPORTED_MODULE_24___default().text),
                                        children: t("help")
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                        ]
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_15__["default"], {
                open: langDrawer,
                onClose: handleCloseLangDrawer,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_languagePopover_languagePopover__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
                    onClose: handleCloseLangDrawer
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_15__["default"], {
                open: currencyDrawer,
                onClose: handleCloseCurrencyDrawer,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_currencyList_currencyList__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {
                    onClose: handleCloseCurrencyDrawer
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 64360:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ProfileCard)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _profileCard_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(86349);
/* harmony import */ var _profileCard_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_profileCard_module_scss__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(41664);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57987);
/* harmony import */ var remixicon_react_LogoutCircleRLineIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(72324);
/* harmony import */ var remixicon_react_LogoutCircleRLineIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_LogoutCircleRLineIcon__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(29969);
/* harmony import */ var components_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(11295);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_avatar__WEBPACK_IMPORTED_MODULE_7__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_avatar__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);









function ProfileCard({ data , handleClose  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();
    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();
    const { logout  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_6__/* .useAuth */ .a)();
    const handleLogout = ()=>{
        logout();
        handleClose();
        push("/login");
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_profileCard_module_scss__WEBPACK_IMPORTED_MODULE_8___default().wrapper),
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                href: "/profile",
                className: (_profileCard_module_scss__WEBPACK_IMPORTED_MODULE_8___default().profile),
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_profileCard_module_scss__WEBPACK_IMPORTED_MODULE_8___default().naming),
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", {
                                children: [
                                    data.firstname,
                                    " ",
                                    data.lastname?.charAt(0),
                                    "."
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                className: (_profileCard_module_scss__WEBPACK_IMPORTED_MODULE_8___default().link),
                                children: t("view.profile")
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_profileCard_module_scss__WEBPACK_IMPORTED_MODULE_8___default().profileImage),
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_avatar__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                            data: data
                        })
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                className: (_profileCard_module_scss__WEBPACK_IMPORTED_MODULE_8___default().logoutBtn),
                onClick: handleLogout,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_LogoutCircleRLineIcon__WEBPACK_IMPORTED_MODULE_4___default()), {})
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 16346:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "a": () => (/* binding */ orderHistoryStatuses),
/* harmony export */   "j": () => (/* binding */ activeOrderStatuses)
/* harmony export */ });
const activeOrderStatuses = [
    "new",
    "accepted",
    "cooking",
    "ready",
    "on_a_way"
];
const orderHistoryStatuses = [
    "delivered",
    "canceled"
];


/***/ }),

/***/ 94098:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const orderService = {
    calculate: (id, data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/cart/calculate/${id}`, data),
    checkCoupon: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/rest/coupons/check`, data),
    create: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/orders`, data),
    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/dashboard/user/orders/paginate?${params}`),
    getById: (id, params, headers)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/dashboard/user/orders/${id}`, {
            params,
            headers
        }),
    cancel: (id)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/orders/${id}/status/change?status=canceled`),
    review: (id, data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/orders/review/${id}`, data),
    autoRepeat: (id, data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/orders/${id}/repeat`, data),
    deleteAutoRepeat: (id)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"]["delete"] */ .Z["delete"](`/dashboard/user/orders/${id}/delete-repeat`)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (orderService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;