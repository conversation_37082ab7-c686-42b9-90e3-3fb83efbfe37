(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9669,520],{20632:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/reservations",function(){return n(89638)}])},20520:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return c}});var s=n(85893);n(67294);var r=n(91491),i=n.n(r),a=n(77262),o=n(11163),l=n(6734);function c(e){let{text:t,buttonText:n,link:r="/"}=e,{push:c}=(0,o.useRouter)(),{t:d}=(0,l.$G)();return(0,s.jsx)("div",{className:"container",children:(0,s.jsxs)("div",{className:i().wrapper,children:[(0,s.jsx)("img",{src:"/images/delivery.webp",alt:d("empty")}),(0,s.jsx)("p",{className:i().text,children:t}),!!n&&(0,s.jsx)("div",{className:i().actions,children:(0,s.jsx)(a.Z,{onClick:()=>c(r),children:n})})]})})}},84169:function(e,t,n){"use strict";n.d(t,{Z:function(){return l}});var s=n(85893);n(67294);var r=n(9008),i=n.n(r),a=n(5848),o=n(3075);function l(e){let{title:t,description:n=o.KM,image:r=o.T5,keywords:l=o.cU}=e,c=a.o6,d=t?t+" | "+o.k5:o.k5;return(0,s.jsxs)(i(),{children:[(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("meta",{charSet:"utf-8"}),(0,s.jsx)("title",{children:d}),(0,s.jsx)("meta",{name:"description",content:n}),(0,s.jsx)("meta",{name:"keywords",content:l}),(0,s.jsx)("meta",{property:"og:type",content:"Website"}),(0,s.jsx)("meta",{name:"title",property:"og:title",content:d}),(0,s.jsx)("meta",{name:"description",property:"og:description",content:n}),(0,s.jsx)("meta",{name:"author",property:"og:author",content:c}),(0,s.jsx)("meta",{property:"og:site_name",content:c}),(0,s.jsx)("meta",{name:"image",property:"og:image",content:r}),(0,s.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,s.jsx)("meta",{name:"twitter:title",content:d}),(0,s.jsx)("meta",{name:"twitter:description",content:n}),(0,s.jsx)("meta",{name:"twitter:site",content:c}),(0,s.jsx)("meta",{name:"twitter:creator",content:c}),(0,s.jsx)("meta",{name:"twitter:image",content:r}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},50530:function(e,t,n){"use strict";n.d(t,{Z:function(){return d}});var s=n(85893);n(67294);var r=n(91249),i=n.n(r),a=n(5152),o=n.n(a);let l=o()(()=>n.e(4474).then(n.bind(n,14474)),{loadableGenerated:{webpack:()=>[14474]}}),c=o()(()=>n.e(9580).then(n.bind(n,89580)),{loadableGenerated:{webpack:()=>[89580]}});function d(e){let{title:t,children:n,refund:r,wallet:a}=e;return(0,s.jsx)("section",{className:i().root,children:(0,s.jsx)("div",{className:"container",children:(0,s.jsxs)("div",{className:i().wrapper,children:[(0,s.jsx)("h1",{className:i().title,children:t}),(0,s.jsx)("div",{className:i().main,children:n}),r&&(0,s.jsx)(l,{}),a&&(0,s.jsx)(c,{})]})})})}},89638:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return y}});var s=n(85893),r=n(67294),i=n(84169),a=n(50530),o=n(5152),l=n.n(o),c=n(88767),d=n(18074),m=n(77322),u=n(24110),x=n.n(u),p=n(88078),h=n(20520),g=n(24318),j=n.n(g),_=n(27484),v=n.n(_),b=n(86886);function f(e){var t,n,r,i;let{data:a,dataIdx:o}=e,{t:l}=(0,d.Z)();return(0,s.jsx)("div",{className:j().wrapper,children:(0,s.jsxs)(b.ZP,{container:!0,spacing:4,alignItems:"center",children:[(0,s.jsx)(b.ZP,{item:!0,sm:4,md:3,lg:2,children:(0,s.jsxs)("div",{className:j().item,children:[(0,s.jsx)("div",{className:j().badge,children:o}),(0,s.jsxs)("div",{className:j().naming,children:[(0,s.jsxs)("h3",{className:j().title,children:["#",a.id]}),(0,s.jsx)("p",{className:j().text,children:l("reservation.id")})]})]})}),(0,s.jsxs)(b.ZP,{item:!0,sm:4,md:3,lg:2,children:[(0,s.jsx)("h3",{className:j().title,children:null==a?void 0:null===(t=a.booking)||void 0===t?void 0:null===(n=t.shop)||void 0===n?void 0:null===(r=n.translation)||void 0===r?void 0:r.title}),(0,s.jsx)("p",{className:j().text,children:l("shop")})]}),(0,s.jsxs)(b.ZP,{item:!0,sm:4,md:3,lg:2,children:[(0,s.jsx)("h3",{className:j().title,children:null===(i=a.table)||void 0===i?void 0:i.name}),(0,s.jsx)("p",{className:j().text,children:l("table")})]}),(0,s.jsxs)(b.ZP,{item:!0,sm:4,md:3,lg:3,children:[(0,s.jsx)("h3",{className:j().title,children:v()(a.start_date).format("DD.MM.YY — HH:mm")}),(0,s.jsx)("p",{className:j().text,children:l("date")})]}),(0,s.jsxs)(b.ZP,{item:!0,sm:4,md:3,lg:2,children:[(0,s.jsx)("h3",{className:j().title,children:l(a.status)}),(0,s.jsx)("p",{className:j().text,children:l("status")})]})]})})}function N(e){let{data:t=[],loading:n=!1}=e,{t:r}=(0,d.Z)();return(0,s.jsxs)("div",{className:x().root,children:[n?Array.from([,,,]).map((e,t)=>(0,s.jsx)(p.Z,{variant:"rectangular",className:x().shimmer},"booking"+t)):t.map((e,t)=>(0,s.jsx)(f,{data:e,dataIdx:t+1},e.id)),!n&&!t.length&&(0,s.jsx)(h.default,{text:r("no.reservations.found")})]})}let w=l()(()=>Promise.resolve().then(n.bind(n,37935)),{loadableGenerated:{webpack:()=>[37935]}}),k=l()(()=>Promise.all([n.e(4564),n.e(2175),n.e(129),n.e(2598),n.e(224),n.e(6860),n.e(6515),n.e(5415)]).then(n.bind(n,16515)),{loadableGenerated:{webpack:()=>[16515]}});function y(e){var t;let{}=e,{t:n,locale:o}=(0,d.Z)(),l=(0,r.useRef)(null),{data:u,error:x,fetchNextPage:p,hasNextPage:h,isFetchingNextPage:g,isLoading:j}=(0,c.useInfiniteQuery)(["bookingHistory",o],e=>{let{pageParam:t=1}=e;return m.Z.getBookingHistory({page:t})},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1}}),_=(0,r.useCallback)(e=>{let t=e[0];t.isIntersecting&&h&&p()},[h,p]);return(0,r.useEffect)(()=>{let e=new IntersectionObserver(_,{root:null,rootMargin:"20px",threshold:0});l.current&&e.observe(l.current)},[_]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.Z,{}),(0,s.jsxs)("div",{className:"bg-white",children:[(0,s.jsxs)(a.Z,{title:n("reservations"),children:[(0,s.jsx)(N,{data:(null==u?void 0:null===(t=u.pages)||void 0===t?void 0:t.flatMap(e=>e.data))||[],loading:j&&!g}),g&&(0,s.jsx)(w,{}),(0,s.jsx)("div",{ref:l})]}),(0,s.jsx)(k,{})]})]})}},77322:function(e,t,n){"use strict";var s=n(25728);t.Z={getAll:e=>s.Z.get("/rest/booking/bookings",{params:e}),disabledDates:(e,t)=>s.Z.get("/rest/booking/disable-dates/table/".concat(e),{params:t}),create:e=>s.Z.post("/dashboard/user/my-bookings",e),getTables:e=>s.Z.get("/rest/booking/tables",{params:e}),getZones:e=>s.Z.get("/rest/booking/shop-sections",{params:e}),getZoneById:(e,t)=>s.Z.get("/rest/booking/shop-sections/".concat(e),{params:t}),getBookingSchedule:(e,t)=>s.Z.get("/rest/booking/shops/".concat(e),{params:t}),getBookingHistory:e=>s.Z.get("/dashboard/user/my-bookings",{params:e})}},91491:function(e){e.exports={wrapper:"empty_wrapper__nwTin",text:"empty_text__oRHIv",actions:"empty_actions__NNcWA"}},24318:function(e){e.exports={wrapper:"reservationHistoryItem_wrapper__5WHud",title:"reservationHistoryItem_title__PhJUn",text:"reservationHistoryItem_text__Bey1_",badge:"reservationHistoryItem_badge__1PtL4",item:"reservationHistoryItem_item__S8fmr",naming:"reservationHistoryItem_naming__hH8vd"}},24110:function(e){e.exports={root:"orderList_root__9MGvz",shimmer:"orderList_shimmer__NvMqh"}},91249:function(e){e.exports={root:"orders_root__HZblW",wrapper:"orders_wrapper__O2mIT",title:"orders_title__5hdk3",main:"orders_main__MbuRG"}},9008:function(e,t,n){e.exports=n(83121)}},function(e){e.O(0,[6886,9774,2888,179],function(){return e(e.s=20632)}),_N_E=e.O()}]);