(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6216],{31536:function(e,t,o){"use strict";o.d(t,{Z:function(){return z}});var r=o(63366),a=o(87462),n=o(67294),l=o(86010),i=o(59766),c=o(94780),s=o(34867),u=o(70182);let d=(0,u.ZP)();var p=o(29628),h=o(39707),f=o(66500),v=o(95408),m=o(98700),b=o(85893);let y=["component","direction","spacing","divider","children","className","useFlexGap"],g=(0,f.Z)(),w=d("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root});function k(e){return(0,p.Z)({props:e,name:"MuiStack",defaultTheme:g})}let x=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],O=({ownerState:e,theme:t})=>{let o=(0,a.Z)({display:"flex",flexDirection:"column"},(0,v.k9)({theme:t},(0,v.P$)({values:e.direction,breakpoints:t.breakpoints.values}),e=>({flexDirection:e})));if(e.spacing){let r=(0,m.hB)(t),n=Object.keys(t.breakpoints.values).reduce((t,o)=>(("object"==typeof e.spacing&&null!=e.spacing[o]||"object"==typeof e.direction&&null!=e.direction[o])&&(t[o]=!0),t),{}),l=(0,v.P$)({values:e.direction,base:n}),c=(0,v.P$)({values:e.spacing,base:n});"object"==typeof l&&Object.keys(l).forEach((e,t,o)=>{let r=l[e];if(!r){let a=t>0?l[o[t-1]]:"column";l[e]=a}});let s=(t,o)=>e.useFlexGap?{gap:(0,m.NA)(r,t)}:{"& > :not(style) ~ :not(style)":{margin:0,[`margin${x(o?l[o]:e.direction)}`]:(0,m.NA)(r,t)}};o=(0,i.Z)(o,(0,v.k9)({theme:t},c,s))}return(0,v.dt)(t.breakpoints,o)};var Z=o(90948),j=o(71657);let $=function(e={}){let{createStyledComponent:t=w,useThemeProps:o=k,componentName:i="MuiStack"}=e,u=()=>(0,c.Z)({root:["root"]},e=>(0,s.Z)(i,e),{}),d=t(O),p=n.forwardRef(function(e,t){let i=o(e),c=(0,h.Z)(i),{component:s="div",direction:p="column",spacing:f=0,divider:v,children:m,className:g,useFlexGap:w=!1}=c,k=(0,r.Z)(c,y),x=u();return(0,b.jsx)(d,(0,a.Z)({as:s,ownerState:{direction:p,spacing:f,useFlexGap:w},ref:t,className:(0,l.Z)(x.root,g)},k,{children:v?function(e,t){let o=n.Children.toArray(e).filter(Boolean);return o.reduce((e,r,a)=>(e.push(r),a<o.length-1&&e.push(n.cloneElement(t,{key:`separator-${a}`})),e),[])}(m,v):m}))});return p}({createStyledComponent:(0,Z.ZP)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root}),useThemeProps:e=>(0,j.Z)({props:e,name:"MuiStack"})});var z=$},45843:function(e,t,o){"use strict";o.d(t,{Z:function(){return j}});var r=o(63366),a=o(87462),n=o(67294),l=o(86010),i=o(94780),c=o(41796),s=o(98216),u=o(21964),d=o(71657),p=o(90948),h=o(1588),f=o(34867);function v(e){return(0,f.Z)("MuiSwitch",e)}let m=(0,h.Z)("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]);var b=o(85893);let y=["className","color","edge","size","sx"],g=e=>{let{classes:t,edge:o,size:r,color:n,checked:l,disabled:c}=e,u={root:["root",o&&`edge${(0,s.Z)(o)}`,`size${(0,s.Z)(r)}`],switchBase:["switchBase",`color${(0,s.Z)(n)}`,l&&"checked",c&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},d=(0,i.Z)(u,v,t);return(0,a.Z)({},t,d)},w=(0,p.ZP)("span",{name:"MuiSwitch",slot:"Root",overridesResolver(e,t){let{ownerState:o}=e;return[t.root,o.edge&&t[`edge${(0,s.Z)(o.edge)}`],t[`size${(0,s.Z)(o.size)}`]]}})(({ownerState:e})=>(0,a.Z)({display:"inline-flex",width:58,height:38,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"}},"start"===e.edge&&{marginLeft:-8},"end"===e.edge&&{marginRight:-8},"small"===e.size&&{width:40,height:24,padding:7,[`& .${m.thumb}`]:{width:16,height:16},[`& .${m.switchBase}`]:{padding:4,[`&.${m.checked}`]:{transform:"translateX(16px)"}}})),k=(0,p.ZP)(u.Z,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver(e,t){let{ownerState:o}=e;return[t.switchBase,{[`& .${m.input}`]:t.input},"default"!==o.color&&t[`color${(0,s.Z)(o.color)}`]]}})(({theme:e})=>({position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:`${"light"===e.palette.mode?e.palette.common.white:e.palette.grey[300]}`,transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),[`&.${m.checked}`]:{transform:"translateX(20px)"},[`&.${m.disabled}`]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:`${"light"===e.palette.mode?e.palette.grey[100]:e.palette.grey[600]}`},[`&.${m.checked} + .${m.track}`]:{opacity:.5},[`&.${m.disabled} + .${m.track}`]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:`${"light"===e.palette.mode?.12:.2}`},[`& .${m.input}`]:{left:"-100%",width:"300%"}}),({theme:e,ownerState:t})=>(0,a.Z)({"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,c.Fq)(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}}},"default"!==t.color&&{[`&.${m.checked}`]:{color:(e.vars||e).palette[t.color].main,"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t.color].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,c.Fq)(e.palette[t.color].main,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${m.disabled}`]:{color:e.vars?e.vars.palette.Switch[`${t.color}DisabledColor`]:`${"light"===e.palette.mode?(0,c.$n)(e.palette[t.color].main,.62):(0,c._j)(e.palette[t.color].main,.55)}`}},[`&.${m.checked} + .${m.track}`]:{backgroundColor:(e.vars||e).palette[t.color].main}})),x=(0,p.ZP)("span",{name:"MuiSwitch",slot:"Track",overridesResolver:(e,t)=>t.track})(({theme:e})=>({height:"100%",width:"100%",borderRadius:7,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:`${"light"===e.palette.mode?e.palette.common.black:e.palette.common.white}`,opacity:e.vars?e.vars.opacity.switchTrack:`${"light"===e.palette.mode?.38:.3}`})),O=(0,p.ZP)("span",{name:"MuiSwitch",slot:"Thumb",overridesResolver:(e,t)=>t.thumb})(({theme:e})=>({boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"})),Z=n.forwardRef(function(e,t){let o=(0,d.Z)({props:e,name:"MuiSwitch"}),{className:n,color:i="primary",edge:c=!1,size:s="medium",sx:u}=o,p=(0,r.Z)(o,y),h=(0,a.Z)({},o,{color:i,edge:c,size:s}),f=g(h),v=(0,b.jsx)(O,{className:f.thumb,ownerState:h});return(0,b.jsxs)(w,{className:(0,l.Z)(f.root,n),sx:u,ownerState:h,children:[(0,b.jsx)(k,(0,a.Z)({type:"checkbox",icon:v,checkedIcon:v,ref:t,ownerState:h},p,{classes:(0,a.Z)({},f,{root:f.switchBase})})),(0,b.jsx)(x,{className:f.track,ownerState:h})]})});var j=Z},23048:function(e,t,o){"use strict";o.d(t,{w:function(){return x}});var r=o(87462),a=o(67294),n=o(45697),l=o.n(n),i=o(29502),c=o(58493),s=o(63366),u=o(86010),d=o(90948),p=o(60083),h=o(50720),f=o(14198),v=o(67542),m=o(85893);let b=["props","ref"],y=(0,d.ZP)(f.ce)(({theme:e})=>({overflow:"hidden",minWidth:v.Pl,backgroundColor:(e.vars||e).palette.background.paper})),g=e=>{var t;let{props:o,ref:a}=e,n=(0,s.Z)(e,b),{localeText:l,slots:i,slotProps:c,className:d,sx:f,displayStaticWrapperAs:v,autoFocus:g}=o,{layoutProps:w,renderCurrentView:k}=(0,p.Q)((0,r.Z)({},n,{props:o,autoFocusView:null!=g&&g,additionalViewProps:{},wrapperVariant:v})),x=null!=(t=null==i?void 0:i.layout)?t:y,O=()=>{var e,t,o;return(0,m.jsx)(h._,{localeText:l,children:(0,m.jsx)(x,(0,r.Z)({},w,null==c?void 0:c.layout,{slots:i,slotProps:c,sx:[...Array.isArray(f)?f:[f],...Array.isArray(null==c||null==(e=c.layout)?void 0:e.sx)?c.layout.sx:[null==c||null==(t=c.layout)?void 0:t.sx]],className:(0,u.Z)(d,null==c||null==(o=c.layout)?void 0:o.className),ref:a,children:k()}))})};return{renderPicker:O}};var w=o(33088),k=o(55071);let x=a.forwardRef(function(e,t){var o,a,n;let l=(0,i.n)(e,"MuiStaticDatePicker"),s=null!=(o=l.displayStaticWrapperAs)?o:"mobile",u=(0,r.Z)({day:c.z,month:c.z,year:c.z},l.viewRenderers),d=(0,r.Z)({},l,{viewRenderers:u,displayStaticWrapperAs:s,yearsPerRow:null!=(a=l.yearsPerRow)?a:"mobile"===s?3:4,slotProps:(0,r.Z)({},l.slotProps,{toolbar:(0,r.Z)({hidden:"desktop"===s},null==(n=l.slotProps)?void 0:n.toolbar)})}),{renderPicker:p}=g({props:d,valueManager:k.h,valueType:"date",validator:w.q,ref:t});return p()});x.propTypes={autoFocus:l().bool,className:l().string,components:l().object,componentsProps:l().object,dayOfWeekFormatter:l().func,defaultCalendarMonth:l().any,defaultValue:l().any,disabled:l().bool,disableFuture:l().bool,disableHighlightToday:l().bool,disablePast:l().bool,displayStaticWrapperAs:l().oneOf(["desktop","mobile"]),displayWeekNumber:l().bool,fixedWeekNumber:l().number,loading:l().bool,localeText:l().object,maxDate:l().any,minDate:l().any,monthsPerRow:l().oneOf([3,4]),onAccept:l().func,onChange:l().func,onClose:l().func,onError:l().func,onMonthChange:l().func,onViewChange:l().func,onYearChange:l().func,openTo:l().oneOf(["day","month","year"]),orientation:l().oneOf(["landscape","portrait"]),readOnly:l().bool,reduceAnimations:l().bool,renderLoading:l().func,shouldDisableDate:l().func,shouldDisableMonth:l().func,shouldDisableYear:l().func,showDaysOutsideCurrentMonth:l().bool,slotProps:l().object,slots:l().object,sx:l().oneOfType([l().arrayOf(l().oneOfType([l().func,l().object,l().bool])),l().func,l().object]),timezone:l().string,value:l().any,view:l().oneOf(["day","month","year"]),viewRenderers:l().shape({day:l().func,month:l().func,year:l().func}),views:l().arrayOf(l().oneOf(["day","month","year"]).isRequired),yearsPerRow:l().oneOf([3,4])}},9008:function(e,t,o){e.exports=o(83121)},10076:function(e,t,o){"use strict";var r=o(67294),a=r&&"object"==typeof r&&"default"in r?r:{default:r},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},l=function(e,t){var o={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e[r]);return o},i=function(e){var t=e.color,o=e.size,r=void 0===o?24:o,i=(e.children,l(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return a.default.createElement("svg",n({},i,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z"}))},c=a.default.memo?a.default.memo(i):i;e.exports=c},4756:function(e,t,o){"use strict";var r=o(67294),a=r&&"object"==typeof r&&"default"in r?r:{default:r},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},l=function(e,t){var o={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e[r]);return o},i=function(e){var t=e.color,o=e.size,r=void 0===o?24:o,i=(e.children,l(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return a.default.createElement("svg",n({},i,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M7.828 11H20v2H7.828l5.364 5.364-1.414 1.414L4 12l7.778-7.778 1.414 1.414z"}))},c=a.default.memo?a.default.memo(i):i;e.exports=c},44165:function(e,t,o){"use strict";var r=o(67294),a=r&&"object"==typeof r&&"default"in r?r:{default:r},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},l=function(e,t){var o={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e[r]);return o},i=function(e){var t=e.color,o=e.size,r=void 0===o?24:o,i=(e.children,l(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return a.default.createElement("svg",n({},i,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm17 8H4v8h16v-8zm0-2V5H4v4h16zm-6 6h4v2h-4v-2z"}))},c=a.default.memo?a.default.memo(i):i;e.exports=c},24847:function(e,t,o){"use strict";var r=o(67294),a=r&&"object"==typeof r&&"default"in r?r:{default:r},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},l=function(e,t){var o={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e[r]);return o},i=function(e){var t=e.color,o=e.size,r=void 0===o?24:o,i=(e.children,l(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return a.default.createElement("svg",n({},i,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-5-8.5L16 8l-3.5 9.002L11 13l-4-1.5z"}))},c=a.default.memo?a.default.memo(i):i;e.exports=c},4778:function(e,t,o){"use strict";var r=o(67294),a=r&&"object"==typeof r&&"default"in r?r:{default:r},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},l=function(e,t){var o={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e[r]);return o},i=function(e){var t=e.color,o=e.size,r=void 0===o?24:o,i=(e.children,l(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return a.default.createElement("svg",n({},i,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M12.9 6.858l4.242 4.243L7.242 21H3v-4.243l9.9-9.9zm1.414-1.414l2.121-2.122a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414l-2.122 2.121-4.242-4.242z"}))},c=a.default.memo?a.default.memo(i):i;e.exports=c}}]);