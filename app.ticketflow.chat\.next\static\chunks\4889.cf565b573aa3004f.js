(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4889],{32913:function(r,o,e){"use strict";e.d(o,{Z:function(){return f}});var s=e(85893),t=e(67294),a=e(90948),n=e(61903),i=e(87109),d=e(93946),l=e(25039),c=e.n(l),p=e(58773),u=e.n(p);let m=(0,a.ZP)(n.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function f(r){let[o,e]=(0,t.useState)(!1),a=()=>{e(r=>!r)};return(0,s.jsx)(m,{variant:"standard",type:o?"text":"password",InputLabelProps:{shrink:!0},InputProps:{endAdornment:(0,s.jsx)(i.Z,{position:"end",children:(0,s.jsx)(d.Z,{onClick:a,disableRipple:!0,children:o?(0,s.jsx)(u(),{}):(0,s.jsx)(c(),{})})})},...r})}},64889:function(r,o,e){"use strict";e.r(o),e.d(o,{default:function(){return w}});var s=e(85893);e(67294);var t=e(49452),a=e.n(t),n=e(98396),i=e(86886),d=e(32913),l=e(6734),c=e(82175),p=e(77262),u=e(94660),m=e(88767),f=e(45641),v=e(73714),h=e(29969);function w(r){var o,e,t,w,x,_,b,j,g;let{handleClose:y}=r,{t:Z}=(0,l.$G)(),z=(0,n.Z)("(min-width:1140px)"),{user:C}=(0,h.a)(),{mutate:P,isLoading:M}=(0,m.useMutation)({mutationFn:r=>f.Z.passwordUpdate(r),onSuccess(r){(0,v.Vp)(Z("saved")),y()},onError:r=>(0,v.vU)(Z(r.statusCode))}),k=(0,c.TA)({initialValues:{old_password:"",password:"",password_confirmation:""},onSubmit(r,o){let{setSubmitting:e}=o;console.log("values => ",r),P(r)},validate(r){let o={};return r.old_password||(null==C?void 0:C.empty_p)||(o.old_password=Z("required")),r.password?r.password.replace(/\s/g,"").length<6&&(o.password=Z("password.should.contain")):o.password=Z("required"),r.password_confirmation?r.password!==r.password_confirmation&&(o.password_confirmation=Z("passwords.dont.match")):o.password_confirmation=Z("required"),o}});return(0,s.jsxs)("div",{className:a().wrapper,children:[(0,s.jsx)("h1",{className:a().title,children:Z("update.password")}),(0,s.jsx)("form",{className:a().form,onSubmit:k.handleSubmit,children:(0,s.jsxs)(i.ZP,{container:!0,spacing:4,children:[!(null==C?void 0:C.empty_p)&&(0,s.jsxs)(i.ZP,{item:!0,xs:12,md:6,children:[(0,s.jsx)(d.Z,{name:"old_password",label:Z("old.password"),placeholder:Z("type.here"),value:k.values.old_password,onChange:k.handleChange,error:!!k.errors.old_password&&k.touched.old_password}),(0,s.jsx)("div",{style:{color:"red",fontSize:"14px"},children:(null===(o=k.errors)||void 0===o?void 0:o.old_password)&&(null===(e=k.touched)||void 0===e?void 0:e.old_password)?null===(t=k.errors)||void 0===t?void 0:t.old_password:""})]}),(0,s.jsxs)(i.ZP,{item:!0,xs:12,md:6,children:[(0,s.jsx)(d.Z,{name:"password",label:Z("password"),placeholder:Z("type.here"),value:k.values.password,onChange:k.handleChange,error:!!k.errors.password&&k.touched.password}),(0,s.jsx)("div",{style:{color:"red",fontSize:"14px"},children:(null===(w=k.errors)||void 0===w?void 0:w.password)&&(null===(x=k.touched)||void 0===x?void 0:x.password)?null===(_=k.errors)||void 0===_?void 0:_.password:""})]}),(0,s.jsxs)(i.ZP,{item:!0,xs:12,md:6,children:[(0,s.jsx)(d.Z,{name:"password_confirmation",label:Z("password.confirmation"),placeholder:Z("type.here"),value:k.values.password_confirmation,onChange:k.handleChange,error:!!k.errors.password_confirmation&&k.touched.password_confirmation}),(0,s.jsx)("div",{style:{color:"red",fontSize:"14px"},children:(null===(b=k.errors)||void 0===b?void 0:b.password_confirmation)&&(null===(j=k.touched)||void 0===j?void 0:j.password_confirmation)?null===(g=k.errors)||void 0===g?void 0:g.password_confirmation:""})]}),!(null==C?void 0:C.empty_p)&&(0,s.jsx)(i.ZP,{item:!0,xs:12,md:6}),(0,s.jsx)(i.ZP,{item:!0,xs:12,md:6,children:(0,s.jsx)(p.Z,{type:"submit",loading:M,children:Z("save")})}),(0,s.jsx)(i.ZP,{item:!0,xs:12,md:6,mt:z?0:-2,children:(0,s.jsx)(u.Z,{type:"button",onClick:y,children:Z("cancel")})})]})})]})}},49452:function(r){r.exports={wrapper:"profilePassword_wrapper__TkHAE",title:"profilePassword_title__EgJa4",form:"profilePassword_form__MAWkK"}},25039:function(r,o,e){"use strict";var s=e(67294),t=s&&"object"==typeof s&&"default"in s?s:{default:s},a=Object.assign||function(r){for(var o=1;o<arguments.length;o++){var e=arguments[o];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r[s]=e[s])}return r},n=function(r,o){var e={};for(var s in r)!(o.indexOf(s)>=0)&&Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s]);return e},i=function(r){var o=r.color,e=r.size,s=void 0===e?24:e,i=(r.children,n(r,["color","size","children"])),d="remixicon-icon "+(i.className||"");return t.default.createElement("svg",a({},i,{className:d,width:s,height:s,fill:void 0===o?"currentColor":o,viewBox:"0 0 24 24"}),t.default.createElement("path",{d:"M12 3c5.392 0 9.878 3.88 10.819 9-.94 5.12-5.427 9-10.819 9-5.392 0-9.878-3.88-10.819-9C2.121 6.88 6.608 3 12 3zm0 16a9.005 9.005 0 0 0 8.777-7 9.005 9.005 0 0 0-17.554 0A9.005 9.005 0 0 0 12 19zm0-2.5a4.5 4.5 0 1 1 0-9 4.5 4.5 0 0 1 0 9zm0-2a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"}))},d=t.default.memo?t.default.memo(i):i;r.exports=d},58773:function(r,o,e){"use strict";var s=e(67294),t=s&&"object"==typeof s&&"default"in s?s:{default:s},a=Object.assign||function(r){for(var o=1;o<arguments.length;o++){var e=arguments[o];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r[s]=e[s])}return r},n=function(r,o){var e={};for(var s in r)!(o.indexOf(s)>=0)&&Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s]);return e},i=function(r){var o=r.color,e=r.size,s=void 0===e?24:e,i=(r.children,n(r,["color","size","children"])),d="remixicon-icon "+(i.className||"");return t.default.createElement("svg",a({},i,{className:d,width:s,height:s,fill:void 0===o?"currentColor":o,viewBox:"0 0 24 24"}),t.default.createElement("path",{d:"M17.882 19.297A10.949 10.949 0 0 1 12 21c-5.392 0-9.878-3.88-10.819-9a10.982 10.982 0 0 1 3.34-6.066L1.392 2.808l1.415-1.415 19.799 19.8-1.415 1.414-3.31-3.31zM5.935 7.35A8.965 8.965 0 0 0 3.223 12a9.005 9.005 0 0 0 13.201 5.838l-2.028-2.028A4.5 4.5 0 0 1 8.19 9.604L5.935 7.35zm6.979 6.978l-3.242-3.242a2.5 2.5 0 0 0 3.241 3.241zm7.893 2.264l-1.431-1.43A8.935 8.935 0 0 0 20.777 12 9.005 9.005 0 0 0 9.552 5.338L7.974 3.76C9.221 3.27 10.58 3 12 3c5.392 0 9.878 3.88 10.819 9a10.947 10.947 0 0 1-2.012 4.592zm-9.084-9.084a4.5 4.5 0 0 1 4.769 4.769l-4.77-4.769z"}))},d=t.default.memo?t.default.memo(i):i;r.exports=d}}]);