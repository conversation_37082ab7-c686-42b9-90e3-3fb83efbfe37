exports.id = 4770;
exports.ids = [4770];
exports.modules = {

/***/ 36643:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "autoRepeatOrder_wrapper__7aQUQ",
	"title": "autoRepeatOrder_title__TrhO5",
	"body": "autoRepeatOrder_body__kKopM",
	"item": "autoRepeatOrder_item__KE0ND"
};


/***/ }),

/***/ 17498:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ AutoRepeatOrder)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _autoRepeatOrder_module_scss__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(36643);
/* harmony import */ var _autoRepeatOrder_module_scss__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_autoRepeatOrder_module_scss__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57987);
/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2296);
/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(formik__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1635);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(73280);
/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(85753);
/* harmony import */ var _mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(10298);
/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var components_button_primaryButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(77262);
/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(74621);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var services_order__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(94098);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_1__, components_alert_toast__WEBPACK_IMPORTED_MODULE_8__, services_order__WEBPACK_IMPORTED_MODULE_10__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_1__, components_alert_toast__WEBPACK_IMPORTED_MODULE_8__, services_order__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);












function AutoRepeatOrder({ orderId , onClose  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();
    const { i18n  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();
    const locale = i18n.language;
    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_9__.useQueryClient)();
    const { isLoading , mutate  } = (0,react_query__WEBPACK_IMPORTED_MODULE_9__.useMutation)({
        mutationFn: (data)=>services_order__WEBPACK_IMPORTED_MODULE_10__/* ["default"].autoRepeat */ .Z.autoRepeat(data.orderId, data.data),
        onSuccess: ()=>{
            queryClient.invalidateQueries([
                "order",
                orderId,
                locale
            ]);
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_8__/* .success */ .Vp)(t("auto.repeat.order.success"));
        },
        onError: (err)=>{
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_8__/* .error */ .vU)(err?.data?.message || t("auto.repeat.order.error"));
        },
        onSettled: ()=>{
            onClose();
        }
    });
    const formik = (0,formik__WEBPACK_IMPORTED_MODULE_2__.useFormik)({
        initialValues: {
            from: dayjs__WEBPACK_IMPORTED_MODULE_3___default()().add(1, "day").format("YYYY-MM-DD"),
            to: dayjs__WEBPACK_IMPORTED_MODULE_3___default()().add(2, "day").format("YYYY-MM-DD")
        },
        onSubmit: (values)=>{
            if (dayjs__WEBPACK_IMPORTED_MODULE_3___default()(values?.from).isAfter(dayjs__WEBPACK_IMPORTED_MODULE_3___default()(values?.to))) {
                return (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_8__/* .error */ .vU)(t("start.date.should.be.before.end.date"));
            }
            mutate({
                orderId,
                data: values
            });
        }
    });
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        className: (_autoRepeatOrder_module_scss__WEBPACK_IMPORTED_MODULE_11___default().wrapper),
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("form", {
            id: "autoRepeatOrder",
            onSubmit: formik.handleSubmit,
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h1", {
                    className: (_autoRepeatOrder_module_scss__WEBPACK_IMPORTED_MODULE_11___default().title),
                    children: t("select.dates.for.auto.repeat")
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    className: (_autoRepeatOrder_module_scss__WEBPACK_IMPORTED_MODULE_11___default().body),
                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_5__.LocalizationProvider, {
                        dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_6__.AdapterDayjs,
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_4__.DatePicker, {
                                label: t("start.date"),
                                disablePast: true,
                                value: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(formik.values.from),
                                onChange: (event)=>{
                                    formik.setFieldValue("from", dayjs__WEBPACK_IMPORTED_MODULE_3___default()(event).format("YYYY-MM-DD"));
                                },
                                format: "YYYY-MM-DD",
                                className: (_autoRepeatOrder_module_scss__WEBPACK_IMPORTED_MODULE_11___default().item)
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_4__.DatePicker, {
                                label: t("end.date"),
                                disablePast: true,
                                value: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(formik.values.to),
                                onChange: (event)=>{
                                    formik.setFieldValue("to", dayjs__WEBPACK_IMPORTED_MODULE_3___default()(event).format("YYYY-MM-DD"));
                                },
                                format: "YYYY-MM-DD",
                                className: (_autoRepeatOrder_module_scss__WEBPACK_IMPORTED_MODULE_11___default().item)
                            })
                        ]
                    })
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_button_primaryButton__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                    type: "submit",
                    loading: isLoading,
                    children: t("submit")
                })
            ]
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 44770:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ AutoRepeatOrderContainer)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var containers_modal_modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(47567);
/* harmony import */ var containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(30182);
/* harmony import */ var components_autoRepeatOrder_autoRepeatOrder__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(17498);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_autoRepeatOrder_autoRepeatOrder__WEBPACK_IMPORTED_MODULE_4__]);
components_autoRepeatOrder_autoRepeatOrder__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];






function AutoRepeatOrderContainer({ open , onClose  }) {
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_1__.useMediaQuery)("(min-width:1140px)");
    const { query  } = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();
    const orderId = Number(query.id);
    if (isDesktop) {
        return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_modal_modal__WEBPACK_IMPORTED_MODULE_2__["default"], {
            open: open,
            onClose: onClose,
            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_autoRepeatOrder_autoRepeatOrder__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
                orderId: orderId,
                onClose: onClose
            })
        });
    }
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_3__["default"], {
        open: open,
        onClose: onClose,
        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_autoRepeatOrder_autoRepeatOrder__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
            orderId: orderId,
            onClose: onClose
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;