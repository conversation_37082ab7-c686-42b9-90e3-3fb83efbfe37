{"version": 1, "config": {"env": {}, "webpackDevMiddleware": null, "eslint": {"ignoreDuringBuilds": false}, "typescript": {"ignoreBuildErrors": false, "tsconfigPath": "tsconfig.json"}, "distDir": ".next", "cleanDistDir": true, "assetPrefix": "", "configOrigin": "next.config.js", "useFileSystemPublicRoutes": true, "generateEtags": true, "pageExtensions": ["tsx", "ts", "jsx", "js"], "target": "server", "poweredByHeader": true, "compress": true, "analyticsId": "", "images": {"deviceSizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840], "imageSizes": [16, 32, 48, 64, 96, 128, 256, 384], "path": "/_next/image", "loader": "default", "loaderFile": "", "domains": [], "disableStaticImages": false, "minimumCacheTTL": 3600, "formats": ["image/webp"], "dangerouslyAllowSVG": true, "contentSecurityPolicy": "default-src 'self'; script-src 'none'; sandbox;", "remotePatterns": [{"protocol": "http", "hostname": "localhost", "port": "8000"}, {"protocol": "https", "hostname": "demo-api.foodyman.org"}, {"protocol": "https", "hostname": "lh3.googleusercontent.com"}, {"protocol": "https", "hostname": "app.ticketflow.chat"}], "unoptimized": true}, "devIndicators": {"buildActivity": true, "buildActivityPosition": "bottom-right"}, "onDemandEntries": {"maxInactiveAge": 25000, "pagesBufferLength": 2}, "amp": {"canonicalBase": ""}, "basePath": "", "sassOptions": {}, "trailingSlash": false, "i18n": null, "productionBrowserSourceMaps": false, "optimizeFonts": true, "excludeDefaultMomentLocales": true, "serverRuntimeConfig": {}, "publicRuntimeConfig": {}, "reactStrictMode": false, "httpAgentOptions": {"keepAlive": true}, "outputFileTracing": true, "staticPageGenerationTimeout": 60, "swcMinify": true, "experimental": {"optimisticClientCache": true, "manualClientBasePath": false, "legacyBrowsers": false, "newNextLinkBehavior": true, "cpus": 15, "sharedPool": true, "profiling": false, "isrFlushToDisk": true, "workerThreads": false, "pageEnv": false, "optimizeCss": false, "nextScriptWorkers": false, "scrollRestoration": false, "externalDir": false, "disableOptimizedLoading": false, "gzipSize": true, "swcFileReading": true, "craCompat": false, "esmExternals": true, "appDir": false, "isrMemoryCacheSize": 52428800, "fullySpecified": false, "outputFileTracingRoot": "", "swcTraceProfiling": false, "forceSwcTransforms": false, "largePageDataBytes": 128000, "enableUndici": false, "adjustFontFallbacks": false, "adjustFontFallbacksWithSizeAdjust": false, "trustHostHeader": false}, "configFileName": "next.config.js"}, "appDir": "C:\\OSPanel\\home\\app.ticketflow.chat", "files": [".next\\routes-manifest.json", ".next\\server\\pages-manifest.json", ".next\\build-manifest.json", ".next\\prerender-manifest.json", ".next\\server\\middleware-manifest.json", ".next\\react-loadable-manifest.json", ".next\\server\\font-manifest.json", ".next\\BUILD_ID"], "ignore": ["node_modules\\next\\dist\\compiled\\@ampproject\\toolbox-optimizer\\**\\*"]}