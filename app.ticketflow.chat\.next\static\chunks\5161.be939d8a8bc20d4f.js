(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5161],{92244:function(e,s,a){"use strict";a.d(s,{Z:function(){return l}});var n=a(85893);a(67294);var r=a(82343),t=a.n(r),i=a(95785),o=a(37562);function l(e){var s;let{data:a,size:r="medium"}=e;return(0,n.jsx)("div",{className:"".concat(t().logo," ").concat(t()[r]),children:(0,n.jsx)("span",{className:t().wrapper,children:(0,n.jsx)(o.Z,{fill:!0,src:(0,i.Z)(a.logo_img),alt:null===(s=a.translation)||void 0===s?void 0:s.title,sizes:"(max-width: 768px) 40px, 60px",quality:90})})})}},83626:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});var n=a(85893),r=a(6684);function t(){return(0,n.jsx)("span",{style:{display:"block",minWidth:"16px",height:"auto"},children:(0,n.jsx)(r.SA,{})})}},25161:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return w}});var n=a(85893),r=a(67294),t=a(10555),i=a.n(t),o=a(41664),l=a.n(o),c=a(97750),d=a.n(c),h=a(92244),_=a(6734),p=a(95785),u=a(37562),m=a(83626);function x(e){var s,a;let{data:r}=e,{t}=(0,_.$G)();return(0,n.jsxs)(l(),{href:"/restaurant/".concat(r.id),className:d().wrapper,children:[(0,n.jsxs)("div",{className:d().header,children:[(0,n.jsx)(h.Z,{data:r,size:"small"}),(0,n.jsxs)("h4",{className:d().shopTitle,children:[null===(s=r.translation)||void 0===s?void 0:s.title,(0,n.jsx)(m.Z,{})]})]}),(0,n.jsx)(u.Z,{fill:!0,src:(0,p.Z)(r.background_img),alt:null===(a=r.translation)||void 0===a?void 0:a.title,sizes:"400px"}),(0,n.jsx)("div",{className:d().badge,children:(0,n.jsx)("span",{className:d().text,children:t("number.of.foods",{count:r.products_count||0})})})]})}var f=a(30719),j=a(88078),g=a(97169),v=a.n(g),C=a(71350),N=a.n(C);let b={spaceBetween:10,preloadImages:!1,className:"featured-shops full-width",breakpoints:{1140:{slidesPerView:6,spaceBetween:30}}};function w(e){let{title:s,featuredShops:a,loading:t=!1}=e,[o,l]=(0,r.useState)(),c=()=>{null==o||o.slideNext()},d=()=>{null==o||o.slidePrev()};return(0,n.jsx)("section",{className:"container",style:{display:t||0!==a.length?"block":"none"},children:(0,n.jsxs)("div",{className:i().container,children:[(0,n.jsxs)("div",{className:i().header,children:[(0,n.jsx)("h2",{className:i().title,children:s}),a.length>6&&(0,n.jsxs)("div",{className:i().actions,children:[(0,n.jsx)("button",{className:i().btn,onClick:d,children:(0,n.jsx)(N(),{})}),(0,n.jsx)("button",{className:i().btn,onClick:c,children:(0,n.jsx)(v(),{})})]})]}),t?(0,n.jsx)("div",{className:i().shimmerContainer,children:Array.from(Array(6)).map((e,s)=>(0,n.jsx)(j.Z,{variant:"rectangular",className:i().shimmer},"recomended"+s))}):(0,n.jsx)(f.tq,{...b,slidesPerView:"auto",onSwiper:l,children:a.map(e=>(0,n.jsx)(f.o5,{children:(0,n.jsx)(x,{data:e})},e.id))})]})})}},97750:function(e){e.exports={wrapper:"shopHeroCard_wrapper__Uue2F",header:"shopHeroCard_header__zn_aO",shopTitle:"shopHeroCard_shopTitle__IHt8K",badge:"shopHeroCard_badge__1_Qbh",text:"shopHeroCard_text__3Z2TS"}},82343:function(e){e.exports={logo:"shopLogo_logo__RFCaX",small:"shopLogo_small__i3Fyo",medium:"shopLogo_medium__H_Sj8",large:"shopLogo_large__kA_9P",wrapper:"shopLogo_wrapper__f0LZd"}},10555:function(e){e.exports={container:"featuredShopsContainer_container__xCWwg",header:"featuredShopsContainer_header__74dRF",title:"featuredShopsContainer_title__ix0Xp",actions:"featuredShopsContainer_actions__sLHID",btn:"featuredShopsContainer_btn__IdiIR",shimmerContainer:"featuredShopsContainer_shimmerContainer__VT_Vv",shimmer:"featuredShopsContainer_shimmer__c1Dyj"}}}]);