{"/_app": "pages/_app.js", "/_error": "pages/_error.js", "/_document": "pages/_document.js", "/404": "pages/404.js", "/ads": "pages/ads.js", "/api/hello": "pages/api/hello.js", "/be-seller": "pages/be-seller.js", "/brands": "pages/brands.js", "/careers": "pages/careers.js", "/liked": "pages/liked.js", "/login": "pages/login.js", "/order-refunds": "pages/order-refunds.js", "/orders/[id]": "pages/orders/[id].js", "/parcel-checkout": "pages/parcel-checkout.js", "/parcels": "pages/parcels.js", "/parcels/[id]": "pages/parcels/[id].js", "/profile": "pages/profile.js", "/promotion": "pages/promotion.js", "/referrals": "pages/referrals.js", "/orders": "pages/orders.js", "/register": "pages/register.js", "/reservations": "pages/reservations.js", "/recipes": "pages/recipes.js", "/reset-password": "pages/reset-password.js", "/settings/notification": "pages/settings/notification.js", "/shop-category": "pages/shop-category.js", "/test-final-icons": "pages/test-final-icons.js", "/update-password": "pages/update-password.js", "/verify-phone": "pages/verify-phone.js", "/update-details": "pages/update-details.js", "/wallet": "pages/wallet.js", "/about": "pages/about.js", "/blog/[id]": "pages/blog/[id].js", "/ads/[id]": "pages/ads/[id].js", "/careers/[id]": "pages/careers/[id].js", "/blog": "pages/blog.js", "/deliver": "pages/deliver.js", "/": "pages/index.js", "/help": "pages/help.js", "/privacy": "pages/privacy.js", "/group/[id]": "pages/group/[id].js", "/restaurant/[id]/checkout": "pages/restaurant/[id]/checkout.js", "/recipes/[id]": "pages/recipes/[id].js", "/reservations/[id]": "pages/reservations/[id].js", "/shop-category/[id]": "pages/shop-category/[id].js", "/saved-locations": "pages/saved-locations.js", "/terms": "pages/terms.js", "/promotion/[id]": "pages/promotion/[id].js", "/referral-terms": "pages/referral-terms.js", "/shop/[id]": "pages/shop/[id].js", "/welcome": "pages/welcome.js", "/shop": "pages/shop.js", "/restaurant/[id]": "pages/restaurant/[id].js"}