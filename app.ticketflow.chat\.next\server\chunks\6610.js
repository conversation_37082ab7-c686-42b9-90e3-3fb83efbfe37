"use strict";
exports.id = 6610;
exports.ids = [6610];
exports.modules = {

/***/ 56610:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Homev1)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57987);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var services_shop__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1612);
/* harmony import */ var services_category__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(56457);
/* harmony import */ var redux_slices_shopFilter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(5215);
/* harmony import */ var hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(34349);
/* harmony import */ var services_story__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(13443);
/* harmony import */ var services_banner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(94910);
/* harmony import */ var hooks_useUserLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(2950);
/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(87104);
/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(qs__WEBPACK_IMPORTED_MODULE_13__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_4__, services_shop__WEBPACK_IMPORTED_MODULE_6__, services_category__WEBPACK_IMPORTED_MODULE_7__, services_story__WEBPACK_IMPORTED_MODULE_10__, services_banner__WEBPACK_IMPORTED_MODULE_11__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_4__, services_shop__WEBPACK_IMPORTED_MODULE_6__, services_category__WEBPACK_IMPORTED_MODULE_7__, services_story__WEBPACK_IMPORTED_MODULE_10__, services_banner__WEBPACK_IMPORTED_MODULE_11__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);














const Empty = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>__webpack_require__.e(/* import() */ 520).then(__webpack_require__.bind(__webpack_require__, 20520)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "components/empty/empty"
        ]
    }
});
const Loader = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.resolve(/* import() */).then(__webpack_require__.bind(__webpack_require__, 37935)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "components/loader/loader"
        ]
    }
});
const BannerContainer = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>__webpack_require__.e(/* import() */ 5613).then(__webpack_require__.bind(__webpack_require__, 75613)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "containers/banner/banner"
        ]
    }
});
const FeaturedShopsContainer = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/* import() */[__webpack_require__.e(6684), __webpack_require__.e(8341)]).then(__webpack_require__.bind(__webpack_require__, 68341)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "containers/featuredShopsContainer/featuredShopsContainer"
        ]
    }
});
const StoreList = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/* import() */[__webpack_require__.e(6684), __webpack_require__.e(1880)]).then(__webpack_require__.bind(__webpack_require__, 21880)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "containers/storeList/storeList"
        ]
    }
});
const ZoneNotFound = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>__webpack_require__.e(/* import() */ 3135).then(__webpack_require__.bind(__webpack_require__, 3135)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "components/zoneNotFound/zoneNotFound"
        ]
    }
});
const NewsContainer = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/* import() */[__webpack_require__.e(182), __webpack_require__.e(5443)]).then(__webpack_require__.bind(__webpack_require__, 75443)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "containers/newsContainer/newsContainer"
        ]
    }
});
const ShopList = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/* import() */[__webpack_require__.e(6684), __webpack_require__.e(3444), __webpack_require__.e(2554), __webpack_require__.e(1855), __webpack_require__.e(2693)]).then(__webpack_require__.bind(__webpack_require__, 62693)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "containers/shopList/shopList"
        ]
    }
});
const ShopCategoryList = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>__webpack_require__.e(/* import() */ 7345).then(__webpack_require__.bind(__webpack_require__, 27345)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "containers/shopCategoryList/v1"
        ]
    }
});
const AdList = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>__webpack_require__.e(/* import() */ 8394).then(__webpack_require__.bind(__webpack_require__, 88394)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "containers/adList/v1"
        ]
    }
});
const BrandShopList = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/* import() */[__webpack_require__.e(6684), __webpack_require__.e(358)]).then(__webpack_require__.bind(__webpack_require__, 358)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\homev1\\homev1.tsx -> " + "containers/brandShopList/v1"
        ]
    }
});
const PER_PAGE = 12;
function Homev1() {
    const { t , i18n  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();
    const locale = i18n.language;
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_5__.useMediaQuery)("(min-width:1140px)");
    const loader = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const { category_id , newest , order_by , group  } = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__/* .useAppSelector */ .C)(redux_slices_shopFilter__WEBPACK_IMPORTED_MODULE_8__/* .selectShopFilter */ .qs);
    const isFilterActive = !!Object.keys(group).length;
    const location = (0,hooks_useUserLocation__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z)();
    const { data: shopCategoryList , isLoading: shopCategoryLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "shopcategory",
        locale
    ], ()=>services_category__WEBPACK_IMPORTED_MODULE_7__/* ["default"].getAllShopCategories */ .Z.getAllShopCategories({
            perPage: 20
        }));
    const { data: stories , isLoading: isStoriesLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "stories",
        locale
    ], ()=>services_story__WEBPACK_IMPORTED_MODULE_10__/* ["default"].getAll */ .Z.getAll());
    const { data: banners , isLoading: isBannerLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "banners",
        locale
    ], ()=>services_banner__WEBPACK_IMPORTED_MODULE_11__/* ["default"].getAll */ .Z.getAll());
    const { isSuccess: isInsideZone , isLoading: isZoneLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "shopZones",
        location
    ], ()=>services_shop__WEBPACK_IMPORTED_MODULE_6__/* ["default"].checkZone */ .Z.checkZone({
            address: location
        }));
    const { data: shops , isLoading: isShopLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "shops",
        location,
        locale
    ], ()=>services_shop__WEBPACK_IMPORTED_MODULE_6__/* ["default"].getAllShops */ .Z.getAllShops(qs__WEBPACK_IMPORTED_MODULE_13___default().stringify({
            perPage: PER_PAGE,
            address: location,
            open: 1
        })));
    const { data , error , fetchNextPage , hasNextPage , isFetchingNextPage , isLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useInfiniteQuery)([
        "restaurants",
        category_id,
        locale,
        order_by,
        group,
        location,
        newest
    ], ({ pageParam =1  })=>services_shop__WEBPACK_IMPORTED_MODULE_6__/* ["default"].getAllRestaurants */ .Z.getAllRestaurants(qs__WEBPACK_IMPORTED_MODULE_13___default().stringify({
            page: pageParam,
            perPage: PER_PAGE,
            category_id: category_id || undefined,
            order_by: newest ? "new" : order_by,
            free_delivery: group.free_delivery,
            take: group.tag,
            rating: group.rating?.split(","),
            prices: group.prices,
            address: location,
            open: Number(group.open) || undefined,
            deals: group.deals
        })), {
        getNextPageParam: (lastPage)=>{
            if (lastPage.meta.current_page < lastPage.meta.last_page) {
                return lastPage.meta.current_page + 1;
            }
            return undefined;
        }
    });
    const restaurants = data?.pages?.flatMap((item)=>item.data) || [];
    const { data: recommendedShops , isLoading: recommendedLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "recommendedShops",
        locale,
        location
    ], ()=>services_shop__WEBPACK_IMPORTED_MODULE_6__/* ["default"].getRecommended */ .Z.getRecommended({
            address: location
        }));
    const { data: ads , isLoading: adListLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "ads",
        locale,
        location
    ], ()=>services_banner__WEBPACK_IMPORTED_MODULE_11__/* ["default"].getAllAds */ .Z.getAllAds({
            perPage: 6,
            address: location
        }));
    const { data: brandShops , isLoading: brandShopLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "brandshops",
        locale,
        location
    ], ()=>services_shop__WEBPACK_IMPORTED_MODULE_6__/* ["default"].getAllShops */ .Z.getAllShops(qs__WEBPACK_IMPORTED_MODULE_13___default().stringify({
            verify: "1",
            address: location
        })));
    const handleObserver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entries)=>{
        const target = entries[0];
        if (target.isIntersecting && hasNextPage) {
            fetchNextPage();
        }
    }, [
        fetchNextPage,
        hasNextPage
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const option = {
            root: null,
            rootMargin: "20px",
            threshold: 0
        };
        const observer = new IntersectionObserver(handleObserver, option);
        if (loader.current) observer.observe(loader.current);
    }, [
        handleObserver,
        hasNextPage,
        fetchNextPage
    ]);
    if (error) {
        console.log("error => ", error);
    }
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ShopCategoryList, {
                data: shopCategoryList?.data?.sort((a, b)=>a?.input - b?.input) || [],
                loading: shopCategoryLoading
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(BannerContainer, {
                stories: stories || [],
                banners: banners?.data || [],
                loadingStory: isStoriesLoading,
                loadingBanner: isBannerLoading
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(StoreList, {
                title: t("shops"),
                shops: shops?.data || [],
                loading: isShopLoading
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(AdList, {
                data: ads?.data,
                loading: adListLoading
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(BrandShopList, {
                data: brandShops?.data || [],
                loading: brandShopLoading
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                style: {
                    minHeight: "60vh"
                },
                children: [
                    !category_id && !newest && !isFilterActive && isInsideZone && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(FeaturedShopsContainer, {
                        title: t("recommended"),
                        featuredShops: recommendedShops?.data || [],
                        loading: recommendedLoading
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ShopList, {
                        title: newest ? t("news.week") : t("all.restaurants"),
                        shops: restaurants,
                        loading: isLoading && !isFetchingNextPage
                    }),
                    isFetchingNextPage && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(Loader, {}),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        ref: loader
                    }),
                    !isInsideZone && !isZoneLoading && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ZoneNotFound, {}),
                    !restaurants.length && !isLoading && isInsideZone && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(Empty, {
                        text: t("no.restaurants")
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(NewsContainer, {})
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 94910:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const bannerService = {
    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/banners/paginate`, {
            params
        }),
    getById: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/banners/${id}`, {
            params
        }),
    getAllAds: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get("/rest/banners-ads", {
            params
        }),
    getAdById: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/banners-ads/${id}`, {
            params
        })
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bannerService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 56457:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const categoryService = {
    getAllShopCategories: (params = {})=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/categories/paginate`, {
            params: {
                ...params,
                type: "shop"
            }
        }),
    getAllSubCategories: (categoryId, params = {})=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`rest/categories/sub-shop/${categoryId}`, {
            params
        }),
    getAllProductCategories: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/shops/${id}/categories`, {
            params
        }),
    getAllRecipeCategories: (params = {})=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/categories/paginate`, {
            params: {
                ...params,
                type: "receipt"
            }
        }),
    getById: (id, params = {})=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/categories/${id}`, {
            params
        })
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (categoryService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 13443:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const storyService = {
    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/stories/paginate`, {
            params
        })
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (storyService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;