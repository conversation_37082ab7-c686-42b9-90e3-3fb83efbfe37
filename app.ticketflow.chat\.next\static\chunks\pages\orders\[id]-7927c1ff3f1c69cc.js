(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3750,6060],{51395:function(e,l,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/orders/[id]",function(){return n(5344)}])},54215:function(e,l,n){"use strict";n.d(l,{Z:function(){return i}});var s=n(85893);n(67294);var t=n(6734),a=n(90026);function i(e){var l,n;let{data:i}=e,{t:d}=(0,t.$G)();return(0,s.jsxs)("div",{children:[d("under")," ","sum"===i.type?(0,s.jsx)(a.Z,{number:i.value}):i.value," +"," ",d("bonus")," ",null===(n=null===(l=i.bonusStock)||void 0===l?void 0:l.product.translation)||void 0===n?void 0:n.title]})}},75619:function(e,l,n){"use strict";n.d(l,{Z:function(){return d}});var s=n(85893);n(67294);var t=n(98456),a=n(78179),i=n.n(a);function d(e){let{}=e;return(0,s.jsx)("div",{className:i().loading,children:(0,s.jsx)(t.Z,{})})}},60104:function(e,l,n){"use strict";n.d(l,{T:function(){return t},v:function(){return s}});let s=[5,10,15,20,25],t=["system","driver"]},56060:function(e,l,n){"use strict";n.r(l),n.d(l,{default:function(){return d}});var s=n(85893);n(67294);var t=n(14564),a=n(90948);let i=(0,a.ZP)(t.ZP)(()=>({"& .MuiBackdrop-root":{backgroundColor:"rgba(0, 0, 0, 0)"},"& .MuiPaper-root":{backgroundColor:"var(--secondary-bg)",boxShadow:"var(--popover-box-shadow)",borderRadius:"10px",maxWidth:"100%"}}));function d(e){let{children:l,...n}=e;return(0,s.jsx)(i,{anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"},...n,children:l})}},5344:function(e,l,n){"use strict";n.r(l),n.d(l,{default:function(){return ll}});var s=n(85893),t=n(67294),a=n(84169),i=n(92490),d=n.n(i),r=n(6734),o=n(45122),c=n(14621),u=n(57249),m=n(19370),v=n(44472),p=n(83578),x=n(90948),h=n(75335),j=n.n(h),_=n(99954),b=n.n(_),y=n(42262),f=n.n(y),N=n(82128),g=n.n(N);let Z=(0,x.ZP)(c.Z)(e=>{let{theme:l}=e;return{["&.".concat(u.Z.alternativeLabel)]:{top:31,"@media (max-width: 576px)":{top:20}},["&.".concat(u.Z.active)]:{["& .".concat(u.Z.line)]:{backgroundColor:"#83EA00"}},["&.".concat(u.Z.completed)]:{["& .".concat(u.Z.line)]:{backgroundColor:"#83EA00"}},["& .".concat(u.Z.line)]:{height:8,border:0,backgroundColor:"var(--secondary-bg)",borderRadius:1,"@media (max-width: 576px)":{height:5}}}}),w=(0,x.ZP)("div")(e=>{let{theme:l,ownerState:n}=e;return{backgroundColor:"var(--secondary-bg)",zIndex:1,color:"#fff",width:70,height:70,display:"flex",borderRadius:"50%",justifyContent:"center",alignItems:"center","@media (max-width: 576px)":{width:44,height:44},"& svg":{width:28,height:28,fill:"#898989","@media (max-width: 576px)":{width:17,height:17}},...n.active&&{backgroundColor:"#83EA00","& svg":{fill:"#232B2F"}},...n.completed&&{backgroundColor:"#83EA00","& svg":{fill:"#232B2F"}}}});function k(e){let{active:l,completed:n,className:t}=e,a={1:(0,s.jsx)(j(),{}),2:(0,s.jsx)(b(),{}),3:(0,s.jsx)(f(),{}),4:(0,s.jsx)(g(),{})};return(0,s.jsx)(w,{ownerState:{completed:n,active:l},className:t,children:a[String(e.icon)]})}let C=["accepted","ready","on_a_way","delivered"],I=e=>{switch(e){case"accepted":return 0;case"ready":case"cooking":return 1;case"on_a_way":return 2;case"delivered":return 3;default:return -1}};function P(e){let{status:l}=e;return(0,s.jsx)(m.Z,{alternativeLabel:!0,activeStep:I(l),connector:(0,s.jsx)(Z,{}),children:C.map(e=>(0,s.jsx)(v.Z,{children:(0,s.jsx)(p.Z,{StepIconComponent:k})},e))})}var M=n(27484),F=n.n(M),T=n(88078),S=n(54215);function W(e){var l,n,t;let{data:a,loading:i=!1}=e,{t:c}=(0,r.$G)();return(0,s.jsx)("div",{className:d().root,children:(0,s.jsx)("div",{className:"container",children:(0,s.jsxs)("div",{className:d().wrapper,children:[(0,s.jsxs)("div",{className:d().shopInfo,children:[(0,s.jsx)(o.Z,{data:null==a?void 0:a.shop,loading:i}),i?(0,s.jsxs)("div",{className:d().naming,children:[(0,s.jsx)(T.Z,{variant:"text",className:d().shimmerTitle}),(0,s.jsx)(T.Z,{variant:"text",className:d().shimmerDesc})]}):(0,s.jsxs)("div",{className:d().naming,children:[(0,s.jsx)("h1",{className:d().title,children:null===(l=null==a?void 0:a.shop.translation)||void 0===l?void 0:l.title}),(0,s.jsx)("p",{className:d().text,children:(null==a?void 0:a.shop.bonus)?(0,s.jsx)(S.Z,{data:null==a?void 0:null===(n=a.shop)||void 0===n?void 0:n.bonus}):null===(t=null==a?void 0:a.shop.translation)||void 0===t?void 0:t.description})]})]}),(0,s.jsx)("div",{className:d().statusWrapper,children:i?(0,s.jsx)(T.Z,{variant:"rectangular",className:d().shimmer}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:d().status,children:[(0,s.jsx)("label",{children:c(null==a?void 0:a.status)}),(0,s.jsx)("div",{className:d().time,children:(0,s.jsx)("span",{className:d().text,children:F()(null==a?void 0:a.updated_at).format("HH:mm")})})]}),(0,s.jsx)(P,{status:(null==a?void 0:a.status)||""})]})})]})})})}var D=n(55642),E=n(98396),G=n(86886),L=n(66776),B=n.n(L),H=n(35603),z=n.n(H),A=n(95785),R=n(90026),q=n(37562);function Q(e){var l,n,t,a,i,d,o,c,u,m,v,p,x,h,j,_,b,y,f;let{data:N,order:g}=e,{t:Z}=(0,r.$G)(),{addonsTotal:w,totalPrice:k,oldPrice:C}=function(e){if(!e)return{addonsTotal:0,productTotal:0,totalPrice:0,oldPrice:0};let l=e.addons.reduce((e,l)=>e+=l.total_price,0),n=e.total_price,s=n+l,t=s+e.discount;return{addonsTotal:l,productTotal:n,totalPrice:s,oldPrice:t}}(N);return(0,s.jsxs)("div",{className:z().row,children:[(0,s.jsxs)("div",{className:z().col,children:[(0,s.jsxs)("h4",{className:z().title,children:[null===(l=N.stock)||void 0===l?void 0:null===(n=l.product)||void 0===n?void 0:null===(t=n.translation)||void 0===t?void 0:t.title,(null===(a=N.stock)||void 0===a?void 0:a.extras)?N.stock.extras.map((e,l)=>(0,s.jsxs)("span",{children:["(",e.value,")"]},"extra"+l)):"",!!N.bonus&&(0,s.jsxs)("span",{className:z().red,children:[" ",Z("bonus")]})]}),(0,s.jsx)("p",{className:z().desc,children:N.addons.map(e=>{var l,n,s;return(null===(l=e.stock.product)||void 0===l?void 0:null===(n=l.translation)||void 0===n?void 0:n.title)+" x "+e.quantity*((null===(s=e.stock.product)||void 0===s?void 0:s.interval)||1)}).join(", ")}),(0,s.jsxs)("div",{className:z().priceContainer,children:[(0,s.jsxs)("div",{className:z().price,children:[(0,s.jsx)(R.Z,{number:N.stock.total_price,symbol:null===(i=g.currency)||void 0===i?void 0:i.symbol})," ","x ",N.quantity,(0,s.jsxs)("span",{className:z().unit,children:["(",((null==N?void 0:null===(d=N.stock)||void 0===d?void 0:null===(o=d.product)||void 0===o?void 0:o.interval)||1)*(null==N?void 0:N.quantity)," ",null==N?void 0:null===(c=N.stock)||void 0===c?void 0:null===(u=c.product)||void 0===u?void 0:null===(m=u.unit)||void 0===m?void 0:null===(v=m.translation)||void 0===v?void 0:v.title,")"]}),(0,s.jsx)("span",{className:z().additionalPrice,children:(0,s.jsx)(R.Z,{number:w,symbol:null===(p=g.currency)||void 0===p?void 0:p.symbol,plus:!0})})]}),(0,s.jsxs)("div",{className:z().price,children:[!!N.discount&&(0,s.jsx)("span",{className:z().oldPrice,children:(0,s.jsx)(R.Z,{number:C,symbol:null===(x=g.currency)||void 0===x?void 0:x.symbol,old:!0})}),(0,s.jsx)(R.Z,{number:k,symbol:null===(h=g.currency)||void 0===h?void 0:h.symbol})]})]})]}),(0,s.jsx)("div",{className:z().imageWrapper,children:(0,s.jsx)(q.Z,{fill:!0,src:(0,A.Z)(null===(j=N.stock)||void 0===j?void 0:null===(_=j.product)||void 0===_?void 0:_.img),alt:null===(b=N.stock)||void 0===b?void 0:null===(y=b.product)||void 0===y?void 0:null===(f=y.translation)||void 0===f?void 0:f.title,sizes:"320px",quality:90})})]})}function $(e){let{data:l}=e,{t:n}=(0,r.$G)();return(0,s.jsxs)("div",{className:B().wrapper,children:[(0,s.jsx)("div",{className:B().header,children:(0,s.jsx)("h3",{className:B().title,children:n("order.details")})}),(0,s.jsx)("div",{className:B().body,children:null==l?void 0:l.details.map(e=>(0,s.jsx)(Q,{data:e,order:l},e.id))})]})}var O=n(70395),U=n.n(O),K=n(60911),X=n.n(K),Y=n(74758),J=n.n(Y),V=n(94660),ee=n(80892),el=n(75688),en=n.n(el),es=n(75931),et=n.n(es),ea=n(90472),ei=n.n(ea),ed=n(5152),er=n.n(ed),eo=n(86701),ec=n(37490),eu=n(94098),em=n(88767),ev=n(73714),ep=n(11163),ex=n(18423),eh=n(34349),ej=n(96477),e_=n(80423),eb=n(11295),ey=n(5848),ef=n(77262),eN=n(47567),eg=n(21014),eZ=n(92430),ew=n.n(eZ),ek=n(10076),eC=n.n(ek),eI=n(92981),eP=n.n(eI),eM=n(46550),eF=n.n(eM),eT=n(30251),eS=n(85943),eW=n(80865),eD=n(58287),eE=n(56060),eG=n(21680),eL=n(60104),eB=n(43668),eH=n.n(eB);function ez(e){var l;let{data:n,handleClose:a,paymentList:i=[],payment:d}=e,{i18n:o}=(0,r.$G)(),{t:c}=(0,r.$G)(),u=(0,em.useQueryClient)(),m=o.language,[v,p,x,h]=(0,eD.Z)(),[j,_,b,y]=(0,eD.Z)(),[f,N]=(0,t.useState)(eL.v[0]),[g,Z]=(0,t.useState)(),[w,k]=(0,t.useState)(null==d?void 0:d.tag),[C,I]=(0,t.useState)([eL.T[0]]),P=("custom"===f?!(null==g?void 0:g.length):!f)||!w,{isLoading:M,mutate:F}=(0,em.useMutation)({mutationFn(e){let l={order_id:e,tips:"custom"===f?Number(g):(0,eG.R)(f,null==n?void 0:n.total_price),for:null==C?void 0:C.join(",")};return eS.Z.payExternal(w,l)},onSuccess(e){a(),window.location.replace(e.data.data.url)},onError(e){var l;(0,ev.vU)(null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.message)}}),{isLoading:T,mutate:S}=(0,em.useMutation)({mutationFn(e){var l;let s={order_id:e,tips:"custom"===f?Number(g):(0,eG.R)(f,null==n?void 0:n.total_price),for:null==C?void 0:C.join(","),payment_sys_id:null===(l=i.find(e=>"wallet"===e.tag))||void 0===l?void 0:l.id};return eS.Z.createTransaction(e,s)},onSuccess(){a(),u.invalidateQueries(["order",null==n?void 0:n.id,m])},onError(e){var l;(0,ev.vU)(null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.message)}}),W=()=>{if(!(null==n?void 0:n.id)){(0,ev.Kp)(c("no.order.id"));return}if(!w){(0,ev.Kp)(c("select.payment.type"));return}if(!f){(0,ev.Kp)(c("select.tip"));return}"wallet"===w?S(n.id):ey.DH.includes(w)&&F(n.id)};return(0,s.jsxs)("div",{className:eH().wrapper,children:[(0,s.jsxs)("h2",{className:eH().title,children:[c("would.you.like.to.add.a.tip"),"?"]}),(0,s.jsxs)("div",{className:eH().tipContainer,children:[(0,s.jsxs)("div",{className:eH().header,children:[(0,s.jsx)("h3",{className:eH().text,children:c("tip.for")}),(0,s.jsxs)("button",{className:eH().selectedButton,onClick:b,children:[(0,s.jsx)("div",{className:eH().selectedItems,children:C.map(e=>(0,s.jsxs)("span",{className:eH().selectedItem,children:[c(e),(null==C?void 0:C.length)>1&&(0,s.jsx)(eF(),{className:eH().closeIcon,size:18,onClick(l){l.stopPropagation(),I(l=>null==l?void 0:l.filter(l=>l!==e))}})]},e))}),j?(0,s.jsx)(eP(),{size:20}):(0,s.jsx)(eC(),{size:20})]})]}),(0,s.jsx)(eE.default,{open:j,anchorEl:_,onClose:y,children:(0,s.jsx)("div",{className:eH().paymentListWrapper,children:eL.T.map(e=>(0,s.jsxs)("div",{className:eH().row,children:[(0,s.jsx)(eW.Z,{value:e,id:e,checked:C.includes(e),name:"tipFor",inputProps:{"aria-label":e},onClick(){I(l=>1===l.length&&l.includes(e)?[e]:l.includes(e)?l.filter(l=>l!==e):[...l,e])}}),(0,s.jsx)("label",{className:eH().label,htmlFor:e,children:(0,s.jsx)("span",{className:eH().text,children:c(e)})})]},e))})})]}),(0,s.jsxs)("div",{className:eH().paymentContainer,children:[(0,s.jsxs)("div",{className:eH().header,children:[(0,s.jsx)("h3",{className:eH().text,children:c("payment.type")}),(0,s.jsxs)("button",{className:eH().selectedButton,onClick:x,children:[(0,s.jsx)("span",{children:c(w)}),v?(0,s.jsx)(eP(),{size:20}):(0,s.jsx)(eC(),{size:20})]})]}),(0,s.jsx)(eE.default,{open:v,anchorEl:p,onClose:h,children:(0,s.jsx)("div",{className:eH().paymentListWrapper,children:i.map(e=>(0,s.jsxs)("div",{className:eH().row,children:[(0,s.jsx)(eW.Z,{value:null==e?void 0:e.tag,id:null==e?void 0:e.tag,onChange(){k(null==e?void 0:e.tag),h()},checked:w===(null==e?void 0:e.tag),name:"tipPayment",inputProps:{"aria-label":null==e?void 0:e.tag}}),(0,s.jsx)("label",{className:eH().label,htmlFor:null==e?void 0:e.tag,children:(0,s.jsx)("span",{className:eH().text,children:c(null==e?void 0:e.tag)})})]},null==e?void 0:e.id))})})]}),(0,s.jsxs)("div",{className:eH().body,children:[eL.v.map(e=>{var l;return(0,s.jsxs)("button",{className:e===f?"".concat(eH().item," ").concat(eH().selectedItem):eH().item,onClick:()=>N(e),children:[(0,s.jsxs)("span",{className:eH().percent,children:[e,"%"]}),(0,s.jsx)("span",{className:eH().price,children:(0,s.jsx)(R.Z,{number:(0,eG.R)(e,null==n?void 0:n.total_price),symbol:null==n?void 0:null===(l=n.currency)||void 0===l?void 0:l.symbol})})]},e)}),(0,s.jsxs)("button",{className:"".concat(eH().item," ").concat("custom"===f?eH().selectedItem:""),onClick:()=>N("custom"),children:[(0,s.jsx)(ew(),{size:20}),(0,s.jsx)("span",{className:eH().price,children:c("custom")})]})]}),"custom"===f&&(0,s.jsx)("div",{className:eH().customTip,children:(0,s.jsx)(eT.Z,{name:"customTip",label:"".concat(c("custom.tip")," (").concat((null==n?void 0:null===(l=n.currency)||void 0===l?void 0:l.symbol)||"$",")"),placeholder:c("type.here"),type:"number",value:g,inputProps:{pattern:"[0-9]*"},onChange(e){let l=Number(e.target.value);l<0||Z(e.target.value)}})}),(0,s.jsx)("div",{className:eH().footer,children:(0,s.jsx)("div",{className:"".concat(eH().btnWrapper," ").concat(P?eH().btnWrapperDisabled:""),children:(0,s.jsx)(ef.Z,{type:"submit",loading:M||T,disabled:P,onClick:W,children:c("submit")})})})]})}var eA=n(21697),eR=n(75619);function eq(e){let{open:l,onClose:n,data:a}=e,i=(0,E.Z)("(min-width:1140px)"),{settings:d}=(0,eA.r)(),{data:r,isLoading:o}=(0,em.useQuery)("payments",()=>eS.Z.getAll()),{paymentType:c,paymentTypes:u}=(0,t.useMemo)(()=>{var e;let l=(null==r?void 0:null===(e=r.data)||void 0===e?void 0:e.filter(e=>(null==e?void 0:e.tag)!=="cash"))||[];return{paymentType:(null==l?void 0:l.find(e=>(null==e?void 0:e.tag)==="wallet"))||l[0],paymentTypes:l}},[d,a,r]);return o?(0,s.jsx)(eR.Z,{}):i?(0,s.jsx)(eN.default,{open:l,onClose:n,children:(0,s.jsx)(ez,{data:a,handleClose:n,paymentList:u,payment:c})}):(0,s.jsx)(eg.default,{open:l,onClose:n,children:(0,s.jsx)(ez,{data:a,handleClose:n,paymentList:u,payment:c})})}var eQ=n(90285),e$=n.n(eQ);let eO=er()(()=>n.e(6041).then(n.bind(n,36041)),{loadableGenerated:{webpack:()=>[36041]}}),eU=er()(()=>Promise.all([n.e(2175),n.e(7996)]).then(n.bind(n,7996)),{loadableGenerated:{webpack:()=>[7996]}}),eK=er()(()=>n.e(7107).then(n.bind(n,47107)),{loadableGenerated:{webpack:()=>[47107]}}),eX=er()(()=>n.e(2483).then(n.bind(n,2483)),{loadableGenerated:{webpack:()=>[2483]}}),eY=er()(()=>Promise.all([n.e(2175),n.e(2598),n.e(224),n.e(8033),n.e(1578)]).then(n.bind(n,21578)),{loadableGenerated:{webpack:()=>[21578]}});function eJ(e){var l,n,t,a,i,d,o,c,u,m,v,p,x,h,j,_,b;let{data:y}=e,{t:f}=(0,r.$G)(),{i18n:N}=(0,r.$G)(),g=N.language,{push:Z}=(0,ep.useRouter)(),w=(0,eh.T)(),k=(0,em.useQueryClient)(),C=(0,eh.C)(ej.Ns),[I,P,M]=(0,ec.Z)(),[F,T,S]=(0,ec.Z)(),[W,D,E]=(0,ec.Z)(),[G,L,B]=(0,ec.Z)(),[H,z,A]=(0,ec.Z)(),q=!(null==y?void 0:null===(l=y.order_refunds)||void 0===l?void 0:l.some(e=>"accepted"===e.status||"pending"===e.status)),Q=y?(y.details.reduce((e,l)=>e+=l.total_price||0,0),y.details.flatMap(e=>e.addons).reduce((e,l)=>e+=l.total_price,0),y.total_discount,(null==y?void 0:y.origin_price)||0):0,{mutate:$,isLoading:O}=(0,em.useMutation)({mutationFn:()=>eu.Z.cancel((null==y?void 0:y.id)||0),onSuccess(){M(),Z("/orders"),(0,ev.Vp)(f("order.cancelled"))},onError:e=>(0,ev.vU)(null==e?void 0:e.statusCode)}),{mutate:K,isLoading:Y}=(0,em.useMutation)({mutationFn(){var e;return eu.Z.deleteAutoRepeat((null==y?void 0:null===(e=y.repeat)||void 0===e?void 0:e.id)||0)},onSuccess(){S(),k.invalidateQueries(["orders",null==y?void 0:y.id,g]),(0,ev.Vp)(f("auto.repeat.order.deleted"))},onError:e=>(0,ev.vU)(null==e?void 0:e.statusCode)}),{isLoading:el,mutate:es}=(0,em.useMutation)({mutationFn:e=>ex.Z.insert(e),onSuccess(e){w((0,ej.CR)(e.data)),Z("/restaurant/".concat(e.data.shop_id,"/checkout"))},onError(){(0,ev.vU)(f("error.400"))}}),{isLoading:ea,mutate:ed}=(0,em.useMutation)({mutationFn:e=>ex.Z.delete(e),onSuccess(){w((0,ej.tx)()),er()}});function er(){var e;if(!(0===C.shop_id||C.shop_id===(null==y?void 0:y.shop.id))){ed({ids:[C.id]});return}let l=[];null==y||y.details.forEach(e=>{let n=e.addons.map(l=>({stock_id:l.stock.id,quantity:l.quantity,parent_id:e.stock.id}));e.bonus||l.push({stock_id:e.stock.id,quantity:e.quantity}),l.push(...n)});let n={shop_id:null==y?void 0:y.shop.id,currency_id:null==y?void 0:null===(e=y.currency)||void 0===e?void 0:e.id,rate:null==y?void 0:y.rate,products:l};es(n)}return(0,s.jsxs)("div",{className:U().wrapper,children:[(0,s.jsxs)("div",{className:U().header,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:U().title,children:f("order")}),(0,s.jsxs)("div",{className:U().subtitle,children:[(0,s.jsxs)("span",{className:U().text,children:["#",null==y?void 0:y.id]}),(0,s.jsx)("span",{className:U().dot}),(0,s.jsx)("span",{className:U().text,children:(null==y?void 0:y.created_at)?(0,eo.wx)(y.created_at):""})]})]}),(null==y?void 0:y.status)==="delivered"&&q&&(0,s.jsx)(eU,{})]}),(0,s.jsxs)("div",{className:U().address,children:[(null==y?void 0:y.delivery_type)==="pickup"?(0,s.jsx)("label",{children:f("pickup.address")}):(0,s.jsx)("label",{children:f("delivery.address")}),(0,s.jsx)("h6",{className:U().text,children:null==y?void 0:null===(n=y.address)||void 0===n?void 0:n.address}),(0,s.jsx)("br",{}),(null==y?void 0:y.delivery_type)==="pickup"?(0,s.jsx)("label",{children:f("pickup.time")}):(0,s.jsx)("label",{children:f("delivery.time")}),(0,s.jsx)("h6",{className:U().text,children:(null==y?void 0:y.delivery_date)?(0,eo.u6)(y.delivery_date,null==y?void 0:y.delivery_time):""}),(0,s.jsx)("br",{}),(0,s.jsx)("label",{children:f("payment.type")}),(0,s.jsx)("h6",{className:U().text,style:{textTransform:"capitalize"},children:(null==y?void 0:y.payment_method)?(0,s.jsxs)("div",{children:[f(y.payment_method),y.payment_method&&["cash_delivery","card_delivery","pix_delivery","debit_delivery"].includes(y.payment_method)&&(0,s.jsxs)("span",{className:U().deliveryBadge,children:[" • ",f("pay.on.delivery")]})]}):f(null==y?void 0:null===(t=y.transaction)||void 0===t?void 0:t.payment_system.tag)}),(0,s.jsx)("br",{}),(null==y?void 0:y.payment_method)==="cash_delivery"&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("label",{children:f("change.required")}),(0,s.jsx)("h6",{className:U().text,children:(null==y?void 0:y.change_required)?(0,s.jsxs)("span",{className:U().changeRequired,children:[f("yes")," - ",f("change.for")," ",(0,s.jsx)(R.Z,{number:null==y?void 0:y.change_amount,symbol:null==y?void 0:null===(a=y.currency)||void 0===a?void 0:a.symbol})]}):(0,s.jsx)("span",{className:U().exactAmount,children:f("exact.amount")})}),(0,s.jsx)("br",{})]}),(null==y?void 0:y.payment_method)&&["card_delivery","pix_delivery","debit_delivery"].includes(y.payment_method)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("label",{children:f("payment.instructions")}),(0,s.jsxs)("h6",{className:U().text,children:["card_delivery"===y.payment_method&&f("use.card.machine"),"pix_delivery"===y.payment_method&&f("use.pix.terminal"),"debit_delivery"===y.payment_method&&f("use.debit.machine")]}),(0,s.jsx)("br",{})]}),(null==y?void 0:y.payment_notes)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("label",{children:f("payment.notes")}),(0,s.jsx)("h6",{className:U().text,children:y.payment_notes}),(0,s.jsx)("br",{})]}),(0,s.jsx)("label",{children:f("payment.status")}),(0,s.jsx)("h6",{className:U().text,style:{textTransform:"capitalize"},children:f(null==y?void 0:null===(i=y.transaction)||void 0===i?void 0:i.status)}),(0,s.jsx)("br",{}),(0,s.jsx)("label",{children:f("ask.this.code.from.customer")}),(0,s.jsx)("h6",{className:U().text,style:{textTransform:"capitalize"},children:null!==(b=null==y?void 0:y.otp)&&void 0!==b?b:f("N/A")})]}),(0,s.jsxs)("div",{className:U().body,children:[(0,s.jsxs)("div",{className:U().flex,children:[(0,s.jsx)("label",{children:f("subtotal")}),(0,s.jsx)("span",{className:U().price,children:(0,s.jsx)(R.Z,{number:Q,symbol:null==y?void 0:null===(d=y.currency)||void 0===d?void 0:d.symbol})})]}),(0,s.jsxs)("div",{className:U().flex,children:[(0,s.jsx)("label",{children:f("delivery.price")}),(0,s.jsx)("span",{className:U().price,children:(0,s.jsx)(R.Z,{number:null==y?void 0:y.delivery_fee,symbol:null==y?void 0:null===(o=y.currency)||void 0===o?void 0:o.symbol})})]}),(0,s.jsxs)("div",{className:U().flex,children:[(0,s.jsx)("label",{children:f("shop.tax")}),(0,s.jsx)("span",{className:U().price,children:(0,s.jsx)(R.Z,{number:null==y?void 0:y.tax,symbol:null==y?void 0:null===(c=y.currency)||void 0===c?void 0:c.symbol})})]}),(0,s.jsxs)("div",{className:U().flex,children:[(0,s.jsx)("label",{children:f("discount")}),(0,s.jsx)("span",{className:U().discount,children:(0,s.jsx)(R.Z,{number:null==y?void 0:y.total_discount,minus:!0,symbol:null==y?void 0:null===(u=y.currency)||void 0===u?void 0:u.symbol})})]}),!!(null==y?void 0:y.coupon)&&(0,s.jsxs)("div",{className:U().flex,children:[(0,s.jsx)("label",{children:f("promo.code")}),(0,s.jsx)("span",{className:U().discount,children:(0,s.jsx)(R.Z,{number:y.coupon.price,minus:!0,symbol:null===(m=y.currency)||void 0===m?void 0:m.symbol})})]}),(0,s.jsxs)("div",{className:U().flex,children:[(0,s.jsx)("label",{children:f("service.fee")}),(0,s.jsx)("span",{className:U().price,children:(0,s.jsx)(R.Z,{number:null==y?void 0:y.service_fee,symbol:null==y?void 0:null===(v=y.currency)||void 0===v?void 0:v.symbol})})]}),(0,s.jsxs)("div",{className:U().flex,children:[(0,s.jsx)("label",{children:f("tips")}),(0,s.jsx)("span",{className:U().price,children:(0,s.jsx)(R.Z,{number:null==y?void 0:y.tips,symbol:null==y?void 0:null===(p=y.currency)||void 0===p?void 0:p.symbol})})]}),(0,s.jsxs)("div",{className:U().flex,children:[(0,s.jsx)("label",{children:f("total")}),(0,s.jsx)("span",{className:U().totalPrice,children:(0,s.jsx)(R.Z,{number:y&&(null==y?void 0:y.total_price)<0?0:null==y?void 0:y.total_price,symbol:null==y?void 0:null===(x=y.currency)||void 0===x?void 0:x.symbol})})]})]}),(null==y?void 0:y.deliveryman)?(0,s.jsxs)("div",{className:U().courierBlock,children:[(0,s.jsxs)("div",{className:U().courier,children:[(0,s.jsx)("div",{className:U().avatar,children:(0,s.jsx)("div",{className:U().imgWrapper,children:(0,s.jsx)(eb.Z,{data:y.deliveryman})})}),(0,s.jsxs)("div",{className:U().naming,children:[(0,s.jsxs)("h5",{className:U().name,children:[y.deliveryman.firstname," ",null===(h=y.deliveryman.lastname)||void 0===h?void 0:h.charAt(0),"."]}),(0,s.jsx)("p",{className:U().text,children:f("driver")})]})]}),(0,s.jsxs)("div",{className:U().actions,children:[(0,s.jsx)("a",{href:"tel:".concat(y.deliveryman.phone),className:U().iconBtn,children:(0,s.jsx)(X(),{})}),(0,s.jsx)("button",{className:U().iconBtn,onClick:D,children:(0,s.jsx)(J(),{})})]})]}):"",ey.de.includes((null==y?void 0:null===(j=y.transaction)||void 0===j?void 0:j.status)||"paid")&&(null==y?void 0:null===(_=y.transaction)||void 0===_?void 0:_.payment_system.tag)!=="cash"?(0,s.jsx)(eX,{data:y}):"",(null==y?void 0:y.status)==="new"?(0,s.jsxs)("div",{className:U().footer,children:[!(null==y?void 0:y.tips)&&(0,s.jsx)(ef.Z,{onClick:L,children:f("add.tip")}),(0,s.jsx)("div",{className:U().main,children:(0,s.jsx)(ee.Z,{type:"button",onClick:P,children:f("cancel.order")})})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:U().footer,children:[!(null==y?void 0:y.tips)&&(null==y?void 0:y.status)!=="canceled"&&(0,s.jsx)(ef.Z,{onClick:L,children:f("add.tip")}),(null==y?void 0:y.status)==="delivered"?(null==y?void 0:y.repeat)?(0,s.jsx)("div",{className:U().main,children:(0,s.jsx)(ee.Z,{type:"button",icon:(0,s.jsx)(e$(),{}),onClick:T,children:f("delete.repeat.order")})}):(0,s.jsx)("div",{className:U().main,children:(0,s.jsx)(ee.Z,{icon:(0,s.jsx)(ei(),{}),type:"button",onClick:z,children:f("auto.repeat.order")})}):"",(null==y?void 0:y.status)==="delivered"||(null==y?void 0:y.status)==="canceled"?(0,s.jsxs)("div",{className:U().main,children:[(0,s.jsx)("a",{href:"tel:".concat(y.shop.phone),style:{display:"block",width:"100%"},children:(0,s.jsx)(V.Z,{icon:(0,s.jsx)(en(),{}),type:"button",children:f("support")})}),(0,s.jsx)(ee.Z,{icon:(0,s.jsx)(et(),{}),type:"button",onClick:er,loading:ea||el,children:f("repeat.order")})]}):""]})}),(0,s.jsx)(eO,{open:I,handleClose:M,onSubmit:$,loading:O,title:f("are.you.sure.cancel.order")}),(0,s.jsx)(eO,{open:F,handleClose:S,onSubmit:K,loading:Y,title:f("are.you.sure.delete.auto.repeat.order")}),(0,s.jsx)(eK,{open:W,onClose:E,PaperProps:{style:{padding:0}},children:(0,s.jsx)(e_.Z,{})}),(0,s.jsx)(eq,{data:y,open:G,onClose:B}),(0,s.jsx)(eY,{open:H,onClose:A})]})}var eV=n(734),e0=n(55385),e5=n.n(e0);function e1(e){let{data:l}=e,{t:n}=(0,r.$G)(),[a,i]=(0,t.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:e5().wrapper,children:[(0,s.jsx)("div",{className:e5().header,children:(0,s.jsx)("h3",{className:e5().title,children:n("order.image")})}),(0,s.jsx)("div",{className:e5().body,children:(0,s.jsx)("img",{src:null==l?void 0:l.image_after_delivered,alt:n("order.image"),onClick:()=>i(!0)})}),(0,s.jsx)(eN.default,{open:a,onClose:()=>i(!1),children:(0,s.jsx)(eV.Z,{src:[(null==l?void 0:l.image_after_delivered)||""],currentIndex:0,closeOnClickOutside:!0,onClose:()=>i(!1)})})]})})}var e8=n(17065),e2=n.n(e8);function e4(e){let{data:l,loading:n}=e,t=(0,E.Z)("(min-width:1140px)");return(0,s.jsx)("div",{className:e2().root,children:!n&&(0,s.jsxs)(G.ZP,{container:!0,spacing:t?4:1.5,children:[(0,s.jsxs)(G.ZP,{item:!0,xs:12,md:7,children:[(0,s.jsx)($,{data:l}),!!(null==l?void 0:l.image_after_delivered)&&(0,s.jsx)(e1,{data:l})]}),(0,s.jsx)(G.ZP,{item:!0,xs:12,md:5,children:(0,s.jsx)(eJ,{data:l})})]})})}var e7=n(91304),e6=n.n(e7);function e9(e){var l;let{list:n}=e,{t}=(0,r.$G)(),a=n[n.length-1];return n.length?(0,s.jsxs)("div",{className:e6().wrapper,children:[(0,s.jsxs)("div",{className:e6().header,children:[(0,s.jsx)("h4",{className:e6().title,children:t("refund")}),(0,s.jsxs)("div",{className:e6().subtitle,children:[(0,s.jsxs)("span",{className:e6().text,children:["#",a.id]}),(0,s.jsx)("span",{className:e6().dot}),(0,s.jsx)("span",{className:e6().text,children:F()(a.updated_at).format("MMM DD, HH:mm")})]}),(0,s.jsx)("div",{className:"".concat(e6().badge," ").concat(e6()[null!==(l=a.status)&&void 0!==l?l:"pending"]),children:(0,s.jsx)("span",{className:e6().text,children:t(a.status)})})]}),(0,s.jsxs)("div",{className:e6().comment,children:[(0,s.jsx)("label",{children:t("your.comment")}),(0,s.jsx)("h6",{className:e6().text,children:a.cause})]}),(0,s.jsxs)("div",{className:e6().comment,children:[(0,s.jsx)("label",{children:t("answer")}),(0,s.jsx)("h6",{className:e6().text,children:a.answer})]})]}):(0,s.jsx)("div",{})}var e3=n(4387);let le=er()(()=>Promise.all([n.e(2175),n.e(4612),n.e(9257)]).then(n.bind(n,59257)),{loadableGenerated:{webpack:()=>[59257]}});function ll(e){let{}=e,{i18n:l}=(0,r.$G)(),n=l.language,{query:i}=(0,ep.useRouter)(),[d,o,c]=(0,ec.Z)(),u=Number(i.id),m=(0,eh.T)(),{data:v,isLoading:p,refetch:x}=(0,em.useQuery)(["order",u,n],()=>eu.Z.getById(u),{refetchOnWindowFocus:!0,refetchInterval:5e3,staleTime:0,onSuccess(e){e.data.review||"delivered"!==e.data.status||o(),e.data.deliveryman&&m((0,e3.nd)(e.data.deliveryman.id))}});return(0,t.useEffect)(()=>()=>{m((0,e3.nd)("admin"))},[m]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.Z,{}),(0,s.jsx)(W,{data:null==v?void 0:v.data,loading:p}),(0,s.jsxs)("div",{className:"container",children:[(0,s.jsx)(e9,{list:(null==v?void 0:v.data.order_refunds)||[]}),(0,s.jsx)(D.Z,{readonly:!0,data:null==v?void 0:v.data,loading:p}),(0,s.jsx)(e4,{data:null==v?void 0:v.data,loading:p})]}),(0,s.jsx)(le,{open:d,onClose:c,refetch:x})]})}},94098:function(e,l,n){"use strict";var s=n(25728);l.Z={calculate:(e,l)=>s.Z.post("/dashboard/user/cart/calculate/".concat(e),l),checkCoupon:e=>s.Z.post("/rest/coupons/check",e),create:e=>s.Z.post("/dashboard/user/orders",e),getAll:e=>s.Z.get("/dashboard/user/orders/paginate?".concat(e)),getById:(e,l,n)=>s.Z.get("/dashboard/user/orders/".concat(e),{params:l,headers:n}),cancel:e=>s.Z.post("/dashboard/user/orders/".concat(e,"/status/change?status=canceled")),review:(e,l)=>s.Z.post("/dashboard/user/orders/review/".concat(e),l),autoRepeat:(e,l)=>s.Z.post("/dashboard/user/orders/".concat(e,"/repeat"),l),deleteAutoRepeat:e=>s.Z.delete("/dashboard/user/orders/".concat(e,"/delete-repeat"))}},85943:function(e,l,n){"use strict";var s=n(25728);l.Z={createTransaction:(e,l)=>s.Z.post("/payments/order/".concat(e,"/transactions"),l),getAll:e=>s.Z.get("/rest/payments",{params:e}),payExternal:(e,l)=>s.Z.get("/dashboard/user/order-".concat(e,"-process"),{params:l}),parcelTransaction:(e,l)=>s.Z.post("/payments/parcel-order/".concat(e,"/transactions"),l)}},21680:function(e,l,n){"use strict";n.d(l,{R:function(){return s}});let s=(e,l)=>(null!=l?l:0)*((null!=e?e:0)/100)},86701:function(e,l,n){"use strict";n.d(l,{u6:function(){return i},wx:function(){return d},yG:function(){return a}});var s=n(27484),t=n.n(s);function a(e){let l=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!e)return"";let n=t()(e).locale("pt-br");return l?n.format("ddd, DD [de] MMM, HH:mm"):n.format("ddd, DD [de] MMM")}function i(e,l){if(!e)return"";let n=t()(e).locale("pt-br");return l?"".concat(n.format("ddd, DD [de] MMM"),", ").concat(l):n.format("ddd, DD [de] MMM, HH:mm")}function d(e){if(!e)return"";let l=t()(e).locale("pt-br");return l.format("DD [de] MMM, HH:mm")}n(57548)},78179:function(e){e.exports={loading:"loading_loading__hXLim",pageLoading:"loading_pageLoading__0nn5j"}},55385:function(e){e.exports={wrapper:"orderImage_wrapper__9BeXl",header:"orderImage_header___ZvTW",title:"orderImage_title__FAJ8D",body:"orderImage_body__StibP"}},35603:function(e){e.exports={row:"orderProductItem_row__QfZwL",col:"orderProductItem_col__HF9ar",title:"orderProductItem_title___Q3_h",red:"orderProductItem_red__9qaew",desc:"orderProductItem_desc__FWlvD",priceContainer:"orderProductItem_priceContainer__NkiPW",price:"orderProductItem_price__ZY8ZI",additionalPrice:"orderProductItem_additionalPrice__jLt0A",oldPrice:"orderProductItem_oldPrice__UdFHl",unit:"orderProductItem_unit__mMcqC",imageWrapper:"orderProductItem_imageWrapper__5DIYW"}},91304:function(e){e.exports={wrapper:"refundInfo_wrapper__cgKOX",header:"refundInfo_header__ww0Qw",title:"refundInfo_title__QySQg",subtitle:"refundInfo_subtitle__1yCnG",text:"refundInfo_text__bRDDW",dot:"refundInfo_dot__Qozcg",badge:"refundInfo_badge__PWSmF",approved:"refundInfo_approved__2EMVS",canceled:"refundInfo_canceled__wxp_j",pending:"refundInfo_pending__xc9wf",comment:"refundInfo_comment__eJkCt"}},43668:function(e){e.exports={wrapper:"tip_wrapper__oQ0aK",title:"tip_title__zaHK_",body:"tip_body__FfwK7",item:"tip_item__YtvmH",percent:"tip_percent__u9J58",price:"tip_price__sr7T1",selectedItem:"tip_selectedItem__7tgJg",customTip:"tip_customTip__sfd68",tipContainer:"tip_tipContainer__5YJwN",header:"tip_header__rf4E3",text:"tip_text__UI9W5",selectedButton:"tip_selectedButton__uIX6j",selectedItems:"tip_selectedItems__Nm_Xl",closeIcon:"tip_closeIcon__3cbih",paymentContainer:"tip_paymentContainer__gYM24",footer:"tip_footer__VxyFN",btnWrapper:"tip_btnWrapper__4mVvq",btnWrapperDisabled:"tip_btnWrapperDisabled__BHisM",paymentListWrapper:"tip_paymentListWrapper__6BwFL",row:"tip_row__YABtU",label:"tip_label__rp5hp"}}},function(e){e.O(0,[8523,4564,6886,1903,6725,4161,9905,2282,8607,9774,2888,179],function(){return e(e.s=51395)}),_N_E=e.O()}]);