/** @type {import('next').NextConfig} */
const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});
const nextConfig = {
  reactStrictMode: false,
  swcMinify: true,
  typescript: {
    ignoreBuildErrors: false,
  },
  // Add error handling for static assets
  onDemandEntries: {
    // Period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 25 * 1000,
    // Number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 2,
  },
  // Improve error handling
  experimental: {
    optimizeCss: false, // Disable CSS optimization that might cause issues
  },
  images: {
    remotePatterns: [
      // Configuração condicional para desenvolvimento local
      ...(process.env.NODE_ENV === 'development' ? [{
        protocol: "http",
        hostname: "localhost",
        port: "8000",
      }] : []),
      {
        protocol: process.env.NEXT_PUBLIC_PROTOCOL || "https",
        hostname: (process.env.NEXT_PUBLIC_API_HOSTNAME || "localhost").split(':')[0],
        port: process.env.NODE_ENV === 'development' && (process.env.NEXT_PUBLIC_API_HOSTNAME || "localhost").includes(':') ?
              (process.env.NEXT_PUBLIC_API_HOSTNAME || "localhost").split(':')[1] : undefined,
      },
      {
        protocol: process.env.NEXT_PUBLIC_PROTOCOL || "https",
        hostname: (process.env.NEXT_PUBLIC_STORAGE_HOSTNAME || "localhost").split(':')[0],
        port: process.env.NODE_ENV === 'development' && (process.env.NEXT_PUBLIC_STORAGE_HOSTNAME || "localhost").includes(':') ?
              (process.env.NEXT_PUBLIC_STORAGE_HOSTNAME || "localhost").split(':')[1] : undefined,
      },
      {
        protocol: "https",
        hostname: "demo-api.foodyman.org",
      },
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
      },
      {
        protocol: "https",
        hostname: "app.ticketflow.chat",
      },
    ],
    minimumCacheTTL: 3600,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Temporariamente desabilitado para debug - REMOVER EM PRODUÇÃO
    unoptimized: true,
  },
};

module.exports = withBundleAnalyzer(nextConfig);
