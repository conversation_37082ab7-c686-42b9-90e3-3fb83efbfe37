exports.id = 2693;
exports.ids = [2693];
exports.modules = {

/***/ 65209:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "shopCard_wrapper__eLDR6",
	"closed": "shopCard_closed__R2PKb",
	"header": "shopCard_header__ndTnF",
	"closedText": "shopCard_closedText__plYTV",
	"body": "shopCard_body__RaioU",
	"shopLogo": "shopCard_shopLogo__YBVUk",
	"title": "shopCard_title__ZOeFL",
	"text": "shopCard_text__N8BjM",
	"footer": "shopCard_footer__dVPVw",
	"flex": "shopCard_flex__7Xj_R",
	"ratingIcon": "shopCard_ratingIcon___i8oO",
	"greenDot": "shopCard_greenDot__URj0M",
	"dot": "shopCard_dot__b_bPy"
};


/***/ }),

/***/ 87270:
/***/ ((module) => {

// Exports
module.exports = {
	"logo": "shopLogo_logo__RFCaX",
	"small": "shopLogo_small__i3Fyo",
	"medium": "shopLogo_medium__H_Sj8",
	"large": "shopLogo_large__kA_9P",
	"wrapper": "shopLogo_wrapper__f0LZd"
};


/***/ }),

/***/ 94437:
/***/ ((module) => {

// Exports
module.exports = {
	"container": "shopList_container__dXd4J",
	"header": "shopList_header__8uPMd",
	"title": "shopList_title__D3BSf",
	"shimmer": "shopList_shimmer__tOM85"
};


/***/ }),

/***/ 54215:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ BonusCaption)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57987);
/* harmony import */ var components_price_price__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(90026);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_2__]);
react_i18next__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];




function BonusCaption({ data  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        children: [
            t("under"),
            " ",
            data.type === "sum" ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_price_price__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                number: data.value
            }) : data.value,
            " +",
            " ",
            t("bonus"),
            " ",
            data.bonusStock?.product.translation?.title
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 24500:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ShopCard)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(65209);
/* harmony import */ var _shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(41664);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var components_shopLogo_shopLogo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(92244);
/* harmony import */ var remixicon_react_RunFillIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(16953);
/* harmony import */ var remixicon_react_RunFillIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_RunFillIcon__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var remixicon_react_StarSmileFillIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(36487);
/* harmony import */ var remixicon_react_StarSmileFillIcon__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_StarSmileFillIcon__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var utils_getImage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(95785);
/* harmony import */ var components_bonusCaption_bonusCaption__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(54215);
/* harmony import */ var containers_shopBadges_shopBadges__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(34364);
/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(37562);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(18074);
/* harmony import */ var utils_getShortTimeType__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(4943);
/* harmony import */ var hooks_useShopWorkingSchedule__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(73444);
/* harmony import */ var components_verifiedComponent_verifiedComponent__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(83626);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_shopLogo_shopLogo__WEBPACK_IMPORTED_MODULE_3__, components_bonusCaption_bonusCaption__WEBPACK_IMPORTED_MODULE_6__, containers_shopBadges_shopBadges__WEBPACK_IMPORTED_MODULE_7__, components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_8__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_9__, components_verifiedComponent_verifiedComponent__WEBPACK_IMPORTED_MODULE_11__]);
([components_shopLogo_shopLogo__WEBPACK_IMPORTED_MODULE_3__, components_bonusCaption_bonusCaption__WEBPACK_IMPORTED_MODULE_6__, containers_shopBadges_shopBadges__WEBPACK_IMPORTED_MODULE_7__, components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_8__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_9__, components_verifiedComponent_verifiedComponent__WEBPACK_IMPORTED_MODULE_11__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);















function ShopCard({ data  }) {
    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z)();
    const { isShopClosed  } = (0,hooks_useShopWorkingSchedule__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z)(data);
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
        href: `/shop/${data.id}`,
        className: `${(_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().wrapper)} ${!data.open || isShopClosed ? (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().closed) : ""}`,
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().header),
                children: [
                    (!data.open || isShopClosed) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().closedText),
                        children: t("closed")
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
                        fill: true,
                        src: (0,utils_getImage__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z)(data.background_img),
                        alt: data.translation?.title,
                        sizes: "400px"
                    })
                ]
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().body),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().shopLogo),
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_shopLogo_shopLogo__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                            data: data
                        })
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_shopBadges_shopBadges__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                        data: data
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("h3", {
                        className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().title),
                        children: [
                            data.translation?.title,
                            data?.verify === 1 && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_verifiedComponent_verifiedComponent__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {})
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                        className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text),
                        children: data.bonus ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_bonusCaption_bonusCaption__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                            data: data.bonus
                        }) : (data.translation?.description)
                    })
                ]
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().footer),
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().flex),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().greenDot)
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_RunFillIcon__WEBPACK_IMPORTED_MODULE_4___default()), {}),
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                                className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text),
                                children: [
                                    data.delivery_time?.from,
                                    "-",
                                    data.delivery_time?.to,
                                    " ",
                                    t((0,utils_getShortTimeType__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)(data.delivery_time?.type))
                                ]
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                        className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().dot)
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().flex),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_StarSmileFillIcon__WEBPACK_IMPORTED_MODULE_5___default()), {
                                className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().ratingIcon)
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                className: (_shopCard_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text),
                                children: data.rating_avg?.toFixed(1) || 0
                            })
                        ]
                    })
                ]
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 92244:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ShopLogo)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _shopLogo_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(87270);
/* harmony import */ var _shopLogo_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_shopLogo_module_scss__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var utils_getImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(95785);
/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(37562);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__]);
components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];





function ShopLogo({ data , size ="medium"  }) {
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        className: `${(_shopLogo_module_scss__WEBPACK_IMPORTED_MODULE_3___default().logo)} ${(_shopLogo_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[size]}`,
        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
            className: (_shopLogo_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),
            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                fill: true,
                src: (0,utils_getImage__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)(data.logo_img),
                alt: data.translation?.title,
                sizes: "(max-width: 768px) 40px, 60px",
                quality: 90
            })
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 62693:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ShopList)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _shopList_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(94437);
/* harmony import */ var _shopList_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_shopList_module_scss__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var components_shopCard_shopCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(24500);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_shopCard_shopCard__WEBPACK_IMPORTED_MODULE_3__]);
components_shopCard_shopCard__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];





function ShopList({ title , shops , loading  }) {
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)("(min-width:1140px)");
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("section", {
        className: "container",
        style: {
            display: !loading && shops.length === 0 ? "none" : "block"
        },
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
            className: (_shopList_module_scss__WEBPACK_IMPORTED_MODULE_4___default().container),
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    className: (_shopList_module_scss__WEBPACK_IMPORTED_MODULE_4___default().header),
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h2", {
                        className: (_shopList_module_scss__WEBPACK_IMPORTED_MODULE_4___default().title),
                        children: title
                    })
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                    container: true,
                    spacing: isDesktop ? 4 : 2,
                    children: !loading ? shops.map((item)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                            item: true,
                            xs: 12,
                            sm: 6,
                            lg: 3,
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_shopCard_shopCard__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                                data: item
                            })
                        }, item.id)) : Array.from(new Array(4)).map((item, idx)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {
                            item: true,
                            xs: 12,
                            sm: 6,
                            lg: 3,
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Skeleton, {
                                variant: "rectangular",
                                className: (_shopList_module_scss__WEBPACK_IMPORTED_MODULE_4___default().shimmer)
                            })
                        }, "shops" + idx))
                })
            ]
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;