(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8830,6060],{21502:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return G}});var a=t(85893);t(67294);var o=t(6999),l=t.n(o),n=t(56060),i=t(41664),c=t.n(i),s=t(6734),d=t(83439),f=t.n(d),u=t(6591),h=t.n(u),v=t(5458),m=t.n(v),p=t(72275),x=t.n(p),j=t(40694),b=t.n(j),g=t(94577),w=t.n(g),y=t(87456),N=t.n(y),O=t(13597),z=t.n(O),_=t(40341),C=t.n(_),k=t(29969),E=t(58287),P=t(88767),Z=t(94098),A=t(80129),H=t.n(A),M=t(16346),B=t(90026),V=t(11295),D=t(45641),L=t(34349),S=t(64698),I=t(21697),W=t(93506),R=t.n(W);function G(e){var r,t,o,i,d;let{data:u}=e,{t:v}=(0,s.$G)(),[p,j,g,y]=(0,E.Z)(),{logout:O,setUserData:_}=(0,k.a)(),A=(0,L.C)(S.j),{settings:W}=(0,I.r)(),G=1===Number(null==W?void 0:W.active_parcel),Q=1===Number(null==W?void 0:W.reservation_enable_for_user);(0,P.useQuery)(["profile",null==A?void 0:A.id],()=>D.Z.get({currency_id:null==A?void 0:A.id}),{staleTime:0,onSuccess(e){_(e.data)}});let{data:T}=(0,P.useQuery)("activeOrders",()=>Z.Z.getAll(H().stringify({order_statuses:!0,statuses:M.j})),{enabled:p}),F=()=>{O(),y()};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{className:l().profileBtn,onClick:g,children:(0,a.jsx)("div",{className:l().imgWrapper,children:(0,a.jsx)(V.Z,{data:u},u.img)})}),(0,a.jsx)(n.default,{open:p,anchorEl:j,onClose:y,anchorOrigin:{vertical:"top",horizontal:"right"},children:(0,a.jsxs)("div",{className:l().wrapper,children:[(0,a.jsxs)("header",{className:l().header,children:[(0,a.jsxs)("div",{className:l().naming,children:[(0,a.jsxs)("h4",{className:l().title,children:[u.firstname," ",null===(r=u.lastname)||void 0===r?void 0:r.charAt(0),"."]}),(0,a.jsx)(c(),{href:"/profile",className:l().link,onClick:y,children:v("view.profile")})]}),(0,a.jsx)("div",{className:l().profileImage,children:(0,a.jsx)(V.Z,{data:u},u.img)})]}),(0,a.jsxs)("div",{className:l().body,children:[(0,a.jsx)(c(),{href:"/wallet",className:l().flex,onClick:y,children:(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(m(),{}),(0,a.jsxs)("span",{className:l().text,children:[v("wallet"),":"]}),(0,a.jsx)("span",{className:l().bold,children:(0,a.jsx)(B.Z,{number:null===(t=u.wallet)||void 0===t?void 0:t.price,symbol:null===(o=u.wallet)||void 0===o?void 0:o.symbol})})]})}),(0,a.jsxs)(c(),{href:"/orders",className:l().flex,onClick:y,children:[(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(f(),{}),(0,a.jsx)("span",{className:l().text,children:v("orders")})]}),!!(null==T?void 0:null===(i=T.meta)||void 0===i?void 0:i.total)&&(0,a.jsx)("div",{className:l().badge,children:null==T?void 0:null===(d=T.meta)||void 0===d?void 0:d.total})]}),Q&&(0,a.jsx)(c(),{href:"/reservations",className:l().flex,onClick:y,children:(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(b(),{}),(0,a.jsx)("span",{className:l().text,children:v("reservations")})]})}),(0,a.jsx)(c(),{href:"/be-seller",className:l().flex,onClick:y,children:(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(C(),{}),(0,a.jsx)("span",{className:l().text,children:v("be.seller")})]})}),G&&(0,a.jsx)(c(),{href:"/parcels",className:l().flex,onClick:y,children:(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(h(),{}),(0,a.jsx)("span",{className:l().text,children:v("parcels")})]})}),(0,a.jsx)(c(),{href:"/liked",className:l().flex,onClick:y,children:(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(x(),{}),(0,a.jsx)("span",{className:l().text,children:v("liked")})]})}),(0,a.jsx)(c(),{href:"/settings/notification",className:l().flex,onClick:y,children:(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(w(),{}),(0,a.jsx)("span",{className:l().text,children:v("settings")})]})}),(0,a.jsx)(c(),{href:"/saved-locations",className:l().flex,onClick:y,children:(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(R(),{}),(0,a.jsx)("span",{className:l().text,children:v("delivery.addresses")})]})}),(0,a.jsx)(c(),{href:"/help",className:l().flex,onClick:y,children:(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(N(),{}),(0,a.jsx)("span",{className:l().text,children:v("help")})]})}),(0,a.jsx)(c(),{href:"/login",className:l().flex,onClick:F,children:(0,a.jsxs)("div",{className:l().item,children:[(0,a.jsx)(z(),{}),(0,a.jsx)("span",{className:l().text,children:v("log.out")})]})})]})]})})]})}},16346:function(e,r,t){"use strict";t.d(r,{a:function(){return o},j:function(){return a}});let a=["new","accepted","cooking","ready","on_a_way"],o=["delivered","canceled"]},56060:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return i}});var a=t(85893);t(67294);var o=t(14564),l=t(90948);let n=(0,l.ZP)(o.ZP)(()=>({"& .MuiBackdrop-root":{backgroundColor:"rgba(0, 0, 0, 0)"},"& .MuiPaper-root":{backgroundColor:"var(--secondary-bg)",boxShadow:"var(--popover-box-shadow)",borderRadius:"10px",maxWidth:"100%"}}));function i(e){let{children:r,...t}=e;return(0,a.jsx)(n,{anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"},...t,children:r})}},94098:function(e,r,t){"use strict";var a=t(25728);r.Z={calculate:(e,r)=>a.Z.post("/dashboard/user/cart/calculate/".concat(e),r),checkCoupon:e=>a.Z.post("/rest/coupons/check",e),create:e=>a.Z.post("/dashboard/user/orders",e),getAll:e=>a.Z.get("/dashboard/user/orders/paginate?".concat(e)),getById:(e,r,t)=>a.Z.get("/dashboard/user/orders/".concat(e),{params:r,headers:t}),cancel:e=>a.Z.post("/dashboard/user/orders/".concat(e,"/status/change?status=canceled")),review:(e,r)=>a.Z.post("/dashboard/user/orders/review/".concat(e),r),autoRepeat:(e,r)=>a.Z.post("/dashboard/user/orders/".concat(e,"/repeat"),r),deleteAutoRepeat:e=>a.Z.delete("/dashboard/user/orders/".concat(e,"/delete-repeat"))}},6999:function(e){e.exports={profileBtn:"profileDropdown_profileBtn__lUVki",imgWrapper:"profileDropdown_imgWrapper__6btm2",wrapper:"profileDropdown_wrapper__GR3_r",header:"profileDropdown_header__rbpis",naming:"profileDropdown_naming__3WG7e",title:"profileDropdown_title__ythS3",link:"profileDropdown_link__dTNyS",profileImage:"profileDropdown_profileImage__HL_Z_",body:"profileDropdown_body__Tqeyo",flex:"profileDropdown_flex__pAYIs",item:"profileDropdown_item__EkN7i",text:"profileDropdown_text__CCJs4",bold:"profileDropdown_bold__iac_e",badge:"profileDropdown_badge__wjFQg"}},6591:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M3 10H2V4.003C2 3.449 2.455 3 2.992 3h18.016A.99.99 0 0 1 22 4.003V10h-1v10.001a.996.996 0 0 1-.993.999H3.993A.996.996 0 0 1 3 20.001V10zm16 0H5v9h14v-9zM4 5v3h16V5H4zm5 7h6v2H9v-2z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},40694:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M19 22H5a3 3 0 0 1-3-3V3a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v12h4v4a3 3 0 0 1-3 3zm-1-5v2a1 1 0 0 0 2 0v-2h-2zm-2 3V4H4v15a1 1 0 0 0 1 1h11zM6 7h8v2H6V7zm0 4h8v2H6v-2zm0 4h5v2H6v-2z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},72275:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12.001 4.529c2.349-2.109 5.979-2.039 8.242.228 2.262 2.268 2.34 5.88.236 8.236l-8.48 8.492-8.478-8.492c-2.104-2.356-2.025-5.974.236-8.236 2.265-2.264 5.888-2.34 8.244-.228zm6.826 1.641c-1.5-1.502-3.92-1.563-5.49-.153l-1.335 1.198-1.336-1.197c-1.575-1.412-3.99-1.35-5.494.154-1.49 1.49-1.565 3.875-.192 5.451L12 18.654l7.02-7.03c1.374-1.577 1.299-3.959-.193-5.454z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},83439:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12h2c0 4.418 3.582 8 8 8s8-3.582 8-8-3.582-8-8-8C9.25 4 6.824 5.387 5.385 7.5H8v2H2v-6h2V6c1.824-2.43 4.729-4 8-4zm1 5v4.585l3.243 3.243-1.415 1.415L11 12.413V7h2z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},13597:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2a9.985 9.985 0 0 1 8 4h-2.71a8 8 0 1 0 .001 12h2.71A9.985 9.985 0 0 1 12 22zm7-6v-3h-8v-2h8V8l5 4-5 4z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},93506:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 23.728l-6.364-6.364a9 9 0 1 1 12.728 0L12 23.728zm4.95-7.778a7 7 0 1 0-9.9 0L12 20.9l4.95-4.95zM12 13a2 2 0 1 1 0-4 2 2 0 0 1 0 4z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},87456:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-1-5h2v2h-2v-2zm2-1.645V14h-2v-1.5a1 1 0 0 1 1-1 1.5 1.5 0 1 0-1.471-1.794l-1.962-.393A3.501 3.501 0 1 1 13 13.355z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},94577:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M3.34 17a10.018 10.018 0 0 1-.978-2.326 3 3 0 0 0 .002-5.347A9.99 9.99 0 0 1 4.865 4.99a3 3 0 0 0 4.631-2.674 9.99 9.99 0 0 1 5.007.002 3 3 0 0 0 4.632 2.672c.579.59 1.093 1.261 1.525 2.01.433.749.757 1.53.978 2.326a3 3 0 0 0-.002 5.347 9.99 9.99 0 0 1-2.501 4.337 3 3 0 0 0-4.631 2.674 9.99 9.99 0 0 1-5.007-.002 3 3 0 0 0-4.632-2.672A10.018 10.018 0 0 1 3.34 17zm5.66.196a4.993 4.993 0 0 1 2.25 2.77c.499.047 1 .048 1.499.001A4.993 4.993 0 0 1 15 17.197a4.993 4.993 0 0 1 3.525-.565c.29-.408.54-.843.748-1.298A4.993 4.993 0 0 1 18 12c0-1.26.47-2.437 1.273-3.334a8.126 8.126 0 0 0-.75-1.298A4.993 4.993 0 0 1 15 6.804a4.993 4.993 0 0 1-2.25-2.77c-.499-.047-1-.048-1.499-.001A4.993 4.993 0 0 1 9 6.803a4.993 4.993 0 0 1-3.525.565 7.99 7.99 0 0 0-.748 1.298A4.993 4.993 0 0 1 6 12c0 1.26-.47 2.437-1.273 3.334a8.126 8.126 0 0 0 .75 1.298A4.993 4.993 0 0 1 9 17.196zM12 15a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0-2a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},40341:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 14v2a6 6 0 0 0-6 6H4a8 8 0 0 1 8-8zm0-1c-3.315 0-6-2.685-6-6s2.685-6 6-6 6 2.685 6 6-2.685 6-6 6zm0-2c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm6 10.5l-2.939 1.545.561-3.272-2.377-2.318 3.286-.478L18 14l1.47 2.977 3.285.478-2.377 2.318.56 3.272L18 21.5z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},5458:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},n=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,n(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return o.default.createElement("svg",l({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M22 7h1v10h-1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v3zm-2 10h-6a5 5 0 0 1 0-10h6V5H4v14h16v-2zm1-2V9h-7a3 3 0 0 0 0 6h7zm-7-4h3v2h-3v-2z"}))},c=o.default.memo?o.default.memo(i):i;e.exports=c},24654:function(){}}]);