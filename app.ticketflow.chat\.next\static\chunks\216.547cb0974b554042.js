(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[216,6555,5636],{45636:function(e,t,l){"use strict";l.r(t),l.d(t,{default:function(){return y}});var n=l(85893),a=l(67294),o=l(6734),i=l(80865),r=l(56555),s=l.n(r),d=l(77262),c=l(27484),u=l.n(c),v=l(80892),f=l(85028),h=l(17662),m=l(19264),p=l(59041),x=l(30719),_=l(71911),j=l(98396);function y(e){var t;let{data:l,handleChangeDeliverySchedule:r,handleClose:c}=e,{t:y}=(0,o.$G)(),g=(0,j.Z)("(min-width:1140px)"),[b,N]=(0,a.useState)(null),[w,O]=(0,a.useState)(0),[C,M]=(0,a.useState)([]),z=f.p[u()().add(w,"day").day()],D=null==l?void 0:null===(t=l.shop_working_days)||void 0===t?void 0:t.find(e=>e.day===z),k=(0,a.useCallback)(()=>{var e;let t=u()().add(w,"day"),n=t.isSame(u()()),a=f.p[t.day()],o=null==l?void 0:null===(e=l.shop_working_days)||void 0===e?void 0:e.find(e=>e.day===a);if(o&&!(0,p.Z)(w,l)){let i=o.from.replace("-",":"),r=o.to.replace("-",":"),s=(0,h.ZP)(i,r,n);M(s),N(null)}else M([]),N(null)},[w,l]);(0,a.useEffect)(()=>{k()},[l,k]);let B=e=>{N(e.target.value)},I=e=>({checked:b===e,onChange:B,value:e,id:e,name:"delivery_time",inputProps:{"aria-label":e}}),L=()=>N(null),P=()=>{if(!b)return;let e=Y(b),t=u()().add(w,"day").format("YYYY-MM-DD");r({time:e,date:t}),c()};function E(e){let t=u()().add(e,"day");return{day:t,weekDay:function(e){let t=e.isSame(u()()),l=e.isSame(u()().add(1,"day"));return t?m.Z.t("today"):l?m.Z.t("tomorrow"):e.format("dddd")}(t)}}function Y(e){var t,n;let a=(0,h.H1)(e),o=parseInt((null==l?void 0:null===(t=l.delivery_time)||void 0===t?void 0:t.to)||"0");if((null==l?void 0:null===(n=l.delivery_time)||void 0===n?void 0:n.type)==="hour"&&(o=60*parseInt(l.delivery_time.to)),a+o>1440)return"".concat(e," - 00:00");let i=(0,h.Ps)(a+o);if(null==D?void 0:D.to){let r=D.to.replace("-",":");if(u()("".concat(u()().format("YYYY-MM-DD")," ").concat(i)).isAfter(u()("".concat(u()().format("YYYY-MM-DD")," ").concat(r))))return"".concat(e," - ").concat(r)}return"".concat(e," - ").concat(i)}return(0,n.jsxs)("div",{className:s().wrapper,children:[(0,n.jsx)("div",{className:s().header,children:(0,n.jsx)("h2",{className:s().title,children:y("time_schedule")})}),(0,n.jsx)("div",{className:s().tabs,children:(0,n.jsx)(x.tq,{spaceBetween:16,slidesPerView:"auto",navigation:g,modules:[_.W_,_.s5],className:"tab-swiper",allowTouchMove:!g,children:f.p.map((e,t)=>(0,n.jsx)(x.o5,{children:(0,n.jsxs)("button",{type:"button",className:"".concat(s().tab," ").concat(w===t?s().active:""),onClick:()=>O(t),children:[(0,n.jsx)("span",{className:s().text,children:E(t).weekDay}),(0,n.jsx)("p",{className:s().subText,children:E(t).day.format("MMM DD")})]})},e))})}),(0,n.jsxs)("div",{className:s().body,children:[C.map((e,t,l)=>(0,n.jsxs)("div",{className:s().row,style:{display:l[t+1]?"flex":"none"},children:[(0,n.jsx)(i.Z,{...I(e)}),(0,n.jsx)("label",{className:s().label,htmlFor:e,children:(0,n.jsx)("span",{className:s().text,children:Y(e)})})]},e)),0===C.length&&(0,n.jsx)("div",{children:y("shop.closed.choose.other.day")})]}),(0,n.jsxs)("div",{className:s().footer,children:[(0,n.jsx)("div",{className:s().action,children:(0,n.jsx)(d.Z,{onClick:P,children:y("save")})}),(0,n.jsx)("div",{className:s().action,children:(0,n.jsx)(v.Z,{onClick:L,children:y("clear")})})]})]})}},86555:function(e,t,l){"use strict";l.r(t),l.d(t,{default:function(){return m}});var n=l(85893),a=l(67294),o=l(76725),i=l(9730),r=l.n(i),s=l(5848),d=l(60291),c=l(45122),u=l(90026);let v=e=>(0,n.jsx)("div",{className:r().point,children:(0,n.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),f=e=>(0,n.jsxs)("div",{className:r().floatCard,children:[(null==e?void 0:e.price)&&(0,n.jsx)("span",{className:r().price,children:(0,n.jsx)(u.Z,{number:e.price})}),(0,n.jsx)("div",{className:r().marker,children:(0,n.jsx)(c.Z,{data:e.shop,size:"small"})})]}),h={fields:["address_components","geometry"],types:["address"]};function m(e){var t,l;let{location:i,setLocation:c=()=>{},readOnly:u=!1,shop:m,inputRef:p,setAddress:x,price:_,drawLine:j,defaultZoom:y=15}=e,g=(0,a.useRef)(),[b,N]=(0,a.useState)(),[w,O]=(0,a.useState)();async function C(e){var t;if(u)return;let l={lat:e.center.lat(),lng:e.center.lng()};c(l);let n=await (0,d.K)("".concat(l.lat,",").concat(l.lng));(null==p?void 0:null===(t=p.current)||void 0===t?void 0:t.value)&&(p.current.value=n),x&&x(n)}let M=(e,t)=>{if(p&&(g.current=new t.places.Autocomplete(p.current,h),g.current.addListener("place_changed",async function(){let e=await g.current.getPlace(),t=function(e){let t={street_number:"streetNumber",route:"streetName",sublocality_level_1:"city",locality:"city1",administrative_area_level_1:"state",postal_code:"postalCode",country:"country"},l={};e.address_components.forEach(e=>{l[t[e.types[0]]]=e.long_name});let n=[null==l?void 0:l.streetName,null==l?void 0:l.city1,null==l?void 0:l.country];return n.join(", ")}(e),l={lat:e.geometry.location.lat(),lng:e.geometry.location.lng()};c(l),x&&x(t)})),O(e),N(t),m){let l={lat:Number(null===(o=m.location)||void 0===o?void 0:o.latitude)||0,lng:Number(null===(r=m.location)||void 0===r?void 0:r.longitude)||0},n=[i,l],a=new t.LatLngBounds;for(var o,r,s=0;s<n.length;s++)a.extend(n[s]);e.fitBounds(a)}};return(0,a.useEffect)(()=>{if(m&&b){var e,t;let l={lat:Number(null===(e=m.location)||void 0===e?void 0:e.latitude)||0,lng:Number(null===(t=m.location)||void 0===t?void 0:t.longitude)||0},n=[i,l],a=new b.LatLngBounds;for(var o=0;o<n.length;o++)a.extend(n[o]);w.fitBounds(a)}},[i,null==m?void 0:m.location,j,w,b]),(0,n.jsxs)("div",{className:r().root,children:[!u&&(0,n.jsx)("div",{className:r().marker,children:(0,n.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),(0,n.jsxs)(o.ZP,{bootstrapURLKeys:{key:s.kr||"",libraries:["places"]},zoom:y,center:i,onDragEnd:C,yesIWantToUseGoogleMapApiInternals:!0,onGoogleApiLoaded(e){let{map:t,maps:l}=e;return M(t,l)},options:{fullscreenControl:u},children:[u&&(0,n.jsx)(v,{lat:i.lat,lng:i.lng}),!!m&&(0,n.jsx)(f,{lat:(null===(t=m.location)||void 0===t?void 0:t.latitude)||0,lng:(null===(l=m.location)||void 0===l?void 0:l.longitude)||0,shop:m,price:_})]})]})}},90216:function(e,t,l){"use strict";l.r(t),l.d(t,{default:function(){return U}});var n=l(85893),a=l(67294),o=l(73944),i=l.n(o),r=l(37490),s=l(6734),d=l(98396),c=l(47567),u=l(21014),v=l(41370),f=l.n(v),h=l(86555),m=l(48654),p=l.n(m),x=l(42480),_=l.n(x),j=l(93520),y=l.n(j),g=l(11327),b=l.n(g),N=l(69675),w=l.n(N),O=l(80207),C=l.n(O),M=l(2525),z=l.n(M),D=l(26261),k=l.n(D),B=l(73444),I=l(73714),L=l(88767),P=l(1612),E=l(25305),Y=l.n(E);function T(e){var t,l,o,i,r,d,c,u,v,m;let{data:x,onClose:j}=e,{t:g}=(0,s.$G)(),[N,O]=(0,a.useState)({lat:Number(null==x?void 0:null===(t=x.location)||void 0===t?void 0:t.latitude),lng:Number(null==x?void 0:null===(l=x.location)||void 0===l?void 0:l.longitude)}),{workingSchedule:M,isShopClosed:D}=(0,B.Z)(x),[E,T]=(0,a.useState)(!1),[Z,S]=(0,a.useState)(!1),{data:H}=(0,L.useQuery)(["branches",null==x?void 0:x.id],()=>P.Z.getAllBranches({shop_id:null==x?void 0:x.id}),{enabled:!!(null==x?void 0:x.id)}),V=async()=>{try{var e;await navigator.clipboard.writeText((null==x?void 0:null===(e=x.translation)||void 0===e?void 0:e.address)||""),(0,I.Vp)(g("copied"))}catch(t){(0,I.vU)("Failed to copy!")}};return(0,n.jsxs)("div",{className:f().container,children:[(0,n.jsx)("button",{className:f().closeBtn,onClick:j,children:(0,n.jsx)(p(),{})}),(0,n.jsx)("div",{className:f().map,children:(0,n.jsx)(h.default,{location:N,readOnly:!0})}),(0,n.jsxs)("div",{className:f().wrapper,children:[(0,n.jsxs)("div",{className:f().header,children:[(0,n.jsx)("h2",{className:f().title,children:null==x?void 0:null===(o=x.translation)||void 0===o?void 0:o.title}),(0,n.jsx)("p",{className:f().text,children:null===(r=null==x?void 0:null===(i=x.tags)||void 0===i?void 0:i.map(e=>{var t;return null===(t=e.translation)||void 0===t?void 0:t.title}))||void 0===r?void 0:r.join(" • ")})]}),(0,n.jsxs)("div",{className:f().body,children:[(0,n.jsx)("div",{className:f().flexBtn,children:(0,n.jsxs)("button",{className:f().flex,onClick(){var e,t;return O({lat:Number(null==x?void 0:null===(e=x.location)||void 0===e?void 0:e.latitude),lng:Number(null==x?void 0:null===(t=x.location)||void 0===t?void 0:t.longitude)})},children:[(0,n.jsx)(_(),{}),(0,n.jsx)("span",{className:f().text,children:null==x?void 0:null===(d=x.translation)||void 0===d?void 0:d.address}),(0,n.jsx)("button",{onClick(e){e.stopPropagation(),V()},children:(0,n.jsx)(y(),{})})]})}),(0,n.jsxs)("div",{className:f().flexBtn,children:[(0,n.jsxs)("button",{className:f().flex,onClick:()=>T(!E),children:[(0,n.jsx)(b(),{}),(0,n.jsx)("span",{className:f().text,children:D?g("closed"):"".concat(g("open.until")," — ").concat(M.to)}),E?(0,n.jsx)(C(),{}):(0,n.jsx)(w(),{})]}),E&&(0,n.jsx)("ul",{className:f().details,children:null==x?void 0:null===(c=x.shop_working_days)||void 0===c?void 0:c.map(e=>(0,n.jsxs)("li",{children:[(0,n.jsxs)("strong",{children:[g(e.day),": "]}),"".concat(e.from," — ").concat(e.to)]},"day"+e.id))})]}),(0,n.jsx)("div",{className:f().flexBtn,children:(0,n.jsxs)("div",{className:f().flex,children:[(0,n.jsx)(z(),{}),(0,n.jsxs)("span",{className:f().text,children:[(null==x?void 0:null===(u=x.rating_avg)||void 0===u?void 0:u.toFixed(1))||0," ",(null==x?void 0:x.rating_avg)?"(".concat(null==x?void 0:x.reviews_count,"+ ").concat(g("ratings"),")"):""]})]})}),H&&(null==H?void 0:null===(v=H.data)||void 0===v?void 0:v.length)!==0&&(0,n.jsxs)("div",{className:f().flexBtn,children:[(0,n.jsxs)("button",{onClick:()=>S(!Z),className:f().flex,children:[(0,n.jsx)(k(),{}),(0,n.jsx)("span",{className:f().text,children:g("branches")}),Z?(0,n.jsx)(C(),{}):(0,n.jsx)(w(),{})]}),Z&&(0,n.jsx)("ul",{className:f().details,children:null==H?void 0:null===(m=H.data)||void 0===m?void 0:m.map(e=>{var t,l;return(0,n.jsxs)("li",{className:f().branch,children:[(0,n.jsxs)("div",{className:f().content,children:[(0,n.jsxs)("div",{className:f().title,children:[null===(t=e.translation)||void 0===t?void 0:t.title,":"," "]}),(0,n.jsx)("span",{children:null==e?void 0:null===(l=e.address)||void 0===l?void 0:l.address})]}),(0,n.jsx)("button",{onClick:()=>O({lat:Number(e.location.latitude),lng:Number(e.location.longitude)}),children:(0,n.jsx)(Y(),{})})]},"branch"+e.id)})})]})]})]})]})}var Z=l(45636),S=l(34349),H=l(23650),V=l(27484),A=l.n(V),G=l(11163);function U(e){let{data:t}=e,{t:l}=(0,s.$G)(),o=(0,S.T)(),v=(0,d.Z)("(min-width:1140px)"),{order:f}=(0,S.C)(H.zT),[h,m,p]=(0,r.Z)(),[x,_,j]=(0,r.Z)(),{push:y}=(0,G.useRouter)(),g=e=>{let{date:l,time:n}=e;o((0,H.Zp)({delivery_time:n,delivery_date:l,shop_id:null==t?void 0:t.id}))};return(0,a.useEffect)(()=>{f.shop_id!==(null==t?void 0:t.id)&&o((0,H.bn)())},[t]),(0,n.jsxs)("div",{className:i().flex,children:[(0,n.jsx)("button",{className:i().textBtn,onClick:m,children:l("more.info")}),(0,n.jsx)("button",{className:i().textBtn,onClick:_,children:l(f.delivery_time?"edit.schedule":"schedule")}),!!f.delivery_time&&(0,n.jsxs)("div",{className:i().text,children:[A()(f.delivery_date).format("ddd, MMM DD,")," ",f.delivery_time]}),(0,n.jsx)("button",{className:i().textBtn,onClick:()=>y({pathname:"/recipes",query:{shop_id:null==t?void 0:t.id}}),children:l("recipes")}),v?(0,n.jsx)(c.default,{open:h,onClose:p,closable:!1,children:h&&(0,n.jsx)(T,{data:t,onClose:p})}):(0,n.jsx)(u.default,{open:h,onClose:p,children:h&&(0,n.jsx)(T,{data:t,onClose:p})}),v?(0,n.jsx)(c.default,{open:x,onClose:j,children:(0,n.jsx)(Z.default,{data:t,handleClose:j,handleChangeDeliverySchedule:g})}):(0,n.jsx)(u.default,{open:x,onClose:j,children:(0,n.jsx)(Z.default,{data:t,handleClose:j,handleChangeDeliverySchedule:g})})]})}},59041:function(e,t,l){"use strict";l.d(t,{Z:function(){return o}});var n=l(27484),a=l.n(n);function o(e,t){var l,n;let o=a()().add(e,"day"),i=a()().format("YYYY-MM-DD"),r=!1,s=null==t?void 0:null===(n=t.shop_working_days)||void 0===n?void 0:n.find(e=>{var t;return(null===(t=e.day)||void 0===t?void 0:t.toLowerCase())===o.format("dddd").toLowerCase()}),d=null==t?void 0:null===(l=t.shop_closed_date)||void 0===l?void 0:l.some(e=>a()(e.day).isSame(o.format("YYYY-MM-DD")));if(0===e){let c=null==s?void 0:s.to.replace("-",":");r=a()().isAfter(a()("".concat(i," ").concat(c)))}let u=(null==s?void 0:s.disabled)||d;return u||r}},17662:function(e,t,l){"use strict";l.d(t,{H1:function(){return o},Ps:function(){return i},ZP:function(){return r}});var n=l(27484),a=l.n(n);let o=e=>e.split(":").reduce((e,t)=>60*e+ +t),i=e=>Math.floor(e/60).toLocaleString("en-US",{minimumIntegerDigits:2})+":"+(e%60).toLocaleString("en-US",{minimumIntegerDigits:2});function r(e,t,l){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30,r=o(e),s=o(t),d=l?o(a()().add(n,"minute").format("HH:00")):0;return d>s?[]:(d>r&&(r=d),Array.from({length:Math.floor((s-r)/n)+1},(e,t)=>i(r+t*n)))}},56555:function(e){e.exports={wrapper:"deliveryTimes_wrapper__l6KX_",header:"deliveryTimes_header__Y5NUn",title:"deliveryTimes_title__NOnZ2",tabs:"deliveryTimes_tabs__jbI3F",tab:"deliveryTimes_tab__BQcng",disabled:"deliveryTimes_disabled__p6aRs",text:"deliveryTimes_text__IE6bA",subText:"deliveryTimes_subText__M_OqM",active:"deliveryTimes_active__1crnt",body:"deliveryTimes_body___8Kii",row:"deliveryTimes_row__4AYPt",label:"deliveryTimes_label__yQILx",footer:"deliveryTimes_footer__NRLyh",action:"deliveryTimes_action__LLPKM"}},9730:function(e){e.exports={root:"map_root__3qcrq",marker:"map_marker__EnBz1",floatCard:"map_floatCard__1zZP1",price:"map_price__CTP0I",point:"map_point__GfLMi"}},41370:function(e){e.exports={container:"shopInfoDetails_container__bmjzQ",closeBtn:"shopInfoDetails_closeBtn__0zoaO",map:"shopInfoDetails_map__LYND6",wrapper:"shopInfoDetails_wrapper__4kph8",header:"shopInfoDetails_header__EGjVH",title:"shopInfoDetails_title__ywD6c",text:"shopInfoDetails_text__461YG",body:"shopInfoDetails_body__wM6Is",flexBtn:"shopInfoDetails_flexBtn__I5ZX2",flex:"shopInfoDetails_flex__ucX9A",details:"shopInfoDetails_details__utJj_",branch:"shopInfoDetails_branch__iSEfj",content:"shopInfoDetails_content__l3flV"}},73944:function(e){e.exports={flex:"shopInfo_flex__QVVm2",text:"shopInfo_text__g999n",textBtn:"shopInfo_textBtn__ULh6Z"}},93520:function(e,t,l){"use strict";var n=l(67294),a=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},i=function(e,t){var l={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(l[n]=e[n]);return l},r=function(e){var t=e.color,l=e.size,n=void 0===l?24:l,r=(e.children,i(e,["color","size","children"])),s="remixicon-icon "+(r.className||"");return a.default.createElement("svg",o({},r,{className:s,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M7 6V3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1h-3v3c0 .552-.45 1-1.007 1H4.007A1.001 1.001 0 0 1 3 21l.003-14c0-.552.45-1 1.007-1H7zM5.003 8L5 20h10V8H5.003zM9 6h8v10h2V4H9v2z"}))},s=a.default.memo?a.default.memo(r):r;e.exports=s},42480:function(e,t,l){"use strict";var n=l(67294),a=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},i=function(e,t){var l={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(l[n]=e[n]);return l},r=function(e){var t=e.color,l=e.size,n=void 0===l?24:l,r=(e.children,i(e,["color","size","children"])),s="remixicon-icon "+(r.className||"");return a.default.createElement("svg",o({},r,{className:s,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M18.364 17.364L12 23.728l-6.364-6.364a9 9 0 1 1 12.728 0zM12 13a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"}))},s=a.default.memo?a.default.memo(r):r;e.exports=s},25305:function(e,t,l){"use strict";var n=l(67294),a=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},i=function(e,t){var l={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(l[n]=e[n]);return l},r=function(e){var t=e.color,l=e.size,n=void 0===l?24:l,r=(e.children,i(e,["color","size","children"])),s="remixicon-icon "+(r.className||"");return a.default.createElement("svg",o({},r,{className:s,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M4 6.143v12.824l5.065-2.17 6 3L20 17.68V4.857l1.303-.558a.5.5 0 0 1 .697.46V19l-7 3-6-3-6.303 2.701a.5.5 0 0 1-.697-.46V7l2-.857zm12.243 5.1L12 15.485l-4.243-4.242a6 6 0 1 1 8.486 0zM12 12.657l2.828-2.829a4 4 0 1 0-5.656 0L12 12.657z"}))},s=a.default.memo?a.default.memo(r):r;e.exports=s},2525:function(e,t,l){"use strict";var n=l(67294),a=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},i=function(e,t){var l={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(l[n]=e[n]);return l},r=function(e){var t=e.color,l=e.size,n=void 0===l?24:l,r=(e.children,i(e,["color","size","children"])),s="remixicon-icon "+(r.className||"");return a.default.createElement("svg",o({},r,{className:s,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M12 18.26l-7.053 3.948 1.575-7.928L.587 8.792l8.027-.952L12 .5l3.386 7.34 8.027.952-5.935 5.488 1.575 7.928z"}))},s=a.default.memo?a.default.memo(r):r;e.exports=s},26261:function(e,t,l){"use strict";var n=l(67294),a=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},i=function(e,t){var l={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(l[n]=e[n]);return l},r=function(e){var t=e.color,l=e.size,n=void 0===l?24:l,r=(e.children,i(e,["color","size","children"])),s="remixicon-icon "+(r.className||"");return a.default.createElement("svg",o({},r,{className:s,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M21 13v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-7H2v-2l1-5h18l1 5v2h-1zM5 13v6h14v-6H5zm1 1h8v3H6v-3zM3 3h18v2H3V3z"}))},s=a.default.memo?a.default.memo(r):r;e.exports=s},80207:function(e,t,l){"use strict";var n=l(67294),a=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},i=function(e,t){var l={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(l[n]=e[n]);return l},r=function(e){var t=e.color,l=e.size,n=void 0===l?24:l,r=(e.children,i(e,["color","size","children"])),s="remixicon-icon "+(r.className||"");return a.default.createElement("svg",o({},r,{className:s,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M5 11h14v2H5z"}))},s=a.default.memo?a.default.memo(r):r;e.exports=s},11327:function(e,t,l){"use strict";var n=l(67294),a=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},i=function(e,t){var l={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(l[n]=e[n]);return l},r=function(e){var t=e.color,l=e.size,n=void 0===l?24:l,r=(e.children,i(e,["color","size","children"])),s="remixicon-icon "+(r.className||"");return a.default.createElement("svg",o({},r,{className:s,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm1-10V7h-2v7h6v-2h-4z"}))},s=a.default.memo?a.default.memo(r):r;e.exports=s}}]);