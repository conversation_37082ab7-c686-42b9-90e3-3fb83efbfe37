(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8607],{80423:function(e,t,r){"use strict";r.d(t,{Z:function(){return R}});var a=r(85893),n=r(67294),o=r(34161),i=r(27484),s=r.n(i),l=r(6734);function d(e){let{date:t}=e,{t:r}=(0,l.$G)(),n=s()(t).isSame(s()(),"day");return(0,a.jsx)("div",{className:"chat-date","data-date":n?r("today"):s()(t).locale("pt-br").format("D [de] MMM")})}function c(e){let{text:t,time:r,chat_img:n}=e;return(0,a.jsx)("div",{className:"admin-message-wrapper",children:(0,a.jsxs)("div",{className:"admin-message ".concat(n&&"chat-image"),children:[n&&(0,a.jsx)(o.v0,{type:"image",model:{position:"normal",direction:"incoming",payload:{src:n,alt:"Joe avatar",width:"100%",height:"100%"}}}),t&&(0,a.jsx)("div",{className:"text",children:t}),(0,a.jsx)("div",{className:"time",children:s()(new Date(r)).format("HH:mm")})]})})}var u=r(15079),m=r.n(u);function _(e){let{text:t,time:r,status:n="",chat_img:i}=e;return(0,a.jsx)("div",{className:"user-sms-wrapper",children:(0,a.jsxs)("div",{className:"user-message ".concat(i&&"chat-image"),children:[i&&(0,a.jsx)(o.v0,{type:"image",model:{position:"normal",direction:"incoming",payload:{src:i,alt:"Joe avatar",width:"100%",height:"100%"}}}),t&&(0,a.jsx)("div",{className:"text",children:t}),(0,a.jsx)("div",{className:"time",children:s()(new Date(r)).format("HH:mm")}),(0,a.jsx)("span",{className:"double-check",children:"pending"===n?"":(0,a.jsx)(m(),{size:16})})]})})}function p(e){let{groupMessages:t,messageEndRef:r}=e;return(0,a.jsxs)("div",{className:"chat-box",children:[t.map((e,t)=>(0,a.jsxs)("div",{children:["Invalid Date"!==e.date?(0,a.jsx)(d,{date:e.date}):"",(0,a.jsx)("div",{className:"sms-box",children:e.messages.map(e=>Boolean(e.sender)?(0,a.jsx)(_,{text:e.chat_content,time:e.created_at,status:e.status,chat_img:e.chat_img},e.created_at):(0,a.jsx)(c,{text:e.chat_content,time:e.created_at,chat_img:e.chat_img},e.created_at))})]},t)),(0,a.jsx)("div",{ref:r})]})}var f=r(4387),h=r(30569),g=r(30251),v=r(86650),x=r(77262),j=r(80892),y=r(73714),b=r(34349);function I(e){let{url:t,setPercent:r=e=>{},file:n,handleOnSubmit:o,handleClose:i}=e,{t:s}=(0,l.$G)(),d=(0,b.T)(),c=()=>{n||(0,y.Kp)("Please upload an image first!");let e=(0,v.iH)(h.tO,"/files/".concat(n.name)),t=(0,v.B0)(e,n);t.on("state_changed",e=>{let t=Math.round(e.bytesTransferred/e.totalBytes*100);r(t)},e=>console.log(e),()=>{(0,v.Jt)(t.snapshot.ref).then(e=>{o(e)})})},u=e=>{d((0,f.zR)(e))};return(0,a.jsx)("div",{className:"upload-media",children:(0,a.jsxs)("div",{className:"upload-form",children:[(0,a.jsx)("img",{src:t}),(0,a.jsx)("div",{children:(0,a.jsx)(g.Z,{label:"Caption",onChange(e){u(e.target.value)}})}),(0,a.jsxs)("div",{className:"footer-btns",children:[(0,a.jsx)(j.Z,{type:"button",onClick:i,children:s("cancel")}),(0,a.jsx)(x.Z,{type:"button",onClick:c,children:s("send")})]})]})})}let w=["image/jpg","image/jpeg","image/png","image/svg+xml","image/svg"];var N=r(11163),k=r(37490),M=r(29969),C=r(98396),B=r(47567),H=r(21014);function R(){let{t:e}=(0,l.$G)(),t=(0,C.Z)("(min-width:1140px)"),r=(0,n.useRef)(null),i=(0,n.useRef)(null),{pathname:d,query:c}=(0,N.useRouter)(),u=(0,b.T)(),[m,_,g]=(0,k.Z)(),v=(0,n.useRef)(),[x,j]=(0,n.useState)(""),[R,Z]=(0,n.useState)(""),P="/restaurant/[id]"===d||"/shop/[id]"===d,S="/orders/[id]"===d,L=String(c.id),{chats:T,currentChat:z,newMessage:D,roleId:W,messages:O}=(0,b.C)(f.Z1),{user:Y}=(0,M.a)(),A=function(e){let{messages:t,currentChat:r}=e;if(!r)return[];let a=t.filter(e=>e.chat_id===r.id).reduce((e,t)=>{let r=s()(new Date(t.created_at)).format("MM-DD-YYYY");return e[r]||(e[r]=[]),e[r].push(t),e},{}),n=Object.keys(a).map(e=>({date:e,messages:a[e]}));return n}({currentChat:z,messages:O}),G=e=>{Y&&T&&(e?u((0,f.eb)(e)):(0,h.P4)({shop_id:-1,roleId:P?L:S?W:"admin",user:{id:Y.id,firstname:Y.firstname,lastname:Y.lastname,img:(null==Y?void 0:Y.img)||""}}))};(0,n.useEffect)(()=>{r.current&&r.current.focus()},[r,z]),(0,n.useEffect)(()=>{let e=T.filter(e=>{var t;return(null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.id)==Y.id}).reverse().find(e=>P?e.roleId==L:S?e.roleId==W:"admin"==e.roleId);G(e)},[T]);let U=e=>{u((0,f.zR)(e))},q=e=>{let t=null==e?void 0:e.includes("https"),r=D.replace(/\&nbsp;/g,"").replace(/<[^>]+>/g,"").trim(),a={chat_content:r,chat_id:(null==z?void 0:z.id)||0,sender:1,unread:!0,roleId:P?L:S?W:"admin",created_at:new Date().toString()};if(t&&(a.chat_img=e),r||t){var n,o,i,s,l,d;(0,h.bG)(a),u((0,f.zR)("")),u((0,f.Hz)({...a,status:"pending"}));let c=(null===(n=v.current)||void 0===n?void 0:n.offsetTop)||0,m=document.querySelector(".message-list .scrollbar-container");s=c-30-(i=(o=m).scrollTop),l=0,(d=function(){var e,t=(e=l+=20,(e/=300)<1?s/2*e*e+i:-s/2*(--e*(e-2)-1)+i);o.scrollTop=t,l<600&&setTimeout(d,20)})(),Z(""),g()}},E=()=>{var e;null===(e=i.current)||void 0===e||e.click()};return(0,a.jsxs)("div",{className:"chat-drawer",children:[(0,a.jsx)("div",{className:"header",children:(0,a.jsx)("h3",{className:"title",children:e("help.center")})}),(0,a.jsxs)("div",{className:"chat-wrapper",children:[(0,a.jsx)("input",{type:"file",ref:i,onChange:function(t){if(w.includes(t.target.files[0].type)){j(t.target.files[0]);let r=new FileReader;r.onload=()=>{2===r.readyState&&(Z(String(r.result)),_())},null==r||r.readAsDataURL(t.target.files[0])}else(0,y.Kp)(e("supported.image.formats.only"))},accept:"image/jpg, image/jpeg, image/png, image/svg+xml, image/svg",className:"d-none"}),(0,a.jsx)(o.tz,{responsive:!0,className:"chat-container rounded",children:(0,a.jsxs)(o.uU,{className:"chat-container",children:[(0,a.jsx)(o.rN,{className:"message-list",children:(0,a.jsx)(p,{groupMessages:A,messageEndRef:v})}),(0,a.jsx)(o.Ru,{ref:r,value:D,onChange:U,onSend:q,placeholder:e("message"),className:"chat-input",attachButton:!0,onAttachClick:E})]})}),t?(0,a.jsx)(B.default,{open:m,onClose:g,children:(0,a.jsx)(I,{url:R,file:x,handleOnSubmit:q,handleClose:g})}):(0,a.jsx)(H.default,{open:m,onClose:g,children:(0,a.jsx)(I,{url:R,file:x,handleOnSubmit:q,handleClose:g})})]})]})}},30251:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var a=r(85893);r(67294);var n=r(90948),o=r(61903);let i=(0,n.ZP)(o.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function s(e){return(0,a.jsx)(i,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},86555:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return f}});var a=r(85893),n=r(67294),o=r(76725),i=r(9730),s=r.n(i),l=r(5848),d=r(60291),c=r(45122),u=r(90026);let m=e=>(0,a.jsx)("div",{className:s().point,children:(0,a.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),_=e=>(0,a.jsxs)("div",{className:s().floatCard,children:[(null==e?void 0:e.price)&&(0,a.jsx)("span",{className:s().price,children:(0,a.jsx)(u.Z,{number:e.price})}),(0,a.jsx)("div",{className:s().marker,children:(0,a.jsx)(c.Z,{data:e.shop,size:"small"})})]}),p={fields:["address_components","geometry"],types:["address"]};function f(e){var t,r;let{location:i,setLocation:c=()=>{},readOnly:u=!1,shop:f,inputRef:h,setAddress:g,price:v,drawLine:x,defaultZoom:j=15}=e,y=(0,n.useRef)(),[b,I]=(0,n.useState)(),[w,N]=(0,n.useState)();async function k(e){var t;if(u)return;let r={lat:e.center.lat(),lng:e.center.lng()};c(r);let a=await (0,d.K)("".concat(r.lat,",").concat(r.lng));(null==h?void 0:null===(t=h.current)||void 0===t?void 0:t.value)&&(h.current.value=a),g&&g(a)}let M=(e,t)=>{if(h&&(y.current=new t.places.Autocomplete(h.current,p),y.current.addListener("place_changed",async function(){let e=await y.current.getPlace(),t=function(e){let t={street_number:"streetNumber",route:"streetName",sublocality_level_1:"city",locality:"city1",administrative_area_level_1:"state",postal_code:"postalCode",country:"country"},r={};e.address_components.forEach(e=>{r[t[e.types[0]]]=e.long_name});let a=[null==r?void 0:r.streetName,null==r?void 0:r.city1,null==r?void 0:r.country];return a.join(", ")}(e),r={lat:e.geometry.location.lat(),lng:e.geometry.location.lng()};c(r),g&&g(t)})),N(e),I(t),f){let r={lat:Number(null===(o=f.location)||void 0===o?void 0:o.latitude)||0,lng:Number(null===(s=f.location)||void 0===s?void 0:s.longitude)||0},a=[i,r],n=new t.LatLngBounds;for(var o,s,l=0;l<a.length;l++)n.extend(a[l]);e.fitBounds(n)}};return(0,n.useEffect)(()=>{if(f&&b){var e,t;let r={lat:Number(null===(e=f.location)||void 0===e?void 0:e.latitude)||0,lng:Number(null===(t=f.location)||void 0===t?void 0:t.longitude)||0},a=[i,r],n=new b.LatLngBounds;for(var o=0;o<a.length;o++)n.extend(a[o]);w.fitBounds(n)}},[i,null==f?void 0:f.location,x,w,b]),(0,a.jsxs)("div",{className:s().root,children:[!u&&(0,a.jsx)("div",{className:s().marker,children:(0,a.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),(0,a.jsxs)(o.ZP,{bootstrapURLKeys:{key:l.kr||"",libraries:["places"]},zoom:j,center:i,onDragEnd:k,yesIWantToUseGoogleMapApiInternals:!0,onGoogleApiLoaded(e){let{map:t,maps:r}=e;return M(t,r)},options:{fullscreenControl:u},children:[u&&(0,a.jsx)(m,{lat:i.lat,lng:i.lng}),!!f&&(0,a.jsx)(_,{lat:(null===(t=f.location)||void 0===t?void 0:t.latitude)||0,lng:(null===(r=f.location)||void 0===r?void 0:r.longitude)||0,shop:f,price:v})]})]})}},84169:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var a=r(85893);r(67294);var n=r(9008),o=r.n(n),i=r(5848),s=r(3075);function l(e){let{title:t,description:r=s.KM,image:n=s.T5,keywords:l=s.cU}=e,d=i.o6,c=t?t+" | "+s.k5:s.k5;return(0,a.jsxs)(o(),{children:[(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,a.jsx)("meta",{charSet:"utf-8"}),(0,a.jsx)("title",{children:c}),(0,a.jsx)("meta",{name:"description",content:r}),(0,a.jsx)("meta",{name:"keywords",content:l}),(0,a.jsx)("meta",{property:"og:type",content:"Website"}),(0,a.jsx)("meta",{name:"title",property:"og:title",content:c}),(0,a.jsx)("meta",{name:"description",property:"og:description",content:r}),(0,a.jsx)("meta",{name:"author",property:"og:author",content:d}),(0,a.jsx)("meta",{property:"og:site_name",content:d}),(0,a.jsx)("meta",{name:"image",property:"og:image",content:n}),(0,a.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,a.jsx)("meta",{name:"twitter:title",content:c}),(0,a.jsx)("meta",{name:"twitter:description",content:r}),(0,a.jsx)("meta",{name:"twitter:site",content:d}),(0,a.jsx)("meta",{name:"twitter:creator",content:d}),(0,a.jsx)("meta",{name:"twitter:image",content:n}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},55642:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var a=r(85893);r(67294);var n=r(86555),o=r(47301),i=r.n(o),s=r(88078);function l(e){var t,r;let{data:o,loading:l=!1,fullHeight:d,price:c,drawLine:u}=e,m={lat:Number(null==o?void 0:null===(t=o.location)||void 0===t?void 0:t.latitude)||0,lng:Number(null==o?void 0:null===(r=o.location)||void 0===r?void 0:r.longitude)||0};return(0,a.jsx)("div",{className:"".concat(i().wrapper," ").concat(d?i().fullHeight:""),children:l?(0,a.jsx)(s.Z,{variant:"rectangular",className:i().shimmer}):(0,a.jsx)(n.default,{location:m,defaultZoom:11,drawLine:u,price:c,readOnly:!0,shop:(null==o?void 0:o.delivery_type)==="pickup"?void 0:null==o?void 0:o.shop})})}},9730:function(e){e.exports={root:"map_root__3qcrq",marker:"map_marker__EnBz1",floatCard:"map_floatCard__1zZP1",price:"map_price__CTP0I",point:"map_point__GfLMi"}},70395:function(e){e.exports={wrapper:"orderInfo_wrapper__VOfS2",header:"orderInfo_header__BRR4r",title:"orderInfo_title__mdIf5",subtitle:"orderInfo_subtitle__MRrYx",text:"orderInfo_text__6R3WH",dot:"orderInfo_dot___JMW8",address:"orderInfo_address__9O1Zm",body:"orderInfo_body__UCS0u",flex:"orderInfo_flex____5q2",price:"orderInfo_price__KmY3l",discount:"orderInfo_discount__5BsvJ",totalPrice:"orderInfo_totalPrice__M0egx",courierBlock:"orderInfo_courierBlock__vQB6Q",courier:"orderInfo_courier__j9zmv",avatar:"orderInfo_avatar__ROqd5",imgWrapper:"orderInfo_imgWrapper___5hfV",rating:"orderInfo_rating__B5TcQ",naming:"orderInfo_naming__slzsU",name:"orderInfo_name__7ddxL",actions:"orderInfo_actions__MxKsd",iconBtn:"orderInfo_iconBtn__Q643h",footer:"orderInfo_footer__IvTTU",main:"orderInfo_main__U6XpY",deliveryBadge:"orderInfo_deliveryBadge__3ZAFV",changeRequired:"orderInfo_changeRequired__whR8d",exactAmount:"orderInfo_exactAmount__0xoUg"}},66776:function(e){e.exports={wrapper:"orderProducts_wrapper__RJtdf",header:"orderProducts_header__fX5HO",title:"orderProducts_title__WCPWW",body:"orderProducts_body__wISZ_",text:"orderProducts_text__t8VRX",flex:"orderProducts_flex__XgAuW",item:"orderProducts_item__kqY0C"}},17065:function(e){e.exports={root:"orderContainer_root__Dw_KY"}},92490:function(e){e.exports={root:"orderHeader_root__IBD_X",wrapper:"orderHeader_wrapper__hfYFz",shopInfo:"orderHeader_shopInfo__IRuOq",naming:"orderHeader_naming__h_MLi",title:"orderHeader_title__NhSXL",text:"orderHeader_text__fWyDT",shimmerTitle:"orderHeader_shimmerTitle__BGzRG",shimmerDesc:"orderHeader_shimmerDesc__ot5Mv",statusWrapper:"orderHeader_statusWrapper__O6u_G",status:"orderHeader_status__pXCV1",time:"orderHeader_time__15VmK",shimmer:"orderHeader_shimmer__Ah10t"}},47301:function(e){e.exports={wrapper:"orderMap_wrapper__h__VP",fullHeight:"orderMap_fullHeight__BsYPD",shimmer:"orderMap_shimmer__IX0_w"}}}]);