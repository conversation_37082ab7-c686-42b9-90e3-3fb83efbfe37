(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5610],{94184:function(t,e){var n; /*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/ !function(){"use strict";var r={}.hasOwnProperty;function o(){for(var t=[],e=0;e<arguments.length;e++){var n=arguments[e];if(n){var i=typeof n;if("string"===i||"number"===i)t.push(n);else if(Array.isArray(n)){if(n.length){var a=o.apply(null,n);a&&t.push(a)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){t.push(n.toString());continue}for(var l in n)r.call(n,l)&&n[l]&&t.push(l)}}}return t.join(" ")}t.exports?(o.default=o,t.exports=o):void 0!==(n=(function(){return o}).apply(e,[]))&&(t.exports=n)}()},83026:function(t,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/blog/[id]",function(){return n(83132)}])},84169:function(t,e,n){"use strict";n.d(e,{Z:function(){return c}});var r=n(85893);n(67294);var o=n(9008),i=n.n(o),a=n(5848),l=n(3075);function c(t){let{title:e,description:n=l.KM,image:o=l.T5,keywords:c=l.cU}=t,s=a.o6,u=e?e+" | "+l.k5:l.k5;return(0,r.jsxs)(i(),{children:[(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,r.jsx)("meta",{charSet:"utf-8"}),(0,r.jsx)("title",{children:u}),(0,r.jsx)("meta",{name:"description",content:n}),(0,r.jsx)("meta",{name:"keywords",content:c}),(0,r.jsx)("meta",{property:"og:type",content:"Website"}),(0,r.jsx)("meta",{name:"title",property:"og:title",content:u}),(0,r.jsx)("meta",{name:"description",property:"og:description",content:n}),(0,r.jsx)("meta",{name:"author",property:"og:author",content:s}),(0,r.jsx)("meta",{property:"og:site_name",content:s}),(0,r.jsx)("meta",{name:"image",property:"og:image",content:o}),(0,r.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,r.jsx)("meta",{name:"twitter:title",content:u}),(0,r.jsx)("meta",{name:"twitter:description",content:n}),(0,r.jsx)("meta",{name:"twitter:site",content:s}),(0,r.jsx)("meta",{name:"twitter:creator",content:s}),(0,r.jsx)("meta",{name:"twitter:image",content:o}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},83132:function(t,e,n){"use strict";n.r(e),n.d(e,{__N_SSG:function(){return X},default:function(){return K}});var r,o,i=n(85893),a=n(67294),l=n(84169),c=n(62302),s=n.n(c),u=n(27484),d=n.n(u),f=n(95785),h=n(90632),p=n.n(h),v=n(13221),m=n.n(v),w=n(79152),y=n.n(w),g=n(81315),b=n.n(g),j=n(22173),x=n.n(j),O=(r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),_=function(t){function e(e){var n=t.call(this,e)||this;return n.name="AssertionError",n}return O(e,t),e}(Error);function k(t,e){if(!t)throw new _(e)}function C(t){var e=Object.entries(t).filter(function(t){return null!=t[1]}).map(function(t){var e=t[0],n=t[1];return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(String(n)))});return e.length>0?"?".concat(e.join("&")):""}var N=n(94184),S=n.n(N),P=(o=function(t,e){return(o=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),E=function(){return(E=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},z=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function l(i){return function(l){return function(i){if(n)throw TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(l){i=[6,l],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,l])}}},B=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&0>e.indexOf(r)&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(t);o<r.length;o++)0>e.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]]);return n},I=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.openShareDialog=function(t){var n=e.props,r=n.onShareWindowClose,o=n.windowHeight,i=void 0===o?400:o,a=n.windowPosition,l=n.windowWidth,c=void 0===l?550:l;!function(t,e,n){var r=E({height:e.height,width:e.width,location:"no",toolbar:"no",status:"no",directories:"no",menubar:"no",scrollbars:"yes",resizable:"no",centerscreen:"yes",chrome:"yes"},B(e,["height","width"])),o=window.open(t,"",Object.keys(r).map(function(t){return"".concat(t,"=").concat(r[t])}).join(", "));if(n)var i=window.setInterval(function(){try{(null===o||o.closed)&&(window.clearInterval(i),n(o))}catch(t){console.error(t)}},1e3)}(t,E({height:i,width:c},"windowCenter"===(void 0===a?"windowCenter":a)?{left:window.outerWidth/2+(window.screenX||window.screenLeft||0)-c/2,top:window.outerHeight/2+(window.screenY||window.screenTop||0)-i/2}:{top:(window.screen.height-i)/2,left:(window.screen.width-c)/2}),r)},e.handleClick=function(t){var n,r,o;return n=void 0,r=void 0,o=function(){var e,n,r,o,i,a,l,c,s;return z(this,function(u){switch(u.label){case 0:var d;if(n=(e=this.props).beforeOnClick,r=e.disabled,o=e.networkLink,i=e.onClick,a=e.url,l=e.openShareDialogOnClick,c=o(a,e.opts),r)return[2];if(t.preventDefault(),!n||!((d=s=n())&&("object"==typeof d||"function"==typeof d)&&"function"==typeof d.then))return[3,2];return[4,s];case 1:u.sent(),u.label=2;case 2:return l&&this.openShareDialog(c),i&&i(t,c),[2]}})},new(r||(r=Promise))(function(t,i){function a(t){try{c(o.next(t))}catch(e){i(e)}}function l(t){try{c(o.throw(t))}catch(e){i(e)}}function c(e){var n;e.done?t(e.value):((n=e.value)instanceof r?n:new r(function(t){t(n)})).then(a,l)}c((o=o.apply(e,n||[])).next())})},e}return P(e,t),e.prototype.render=function(){var t=this.props,e=(t.beforeOnClick,t.children),n=t.className,r=t.disabled,o=t.disabledStyle,i=t.forwardedRef,l=(t.networkLink,t.networkName),c=(t.onShareWindowClose,t.openShareDialogOnClick,t.opts,t.resetButtonStyle),s=t.style,u=(t.url,t.windowHeight,t.windowPosition,t.windowWidth,B(t,["beforeOnClick","children","className","disabled","disabledStyle","forwardedRef","networkLink","networkName","onShareWindowClose","openShareDialogOnClick","opts","resetButtonStyle","style","url","windowHeight","windowPosition","windowWidth"])),d=S()("react-share__ShareButton",{"react-share__ShareButton--disabled":!!r,disabled:!!r},n),f=c?E(E({backgroundColor:"transparent",border:"none",padding:0,font:"inherit",color:"inherit",cursor:"pointer"},s),r&&o):E(E({},s),r&&o);return a.createElement("button",E({},u,{"aria-label":u["aria-label"]||l,className:d,onClick:this.handleClick,ref:i,style:f}),e)},e.defaultProps={disabledStyle:{opacity:.6},openShareDialogOnClick:!0,resetButtonStyle:!0},e}(a.Component),A=function(){return(A=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},H=function(t,e,n,r){function o(o,i){var l=n(o),c=A({},o);return Object.keys(l).forEach(function(t){delete c[t]}),a.createElement(I,A({},r,c,{forwardedRef:i,networkName:t,networkLink:e,opts:n(o)}))}return o.displayName="ShareButton-".concat(t),(0,a.forwardRef)(o)},M=H("facebook",function(t,e){var n=e.quote,r=e.hashtag;return k(t,"facebook.url"),"https://www.facebook.com/sharer/sharer.php"+C({u:t,quote:n,hashtag:r})},function(t){return{quote:t.quote,hashtag:t.hashtag}},{windowWidth:550,windowHeight:400}),V=H("twitter",function(t,e){var n=e.title,r=e.via,o=e.hashtags,i=void 0===o?[]:o,a=e.related,l=void 0===a?[]:a;return k(t,"twitter.url"),k(Array.isArray(i),"twitter.hashtags is not an array"),k(Array.isArray(l),"twitter.related is not an array"),"https://twitter.com/share"+C({url:t,text:n,via:r,hashtags:i.length>0?i.join(","):void 0,related:l.length>0?l.join(","):void 0})},function(t){return{hashtags:t.hashtags,title:t.title,via:t.via,related:t.related}},{windowWidth:550,windowHeight:400}),W=H("linkedin",function(t,e){var n=e.title,r=e.summary,o=e.source;return k(t,"linkedin.url"),"https://linkedin.com/shareArticle"+C({url:t,mini:"true",title:n,summary:r,source:o})},function(t){return{title:t.title,summary:t.summary,source:t.source}},{windowWidth:750,windowHeight:600}),Z=H("email",function(t,e){var n=e.subject,r=e.body,o=e.separator;return"mailto:"+C({subject:n,body:r?r+o+t:t})},function(t){return{subject:t.subject,body:t.body,separator:t.separator||" "}},{openShareDialogOnClick:!1,onClick:function(t,e){window.location.href=e}}),D=n(6734),T=n(73714),L=n(5848),R=n(37562);function q(t){var e,n,r,o,a,l,c;let{data:u}=t,{t:h}=(0,D.$G)(),v=L.o6+"/blog/"+(null==u?void 0:u.uuid),w=async()=>{try{await navigator.clipboard.writeText(v),(0,T.Vp)(h("copied"))}catch(t){(0,T.vU)("Failed to copy!")}};return(0,i.jsx)("div",{className:s().container,children:(0,i.jsxs)("div",{className:"container",children:[(0,i.jsxs)("div",{className:s().header,children:[(0,i.jsx)("h1",{className:s().title,children:null==u?void 0:null===(e=u.translation)||void 0===e?void 0:e.title}),(0,i.jsx)("p",{className:s().muted,children:d()(null==u?void 0:u.created_at).format("MMM DD, HH:mm")})]}),(0,i.jsx)("div",{className:s().hero,children:(0,i.jsx)(R.Z,{fill:!0,src:(0,f.Z)(null==u?void 0:u.img),alt:(null==u?void 0:null===(n=u.translation)||void 0===n?void 0:n.title)||"",sizes:"(max-width: 768px) 600px, 1072px"})}),(0,i.jsxs)("main",{className:s().content,children:[(0,i.jsx)("div",{className:s().sticky,children:(0,i.jsxs)("div",{className:s().share,children:[(0,i.jsx)(M,{url:v,title:null==u?void 0:null===(r=u.translation)||void 0===r?void 0:r.title,className:s().shareItem,children:(0,i.jsx)(p(),{})}),(0,i.jsx)(V,{url:v,title:null==u?void 0:null===(o=u.translation)||void 0===o?void 0:o.title,className:s().shareItem,children:(0,i.jsx)(m(),{})}),(0,i.jsx)(W,{url:v,title:null==u?void 0:null===(a=u.translation)||void 0===a?void 0:a.title,className:s().shareItem,children:(0,i.jsx)(y(),{})}),(0,i.jsx)(Z,{url:v,title:null==u?void 0:null===(l=u.translation)||void 0===l?void 0:l.title,className:s().shareItem,children:(0,i.jsx)(b(),{})}),(0,i.jsx)("button",{className:s().shareItem,onClick:w,children:(0,i.jsx)(x(),{})})]})}),(0,i.jsx)("div",{dangerouslySetInnerHTML:{__html:(null==u?void 0:null===(c=u.translation)||void 0===c?void 0:c.description)||""}})]})]})})}var G=n(11163),U=n(88767),F=n(12838),X=!0;function K(t){var e,n,r,o,a;let{}=t,{i18n:c}=(0,D.$G)(),s=c.language,{query:u}=(0,G.useRouter)(),d=String(u.id),{data:h,error:p}=(0,U.useQuery)(["blog",d,s],()=>F.Z.getById(d),{staleTime:0});return p&&console.log("error => ",p),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(l.Z,{title:null==h?void 0:null===(e=h.data)||void 0===e?void 0:null===(n=e.translation)||void 0===n?void 0:n.title,description:null==h?void 0:null===(r=h.data)||void 0===r?void 0:null===(o=r.translation)||void 0===o?void 0:o.short_desc,image:(0,f.Z)(null==h?void 0:null===(a=h.data)||void 0===a?void 0:a.img)}),(0,i.jsx)(q,{data:null==h?void 0:h.data})]})}},12838:function(t,e,n){"use strict";var r=n(25728);e.Z={getAll:t=>r.Z.get("/rest/blogs/paginate?type=blog",{params:t}),getById:(t,e)=>r.Z.get("/rest/blogs/".concat(t),{params:e}),getLastBlog:t=>r.Z.get("rest/last-blog/show",{params:t}),getAllNews:t=>r.Z.get("/rest/blogs/paginate?type=notification",{params:t}),getNewsById:(t,e)=>r.Z.get("/rest/blogs/".concat(t),{params:e})}},62302:function(t){t.exports={container:"blogContent_container__0AO_S",header:"blogContent_header__N1_fv",title:"blogContent_title__Owqcp",muted:"blogContent_muted__c04SM",text:"blogContent_text__MtKqV",hero:"blogContent_hero__rbv68",content:"blogContent_content__Ntifa",sticky:"blogContent_sticky__iQyWB",share:"blogContent_share__5Vk4C",shareItem:"blogContent_shareItem__f7LuF"}},9008:function(t,e,n){t.exports=n(83121)},90632:function(t,e,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},a=function(t,e){var n={};for(var r in t)!(e.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},l=function(t){var e=t.color,n=t.size,r=void 0===n?24:n,l=(t.children,a(t,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",i({},l,{className:c,width:r,height:r,fill:void 0===e?"currentColor":e,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.879V14.89h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.989C18.343 21.129 22 16.99 22 12c0-5.523-4.477-10-10-10z"}))},c=o.default.memo?o.default.memo(l):l;t.exports=c},79152:function(t,e,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},a=function(t,e){var n={};for(var r in t)!(e.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},l=function(t){var e=t.color,n=t.size,r=void 0===n?24:n,l=(t.children,a(t,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",i({},l,{className:c,width:r,height:r,fill:void 0===e?"currentColor":e,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M6.94 5a2 2 0 1 1-4-.002 2 2 0 0 1 4 .002zM7 8.48H3V21h4V8.48zm6.32 0H9.34V21h3.94v-6.57c0-3.66 4.77-4 4.77 0V21H22v-7.93c0-6.17-7.06-5.94-8.72-2.91l.04-1.68z"}))},c=o.default.memo?o.default.memo(l):l;t.exports=c},22173:function(t,e,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},a=function(t,e){var n={};for(var r in t)!(e.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},l=function(t){var e=t.color,n=t.size,r=void 0===n?24:n,l=(t.children,a(t,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",i({},l,{className:c,width:r,height:r,fill:void 0===e?"currentColor":e,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M13.06 8.11l1.415 1.415a7 7 0 0 1 0 9.9l-.354.353a7 7 0 0 1-9.9-9.9l1.415 1.415a5 5 0 1 0 7.071 7.071l.354-.354a5 5 0 0 0 0-7.07l-1.415-1.415 1.415-1.414zm6.718 6.011l-1.414-1.414a5 5 0 1 0-7.071-7.071l-.354.354a5 5 0 0 0 0 7.07l1.415 1.415-1.415 1.414-1.414-1.414a7 7 0 0 1 0-9.9l.354-.353a7 7 0 0 1 9.9 9.9z"}))},c=o.default.memo?o.default.memo(l):l;t.exports=c},81315:function(t,e,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},a=function(t,e){var n={};for(var r in t)!(e.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},l=function(t){var e=t.color,n=t.size,r=void 0===n?24:n,l=(t.children,a(t,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",i({},l,{className:c,width:r,height:r,fill:void 0===e?"currentColor":e,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm9.06 8.683L5.648 6.238 4.353 7.762l7.72 6.555 7.581-6.56-1.308-1.513-6.285 5.439z"}))},c=o.default.memo?o.default.memo(l):l;t.exports=c},13221:function(t,e,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},a=function(t,e){var n={};for(var r in t)!(e.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},l=function(t){var e=t.color,n=t.size,r=void 0===n?24:n,l=(t.children,a(t,["color","size","children"])),c="remixicon-icon "+(l.className||"");return o.default.createElement("svg",i({},l,{className:c,width:r,height:r,fill:void 0===e?"currentColor":e,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M22.162 5.656a8.384 8.384 0 0 1-2.402.658A4.196 4.196 0 0 0 21.6 4c-.82.488-1.719.83-2.656 1.015a4.182 4.182 0 0 0-7.126 3.814 11.874 11.874 0 0 1-8.62-4.37 4.168 4.168 0 0 0-.566 2.103c0 1.45.738 2.731 1.86 3.481a4.168 4.168 0 0 1-1.894-.523v.052a4.185 4.185 0 0 0 3.355 4.101 4.21 4.21 0 0 1-1.89.072A4.185 4.185 0 0 0 7.97 16.65a8.394 8.394 0 0 1-6.191 1.732 11.83 11.83 0 0 0 6.41 1.88c7.693 0 11.9-6.373 11.9-11.9 0-.18-.005-.362-.013-.54a8.496 8.496 0 0 0 2.087-2.165z"}))},c=o.default.memo?o.default.memo(l):l;t.exports=c}},function(t){t.O(0,[9774,2888,179],function(){return t(t.s=83026)}),_N_E=t.O()}]);