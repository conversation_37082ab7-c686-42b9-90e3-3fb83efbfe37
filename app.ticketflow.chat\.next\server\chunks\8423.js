"use strict";
exports.id = 8423;
exports.ids = [8423];
exports.modules = {

/***/ 18423:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const cartService = {
    guestStore: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/rest/cart`, data),
    guestGet: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/cart/${id}`, {
            params
        }),
    store: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/cart`, data),
    get: async (params)=>{
        try {
            return await _request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/dashboard/user/cart`, {
                params
            });
        } catch (error) {
            // Silently handle cart API failures (common when user is not authenticated)
            // Don't log as warning since this is expected behavior in some cases
            return {
                data: {
                    id: 0,
                    shop_id: 0,
                    user_carts: [],
                    total_price: 0,
                    group: false,
                    owner_id: undefined
                },
                message: "Cart not available",
                status: true,
                timestamp: new Date().toISOString()
            };
        }
    },
    deleteCartProducts: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"]["delete"] */ .Z["delete"](`/dashboard/user/cart/product/delete`, {
            data
        }),
    delete: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"]["delete"] */ .Z["delete"](`/dashboard/user/cart/delete`, {
            data
        }),
    insert: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/cart/insert-product`, data),
    open: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/cart/open`, data),
    setGroup: (id)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/cart/set-group/${id}`),
    guestLeave: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"]["delete"] */ .Z["delete"](`/rest/cart/member/delete`, {
            params
        }),
    join: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/rest/cart/open`, data),
    statusChange: (uuid, data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/rest/cart/status/${uuid}`, data),
    deleteGuestProducts: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"]["delete"] */ .Z["delete"](`/rest/cart/product/delete`, {
            data
        }),
    deleteGuest: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"]["delete"] */ .Z["delete"](`/dashboard/user/cart/member/delete`, {
            params
        }),
    insertGuest: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/rest/cart/insert-product`, data)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (cartService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;