exports.id = 5728;
exports.ids = [5728];
exports.modules = {

/***/ 75167:
/***/ ((module) => {

// Exports
module.exports = {
	"root": "alert_root__WFGuJ",
	"success": "alert_success__x8WI3",
	"warning": "alert_warning__z__oT",
	"error": "alert_error__igC0t",
	"info": "alert_info__Bn5Y5",
	"icon": "alert_icon__LI8TL",
	"message": "alert_message__kbkpY",
	"layout": "alert_layout__mpDvp"
};


/***/ }),

/***/ 38362:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ Alert)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(11060);
/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _alert_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(75167);
/* harmony import */ var _alert_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_alert_module_scss__WEBPACK_IMPORTED_MODULE_3__);




function Alert({ icon , message , closeToast , type  }) {
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: `${(_alert_module_scss__WEBPACK_IMPORTED_MODULE_3___default().root)} ${(_alert_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[type]}`,
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                className: (_alert_module_scss__WEBPACK_IMPORTED_MODULE_3___default().icon),
                children: icon
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: (_alert_module_scss__WEBPACK_IMPORTED_MODULE_3___default().layout),
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                    className: (_alert_module_scss__WEBPACK_IMPORTED_MODULE_3___default().message),
                    children: message
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                type: "button",
                onClick: closeToast,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2___default()), {})
            })
        ]
    });
}


/***/ }),

/***/ 74621:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Kp": () => (/* binding */ warning),
/* harmony export */   "Vp": () => (/* binding */ success),
/* harmony export */   "vU": () => (/* binding */ error)
/* harmony export */ });
/* unused harmony export info */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3590);
/* harmony import */ var remixicon_react_CheckboxCircleLineIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4634);
/* harmony import */ var remixicon_react_CheckboxCircleLineIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CheckboxCircleLineIcon__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var remixicon_react_ErrorWarningLineIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(53112);
/* harmony import */ var remixicon_react_ErrorWarningLineIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ErrorWarningLineIcon__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var remixicon_react_InformationLineIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(12564);
/* harmony import */ var remixicon_react_InformationLineIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_InformationLineIcon__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(38362);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_1__]);
react_toastify__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];






const success = (msg, options)=>{
    (0,react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast)(/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_alert__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
        icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_CheckboxCircleLineIcon__WEBPACK_IMPORTED_MODULE_2___default()), {}),
        message: msg,
        type: "success"
    }), options);
};
const warning = (msg, options)=>{
    (0,react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast)(/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_alert__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
        icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ErrorWarningLineIcon__WEBPACK_IMPORTED_MODULE_3___default()), {}),
        message: msg,
        type: "warning"
    }), options);
};
const error = (msg, options)=>{
    (0,react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast)(/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_alert__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
        icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ErrorWarningLineIcon__WEBPACK_IMPORTED_MODULE_3___default()), {}),
        message: msg,
        type: "error"
    }), options);
};
const info = (msg, options)=>{
    toast(/*#__PURE__*/ _jsx(Alert, {
        icon: /*#__PURE__*/ _jsx(InformationLineIcon, {}),
        message: msg,
        type: "info"
    }), options);
};

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 5848:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "DA": () => (/* binding */ G_TAG),
/* harmony export */   "DH": () => (/* binding */ EXTERNAL_PAYMENTS),
/* harmony export */   "OU": () => (/* binding */ ADMIN_PANEL_URL),
/* harmony export */   "T5": () => (/* binding */ API_URL),
/* harmony export */   "_n": () => (/* binding */ BASE_URL),
/* harmony export */   "de": () => (/* binding */ UNPAID_STATUSES),
/* harmony export */   "kr": () => (/* binding */ MAP_API_KEY),
/* harmony export */   "o6": () => (/* binding */ WEBSITE_URL),
/* harmony export */   "yA": () => (/* binding */ IMAGE_URL)
/* harmony export */ });
// Do not edit this file
const WEBSITE_URL = "http://app.ticketflow.chat";
const BASE_URL = "http://localhost:8000";
const ADMIN_PANEL_URL = "http://admin.ticketflow.chat";
const API_URL = BASE_URL + "/api/v1/";
const IMAGE_URL = BASE_URL + "/storage/images/";
const MAP_API_KEY = "AIzaSyAJcyKXGQqn7dPIgr1wrIf_SXNxYLannxQ";
const G_TAG = "G-NN1YV8NXGD";
const UNPAID_STATUSES = [
    "progress",
    "canceled",
    "rejected"
];
const EXTERNAL_PAYMENTS = [
    "stripe",
    "razorpay",
    "paystack",
    "moyasar",
    "paytabs",
    "mercado-pago",
    "flutterWave",
    "paypal",
    "pay-fast"
];


/***/ }),

/***/ 61664:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(22021);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57987);
/* harmony import */ var i18next_http_backend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(64329);
/* harmony import */ var _locales_en_translation_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(87551);
/* harmony import */ var _locales_pt_BR_translation_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(60879);
/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(3075);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([i18next__WEBPACK_IMPORTED_MODULE_0__, react_i18next__WEBPACK_IMPORTED_MODULE_1__, i18next_http_backend__WEBPACK_IMPORTED_MODULE_2__]);
([i18next__WEBPACK_IMPORTED_MODULE_0__, react_i18next__WEBPACK_IMPORTED_MODULE_1__, i18next_http_backend__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);






const resources = {
    en: {
        translation: _locales_en_translation_json__WEBPACK_IMPORTED_MODULE_3__
    },
    "pt-BR": {
        translation: _locales_pt_BR_translation_json__WEBPACK_IMPORTED_MODULE_4__
    }
};
i18next__WEBPACK_IMPORTED_MODULE_0__["default"].use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).use(i18next_http_backend__WEBPACK_IMPORTED_MODULE_2__["default"]).init({
    resources,
    fallbackLng: constants_config__WEBPACK_IMPORTED_MODULE_5__/* .DEFAULT_LANGUAGE */ .k$,
    // lng: getCookieFromBrowser("NEXT_LOCALE") || DEFAULT_LANGUAGE,
    supportedLngs: [
        "aa",
        "ab",
        "ae",
        "af",
        "ak",
        "am",
        "an",
        "ar",
        "as",
        "av",
        "ay",
        "az",
        "az",
        "ba",
        "be",
        "bg",
        "bh",
        "bi",
        "bm",
        "bn",
        "bo",
        "br",
        "bs",
        "ca",
        "ce",
        "ch",
        "co",
        "cr",
        "cs",
        "cu",
        "cv",
        "cy",
        "da",
        "de",
        "dv",
        "dz",
        "ee",
        "el",
        "en",
        "eo",
        "es",
        "et",
        "eu",
        "fa",
        "ff",
        "fi",
        "fj",
        "fo",
        "fr",
        "fy",
        "ga",
        "gd",
        "gl",
        "gn",
        "gu",
        "gv",
        "ha",
        "he",
        "hi",
        "ho",
        "hr",
        "ht",
        "hu",
        "hy",
        "hz",
        "ia",
        "id",
        "ie",
        "ig",
        "ii",
        "ik",
        "io",
        "is",
        "it",
        "iu",
        "ja",
        "jv",
        "ka",
        "kg",
        "ki",
        "kj",
        "kk",
        "kl",
        "km",
        "kn",
        "ko",
        "kr",
        "ks",
        "ku",
        "kv",
        "kw",
        "ky",
        "la",
        "lb",
        "lg",
        "li",
        "ln",
        "lo",
        "lt",
        "lu",
        "lv",
        "mg",
        "mh",
        "mi",
        "mk",
        "ml",
        "mn",
        "mr",
        "ms",
        "mt",
        "my",
        "na",
        "nb",
        "nd",
        "ne",
        "ng",
        "nl",
        "nn",
        "no",
        "nr",
        "nv",
        "ny",
        "oc",
        "oj",
        "om",
        "or",
        "os",
        "pa",
        "pi",
        "pl",
        "ps",
        "pt",
        "pt-BR",
        "qu",
        "rm",
        "rn",
        "ro",
        "ru",
        "rw",
        "sa",
        "sc",
        "sd",
        "se",
        "sg",
        "si",
        "sk",
        "sl",
        "sm",
        "sn",
        "so",
        "sq",
        "sr",
        "ss",
        "st",
        "su",
        "sv",
        "sw",
        "ta",
        "te",
        "tg",
        "th",
        "ti",
        "tk",
        "tl",
        "tn",
        "to",
        "tr",
        "ts",
        "tt",
        "tw",
        "ty",
        "ug",
        "uk",
        "ur",
        "uz",
        "ve",
        "vi",
        "vo",
        "wa",
        "wo",
        "xh",
        "yi",
        "yo",
        "za",
        "zh",
        "zu"
    ],
    ns: [
        "translation",
        "errors"
    ],
    defaultNS: "translation"
});
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__["default"]);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 25728:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(99648);
/* harmony import */ var i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(61664);
/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5848);
/* harmony import */ var utils_session__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(24941);
/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(74621);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__, i18n__WEBPACK_IMPORTED_MODULE_1__, utils_session__WEBPACK_IMPORTED_MODULE_3__, components_alert_toast__WEBPACK_IMPORTED_MODULE_4__]);
([axios__WEBPACK_IMPORTED_MODULE_0__, i18n__WEBPACK_IMPORTED_MODULE_1__, utils_session__WEBPACK_IMPORTED_MODULE_3__, components_alert_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);
//@ts-nocheck





const request = axios__WEBPACK_IMPORTED_MODULE_0__["default"].create({
    baseURL: constants_constants__WEBPACK_IMPORTED_MODULE_2__/* .API_URL */ .T5
});
request.interceptors.request.use((config)=>{
    const token = (0,utils_session__WEBPACK_IMPORTED_MODULE_3__/* .getCookieFromBrowser */ .zt)("access_token");
    const locale = i18n__WEBPACK_IMPORTED_MODULE_1__/* ["default"].language */ .Z.language;
    if (token) {
        config.headers.Authorization = token;
    }
    config.params = {
        lang: locale,
        ...config.params
    };
    return config;
}, (error)=>errorHandler(error));
function errorHandler(error) {
    if (error?.response) {
        if (error?.response?.status === 403) {} else if (error?.response?.status === 401) {
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_4__/* .error */ .vU)(i18n__WEBPACK_IMPORTED_MODULE_1__/* ["default"].t */ .Z.t("unauthorized"), {
                toastId: "unauthorized"
            });
            (0,utils_session__WEBPACK_IMPORTED_MODULE_3__/* .removeCookie */ .nJ)("user");
            (0,utils_session__WEBPACK_IMPORTED_MODULE_3__/* .removeCookie */ .nJ)("access_token");
            window.location.replace("/login");
        } else if (error?.response?.status === 404 && error?.config?.url?.includes("/dashboard/user/cart")) {
        // Don't log 404 errors for cart endpoints - these are expected when user is not authenticated
        // The cart service will handle this gracefully with fallback
        } else {
            console.log("error => ", error);
        }
    } else {
        console.log("error => ", error);
    }
    return Promise.reject(error.response);
}
request.interceptors.response.use((response)=>response.data, errorHandler);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (request);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 24941:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "d8": () => (/* binding */ setCookie),
/* harmony export */   "ej": () => (/* binding */ getCookie),
/* harmony export */   "nJ": () => (/* binding */ removeCookie),
/* harmony export */   "zt": () => (/* binding */ getCookieFromBrowser)
/* harmony export */ });
/* unused harmony export getCookieFromServer */
/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(69915);
/* harmony import */ var next_cookies__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(7486);
/* harmony import */ var next_cookies__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_cookies__WEBPACK_IMPORTED_MODULE_1__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_0__]);
js_cookie__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];


const isBrowser = "undefined" !== "undefined";
const getCookieFromBrowser = (key)=>{
    return js_cookie__WEBPACK_IMPORTED_MODULE_0__["default"].get(key);
};
const getCookieFromServer = (ctx, key = "id_token")=>{
    const cookie = next_cookies__WEBPACK_IMPORTED_MODULE_1___default()(ctx);
    const token = cookie && cookie[key] ? cookie[key] : false;
    if (!token) {
        return null;
    }
    return token;
};
const getCookie = (key, context)=>{
    return isBrowser ? getCookieFromBrowser(key) : getCookieFromServer(context, key);
};
const setCookie = (key, token)=>{
    js_cookie__WEBPACK_IMPORTED_MODULE_0__["default"].set(key, token, {
        expires: 7
    });
};
const removeCookie = (key)=>{
    js_cookie__WEBPACK_IMPORTED_MODULE_0__["default"].remove(key);
};

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 87551:
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"search.restaurants.products":"Search restaurants and products","search.products.in":"Search products in {{shop}}","delivery":"Delivery","delivery.address":"Delivery address","delivery.range":"{{times}} min","delivery.price":"Delivery price","delivery.time":"Delivery time","sorted.by":"Sorted by","filter":"Filter","recommended":"Recommended","news.week":"News of the week","all.restaurants":"All restaurants","number.of.foods":"{{count}} foods","popular":"Popular","foods":"Foods","orders":"Orders","liked":"Liked","order":"Order","your.orders":"Your orders","total":"Total","cart.empty":"Cart is empty","pickup":"Pickup","type.here":"Type here","payment":"Payment","payment.method":"Payment method","payment.status":"Payment status","promo.code":"Promo code","add":"Add","enter":"Enter","subtotal":"Subtotal","service.fee":"Service fee","continue.payment":"Continue payment","more":"More","working.time":"Working time","start.group.order":"Start group order","clear.bag":"Clear bag","save":"Save","add.promocode":"Add promo code","clear":"Clear","sign.up":"Sign up","login":"Login","app.text":"There\'s more to love in the app.","dont.have.account":"Don\'t have an account?","keep.logged":"Keep me logged in","forgot.password":"Forgot password","access.quickly":"or access quickly","have.account":"Already have an account?","reset.password":"Reset password","reset.password.text":"Please provide email address and we\'ll send you code which you can change your password.","send":"Send","enter.otp.code":"Enter OTP code","enter.code.text":"We are send OTP code to {{phone}}","send.new":"Send new","confirm":"Confirm","restaurant":"Restaurant","found.number.results":"Found {{count}} results","enter.delivery.address":"Enter delivery address","search":"Search","submit":"Submit","view.profile":"View profile","settings":"Settings","help":"Help","log.out":"Log out","profile":"Profile","date.of.birth":"Date of birth","update.password":"Update password","old.password":"Old password","password.confirmation":"Password confirmation","cancel":"Cancel","gender":"Gender","choose.here":"Choose here","male":"Male","female":"Female","notification":"Notification","push.notifications":"Push notifications","on":"On","off":"Off","send.news.email":"Send news email","discount.notifications":"Discount notifications","order.verify":"Order verify","back":"Back","active.orders":"Active orders","order.history":"Order history","new":"New","accepted":"Accepted","ready":"Ready","on_a_way":"On a way","delivered":"Delivered","cancelled":"Cancelled","driver":"Driver","support":"Support","repeat.order":"Repeat order","liked.restaurants":"Liked restaurants","have.questions":"Still have questions?","questions.text":"Can’t find the answer you’re looking or? Please chat to our friendly team.","call.support":"Call to support","group.order.text":"You fully manage the order and confirm the address. Team members can add a product from a location of your choice.","start":"Start","copied":"Copied to clipboard!","group.members":"Group members","choosing":"Choosing","clear.cart":"Are you sure to clear the cart?","rating":"Rating","special.offers":"Special offers","free.delivery":"Free delivery","show":"Show","all":"All","languages":"Languages","currency":"Currency","no":"No","yes":"Yes","order.for.address":"Order for this address?","replace.cart.prompt":"You can only add items from one restaurant to your shopping cart.","saved":"Saved","required":"Required","passwords.dont.match":"Passwords don\'t match","password.should.contain":"Password should contain at least 6 characters","shop.tax":"Shop tax","order.tax":"Order tax","vat.tax":"VAT tax","today":"Today","tomorrow":"Tomorrow","min":"min","edit":"Edit","order.details":"Order details","cancel.order":"Cancel order","under":"Under","bonus":"Bonus","are.you.sure.cancel.order":"Are you sure to cancel this order?","order.cancelled":"Order cancelled","wallet":"Wallet","choose.payment.method":"Please, choose payment method","refund":"Refund","leave.feedback":"Leave feedback","thanks.for.feedback":"Thank you for your feedback!","order.refund":"Order refund","why.refund":"Why do you want to refund?","request.sent":"Request sent successfully!","request.not.sent":"You request didn\'t send!","pending":"Pending","approved":"Approved","rejected":"Rejected","refunds":"Refunds","products":"Products","your.comment":"Your comment","answer":"Answer","order.id":"Order ID","go.to.order":"Go to order","price":"Price","closed":"Closed","done":"Done","manage.group.order":"Manage group order","manage.order":"Manage order","join.group.order":"Join group order","join.group.text":"You can only select products from the restaurant chosen by the creator of the group","join":"Join","leave.group":"Leave group","are.you.sure.leave.group":"Are you sure to leave group order?","edit.order":"Edit order","you.kicked.from.group":"You have been kicked from group order","group.order.permission":"Some group members haven\'t finished making order. Are you sure to continue?","see.all":"See all","all.shops":"All shops","shops":"Shops","catalog":"Catalog","ingredients":"Ingredients","transaction.id":"Transaction ID","wallet.history":"Wallet history","sender":"Sender","date":"Date","note":"Note","topup.wallet":"Topup wallet","your.order":"Your order","your.order.status.updated.text":"Your order status has been updated! Click \'Show\' to see order details.","help.center":"Help center","message":"Message","login.first":"Please, login first","add.to.bag":"Add to bag","be.seller":"Become seller","general":"General","logo.image":"Logo image","background.image":"Background image","delivery.info":"Delivery info","minute":"Minute","day":"Day","month":"Month","address":"Address","seller.request.under.review":"Your request to become seller is currently under review.","seller.request.accepted":"Your request to become seller is accepted.","start.price":"Start price","shop.closed":"Shop is closed","no.zone.title":"We don\'t deliver here yet :(","no.zone.text":"But we add dozens of new places every week. Maybe we\'ll be here soon! If you enter your email, we\'ll tell you as soon as we\'re available. We promise not to spam!","payment.type":"Payment type","verify":"Verify","verify.email":"Email verification","verify.text":"Please, enter the verification code we’ve sent you to","verify.didntRecieveCode":"Didn’t receive the code?","resend":"Send again","should.match":"Passwords should match","verify.send":"Verification code send successfully","email.inuse":"The email has already been taken.","verify.error":"Wrong verification code","about":"About","become.affiliate":"Become an Affiliate","careers":"Careers","blog":"Blog","get.helps":"Get helps","add.your.restaurant":"Add your restaurant","sign.up.to.deliver":"Sign up to deliver","privacy.policy":"Privacy Policy","terms":"Terms","tags":"Tags","near_you":"Near you","open_now":"Open now","copy.code":"Copy code","balance":"Balance","referrals":"Referrals","referral.title":"{{price_from}} for you, {{price_to}} for a friend","referral.text":"Friends can get up to {{price_to}} off — you’ll get {{price_from}} when they place their first order.","role":"Role","category":"Category","no.items":"No items","referral.terms":"Referral terms","login.or.create.account":"Login or create account","sign.in.be.seller":"Sign in to be seller","error.400":"Error occured. Please, try again later","deals":"Deals","more.info":"More info","ratings":"Ratings","open.until":"Open until","no.orders.found":"You don\'t have any orders yet","go.to.menu":"Go to menu","no.refunds.found":"You don\'t have any order refunds yet. You can create a refund request from delivered orders.","no.active.orders.found":"No active orders","no.wallet.found":"You don\'t have any wallet transactions yet","recent.searches":"Recent searches","no.liked.restaurants":"You don\'t have any liked restaurants yet","try.again":"Try again","unauthorized":"Unauthorized","you.cannot.join":"You cannot join. Invalid group order","delivery.zone.not.available":"Sorry, we’re not available here","leave.group.prompt":"You have joined in group order. In order to add product, leave group first!","hours.ago":"hours ago","become.delivery":"Become a delivery driver","become.delivery.text":"Instead of traditional food delivery jobs where the hours aren’t flexible, try being your own boss with Foodyman. Get paid to deliver on your schedule using the food delivery app most downloaded by customers.","discount":"Discount","only.opened":"Only opened","schedule":"Schedule","shop.closed.choose.other.day":"Shop is closed in this day. Please, select another day.","edit.schedule":"Edit schedule","pickup.address":"Pickup address","pickup.time":"Pickup time","branch":"Branch","branches":"Branches","branches.not.found":"Branches not found","out.of.stock":"Out of stock","hour":"Hour","h":"hour","no.restaurants":"Restaurants not found according to your request","no.shops":"Shops not found according to your request","sms.not.sent":"Sms not sent!","email.or.phone":"Email or phone","login.invalid":"Login or password is invalid","verify.phone":"Phone verification","recipes":"Recipes","recipes.title":"Recipes","recipes.description":"Choose your favorite food recipe and buy as you wish","no.recipes":"Recipes not found according to your request","total.time":"Total time","calories":"Calories","servings":"Servings","instructions":"Instructions","nutritions":"Nutritions","add.items.to.cart":"Add {{number}} items to cart","recipe.discount.condition":"If you buy all ingredients you can get discount by","go.to.recipe.order":"Ingredients added to cart successfully.","recipe.discount.definition":"You got recipe discount","insufficient.wallet.balance":"Insufficient wallet balance","go.to.admin.panel":"Go to admin panel","have.not.password":"You have not set password yet. Please, make sure you have a password in system before you create a request for become seller","email":"Email","edit.phone":"Edit phone","verified":"Verified","something.went.wrong":"Something went wrong","phone.required":"Phone number is required","no.careers.found":"Careers not found according to your request","welcome.title":"Get your favorite foods delivered","welcome.description":"Choose your address and start ordering","do.you.have.restaurant":"Do you have a restaurant?","deliver.title":"Looking for delivery driver jobs?","welcome.features.title":"Other options for you","start.ordering":"Start ordering","why.choose.us":"Why choose us","why.choose.us.first.title":"Choose what you want","why.choose.us.first.text":"Select items from your favorite stores at Foodyman","why.choose.us.second.title":"See real-time updates","why.choose.us.second.text":"Personal shoppers pick items with care","why.choose.us.third.title":"Get your items same-day","why.choose.us.third.text":"Enjoy Foodyman\'s 100% quality guarantee on every order","choose.recomended.address":"Choose recomended address","place.for.ad":"Place for your advertisement here","ok":"Ok","people.trust.us":"People trust us","delivery.was.successfull":"Delivery was successfull","view.our.insta":"View our Instagram","latest.blog":"Latest blog","ads":"Ads","faq":"Frequently asked questions","view.more":"View more","transactions":"Transactions","mark.read":"Mark all as read","notifications":"Notifications","no.notifications":"Notifications not found according to your request","news":"News","order.for.someone":"I want to order for someone","user.details.empty":"Please, fill user details","phone.invalid":"Phone number is invalid","door.to.door.delivery":"Door to door delivery","sender.details":"Sender details","parcel.details":"Parcel details","receiver.details":"Receiver details","home":"Home","work":"Work","other":"Other","address.type":"Address type","stage":"Stage","room":"Room","active.parcels":"Active parcels","parcel.history":"Parcel history","receiver":"Receiver","parcel":"Parcel","parcel.cancelled":"Parcel cancelled","phone.number":"Phone number","type":"Type","parcels":"Parcels","sign.in.parcel.order":"Sign in to use door to door delivery","up.to.weight":"up to {{ number }} kg","up.to.length":"up to {{ number }} m","length":"Length","width":"Width","height":"Height","weight":"Weight","hero.title":"Explore Our Shops with fast delivery","offers":"Offers","view.all":"View all","number.of.offers":"{{number}} offers","door.to.door.delivery.service":"Your personal door-to-door delivery service","favorite.brands":"Favorite brands","popular.near.you":"Popular near you","daily.offers":"Daily offers","follow.us":"Follow us on Social Media","home.page":"Home page","all.stories":"All stories","categories":"Categories","trending":"Trending","delivery.free":"Delivery free","delivery.with.in":"Delivery with in","shop.banner.title":"Something hot. Something tasty.","shop.banner.desc":"Top ratings and consistently great service","order.now":"Order now","error.something.went.wrong":"Oops, something went wrong!","supported.image.formats.only":"Supported only image formats!","invalid.image.source":"Invalid image source","user.successfully.login":"User successfully logged in","verify.code.sent":"Verification code sent","empty":"Empty","welcome":"Welcome","image":"Image","banner":"Banner","brand.logo":"Brand logo","brand.logo.dark":"Brand logo dark","shop":"Shop"}');

/***/ }),

/***/ 60879:
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"search.restaurants.products":"Buscar restaurantes e produtos","search.products.in":"Buscar produtos em {{shop}}","delivery":"Entrega","delivery.address":"Endereço de entrega","delivery.range":"{{times}} min","delivery.price":"Preço da entrega","delivery.time":"Tempo de entrega","sorted.by":"Ordenado por","filter":"Filtro","recommended":"Recomendado","news.week":"Novidades da semana","all.restaurants":"Todos os restaurantes","number.of.foods":"{{count}} pratos","popular":"Popular","foods":"Pratos","orders":"Pedidos","liked":"Curtidos","order":"Pedido","your.orders":"Seus pedidos","total":"Total","cart.empty":"Carrinho vazio","pickup":"Retirada","type.here":"Digite aqui","payment":"Pagamento","payment.method":"Método de pagamento","payment.status":"Status do pagamento","promo.code":"Código promocional","add":"Adicionar","enter":"Entrar","subtotal":"Subtotal","service.fee":"Taxa de serviço","continue.payment":"Continuar pagamento","more":"Mais","working.time":"Horário de funcionamento","start.group.order":"Iniciar pedido em grupo","clear.bag":"Limpar sacola","save":"Salvar","add.promocode":"Adicionar código promocional","clear":"Limpar","sign.up":"Cadastrar-se","login":"Entrar","app.text":"Há mais para amar no aplicativo.","dont.have.account":"Não tem uma conta?","keep.logged":"Manter-me conectado","forgot.password":"Esqueceu a senha","access.quickly":"ou acesse rapidamente","have.account":"Já tem uma conta?","reset.password":"Redefinir senha","reset.password.text":"Por favor, forneça o endereço de e-mail e enviaremos um código para você alterar sua senha.","send":"Enviar","enter.otp.code":"Digite o código OTP","enter.code.text":"Enviamos o código OTP para {{phone}}","send.new":"Enviar novo","confirm":"Confirmar","restaurant":"Restaurante","found.number.results":"Encontrados {{count}} resultados","enter.delivery.address":"Digite o endereço de entrega","search":"Buscar","submit":"Enviar","view.profile":"Ver perfil","settings":"Configurações","help":"Ajuda","log.out":"Sair","profile":"Perfil","date.of.birth":"Data de nascimento","update.password":"Atualizar senha","old.password":"Senha atual","password.confirmation":"Confirmação da senha","cancel":"Cancelar","gender":"Gênero","choose.here":"Escolha aqui","male":"Masculino","female":"Feminino","notification":"Notificação","push.notifications":"Notificações push","on":"Ligado","off":"Desligado","send.news.email":"Enviar e-mail de notícias","discount.notifications":"Notificações de desconto","order.verify":"Verificar pedido","back":"Voltar","active.orders":"Pedidos ativos","order.history":"Histórico de pedidos","new":"Novo","accepted":"Aceito","ready":"Pronto","on_a_way":"A caminho","delivered":"Entregue","cancelled":"Cancelado","driver":"Entregador","support":"Suporte","repeat.order":"Repetir pedido","liked.restaurants":"Restaurantes curtidos","have.questions":"Ainda tem dúvidas?","questions.text":"Não consegue encontrar a resposta que procura? Por favor, converse com nossa equipe amigável.","call.support":"Ligar para o suporte","group.order.text":"Você gerencia totalmente o pedido e confirma o endereço. Os membros da equipe podem adicionar um produto de um local de sua escolha.","start":"Iniciar","copied":"Copiado para a área de transferência!","group.members":"Membros do grupo","choosing":"Escolhendo","clear.cart":"Tem certeza de que deseja limpar o carrinho?","rating":"Avaliação","special.offers":"Ofertas especiais","free.delivery":"Entrega grátis","show":"Mostrar","all":"Todos","languages":"Idiomas","currency":"Moeda","no":"Não","yes":"Sim","order.for.address":"Pedir para este endereço?","replace.cart.prompt":"Você só pode adicionar itens de um restaurante ao seu carrinho de compras.","saved":"Salvo","required":"Obrigatório","passwords.dont.match":"As senhas não coincidem","password.should.contain":"A senha deve conter pelo menos 6 caracteres","shop.tax":"Taxa da loja","order.tax":"Taxa do pedido","vat.tax":"Taxa de IVA","today":"Hoje","tomorrow":"Amanhã","min":"min","edit":"Editar","order.details":"Detalhes do pedido","cancel.order":"Cancelar pedido","under":"Abaixo","bonus":"Bônus","are.you.sure.cancel.order":"Tem certeza de que deseja cancelar este pedido?","order.cancelled":"Pedido cancelado","wallet":"Carteira","choose.payment.method":"Por favor, escolha o método de pagamento","refund":"Reembolso","leave.feedback":"Deixar feedback","thanks.for.feedback":"Obrigado pelo seu feedback!","order.refund":"Reembolso do pedido","why.refund":"Por que você quer um reembolso?","request.sent":"Solicitação enviada com sucesso!","request.not.sent":"Sua solicitação não foi enviada!","pending":"Pendente","approved":"Aprovado","rejected":"Rejeitado","refunds":"Reembolsos","products":"Produtos","your.comment":"Seu comentário","answer":"Resposta","order.id":"ID do Pedido","go.to.order":"Ir para o pedido","price":"Preço","closed":"Fechado","done":"Concluído","manage.group.order":"Gerenciar pedido em grupo","manage.order":"Gerenciar pedido","join.group.order":"Participar do pedido em grupo","join.group.text":"Você só pode selecionar produtos do restaurante escolhido pelo criador do grupo","join":"Participar","leave.group":"Sair do grupo","are.you.sure.leave.group":"Tem certeza de que deseja sair do pedido em grupo?","edit.order":"Editar pedido","you.kicked.from.group":"Você foi removido do pedido em grupo","group.order.permission":"Alguns membros do grupo não terminaram de fazer o pedido. Tem certeza de que deseja continuar?","see.all":"Ver todos","all.shops":"Todas as lojas","shops":"Lojas","catalog":"Catálogo","ingredients":"Ingredientes","transaction.id":"ID da Transação","wallet.history":"Histórico da carteira","sender":"Remetente","date":"Data","note":"Nota","topup.wallet":"Recarregar carteira","your.order":"Seu pedido","your.order.status.updated.text":"O status do seu pedido foi atualizado! Clique em \'Mostrar\' para ver os detalhes do pedido.","help.center":"Central de ajuda","message":"Mensagem","login.first":"Por favor, faça login primeiro","add.to.bag":"Adicionar à sacola","be.seller":"Tornar-se vendedor","general":"Geral","logo.image":"Imagem do logo","background.image":"Imagem de fundo","delivery.info":"Informações de entrega","minute":"Minuto","day":"Dia","month":"Mês","address":"Endereço","seller.request.under.review":"Sua solicitação para se tornar vendedor está sendo analisada.","seller.request.accepted":"Sua solicitação para se tornar vendedor foi aceita.","start.price":"Preço inicial","shop.closed":"Loja fechada","no.zone.title":"Ainda não entregamos aqui :(","no.zone.text":"Mas adicionamos dezenas de novos locais toda semana. Talvez estaremos aqui em breve! Se você inserir seu e-mail, avisaremos assim que estivermos disponíveis. Prometemos não enviar spam!","payment.type":"Tipo de pagamento","verify":"Verificar","verify.email":"Verificação de e-mail","verify.text":"Por favor, digite o código de verificação que enviamos para","verify.didntRecieveCode":"Não recebeu o código?","resend":"Enviar novamente","should.match":"As senhas devem coincidir","verify.send":"Código de verificação enviado com sucesso","email.inuse":"O e-mail já está sendo usado.","verify.error":"Código de verificação incorreto","about":"Sobre","become.affiliate":"Torne-se um Afiliado","careers":"Carreiras","blog":"Blog","get.helps":"Obter ajuda","add.your.restaurant":"Adicione seu restaurante","sign.up.to.deliver":"Cadastre-se para entregar","privacy.policy":"Política de Privacidade","terms":"Termos","tags":"Tags","near_you":"Perto de você","open_now":"Aberto agora","copy.code":"Copiar código","balance":"Saldo","referrals":"Indicações","referral.title":"{{price_from}} para você, {{price_to}} para um amigo","referral.text":"Amigos podem obter até {{price_to}} de desconto — você receberá {{price_from}} quando eles fizerem seu primeiro pedido.","role":"Função","category":"Categoria","no.items":"Nenhum item","referral.terms":"Termos de indicação","login.or.create.account":"Entrar ou criar conta","sign.in.be.seller":"Entre para ser vendedor","error.400":"Ocorreu um erro. Por favor, tente novamente mais tarde","deals":"Ofertas","more.info":"Mais informações","ratings":"Avaliações","open.until":"Aberto até","no.orders.found":"Você ainda não tem pedidos","go.to.menu":"Ir para o menu","no.refunds.found":"Você ainda não tem reembolsos de pedidos. Você pode criar uma solicitação de reembolso a partir de pedidos entregues.","no.active.orders.found":"Nenhum pedido ativo","no.wallet.found":"Você ainda não tem transações na carteira","recent.searches":"Buscas recentes","no.liked.restaurants":"Você ainda não tem restaurantes curtidos","try.again":"Tente novamente","unauthorized":"Não autorizado","you.cannot.join":"Você não pode participar. Pedido em grupo inválido","delivery.zone.not.available":"Desculpe, não estamos disponíveis aqui","leave.group.prompt":"Você participou de um pedido em grupo. Para adicionar produto, saia do grupo primeiro!","hours.ago":"horas atrás","become.delivery":"Torne-se um entregador","become.delivery.text":"Em vez de empregos tradicionais de entrega de comida onde os horários não são flexíveis, tente ser seu próprio chefe com o Foodyman. Seja pago para entregar em seu horário usando o aplicativo de entrega de comida mais baixado pelos clientes.","discount":"Desconto","only.opened":"Apenas abertos","schedule":"Agenda","shop.closed.choose.other.day":"A loja está fechada neste dia. Por favor, selecione outro dia.","edit.schedule":"Editar agenda","pickup.address":"Endereço de retirada","pickup.time":"Horário de retirada","branch":"Filial","branches":"Filiais","branches.not.found":"Filiais não encontradas","out.of.stock":"Fora de estoque","hour":"Hora","h":"hora","no.restaurants":"Restaurantes não encontrados de acordo com sua solicitação","no.shops":"Lojas não encontradas de acordo com sua solicitação","sms.not.sent":"SMS não enviado!","email.or.phone":"E-mail ou telefone","login.invalid":"Login ou senha inválidos","verify.phone":"Verificação de telefone","recipes":"Receitas","recipes.title":"Receitas","recipes.description":"Escolha sua receita de comida favorita e compre como desejar","no.recipes":"Receitas não encontradas de acordo com sua solicitação","total.time":"Tempo total","calories":"Calorias","servings":"Porções","instructions":"Instruções","nutritions":"Nutrição","add.items.to.cart":"Adicionar {{number}} itens ao carrinho","recipe.discount.condition":"Se você comprar todos os ingredientes, pode obter desconto de","go.to.recipe.order":"Ingredientes adicionados ao carrinho com sucesso.","recipe.discount.definition":"Você obteve desconto da receita","insufficient.wallet.balance":"Saldo insuficiente na carteira","go.to.admin.panel":"Ir para o painel administrativo","have.not.password":"Você ainda não definiu uma senha. Por favor, certifique-se de ter uma senha no sistema antes de criar uma solicitação para se tornar vendedor","email":"E-mail","edit.phone":"Editar telefone","verified":"Verificado","something.went.wrong":"Algo deu errado","phone.required":"Número de telefone é obrigatório","no.careers.found":"Carreiras não encontradas de acordo com sua solicitação","welcome.title":"Receba suas comidas favoritas entregues","welcome.description":"Escolha seu endereço e comece a pedir","do.you.have.restaurant":"Você tem um restaurante?","deliver.title":"Procurando empregos de entregador?","welcome.features.title":"Outras opções para você","start.ordering":"Começar a pedir","why.choose.us":"Por que nos escolher","why.choose.us.first.title":"Escolha o que você quer","why.choose.us.first.text":"Selecione itens de suas lojas favoritas no Foodyman","why.choose.us.second.title":"Veja atualizações em tempo real","why.choose.us.second.text":"Compradores pessoais escolhem itens com cuidado","why.choose.us.third.title":"Receba seus itens no mesmo dia","why.choose.us.third.text":"Aproveite a garantia de qualidade 100% do Foodyman em cada pedido","choose.recomended.address":"Escolha endereço recomendado","place.for.ad":"Local para seu anúncio aqui","ok":"Ok","people.trust.us":"As pessoas confiam em nós","delivery.was.successfull":"A entrega foi bem-sucedida","view.our.insta":"Veja nosso Instagram","latest.blog":"Blog mais recente","ads":"Anúncios","faq":"Perguntas frequentes","view.more":"Ver mais","transactions":"Transações","mark.read":"Marcar todas como lidas","notifications":"Notificações","no.notifications":"Notificações não encontradas de acordo com sua solicitação","news":"Notícias","order.for.someone":"Quero pedir para alguém","user.details.empty":"Por favor, preencha os detalhes do usuário","phone.invalid":"Número de telefone inválido","door.to.door.delivery":"Entrega porta a porta","sender.details":"Detalhes do remetente","parcel.details":"Detalhes da encomenda","receiver.details":"Detalhes do destinatário","home":"Casa","work":"Trabalho","other":"Outro","address.type":"Tipo de endereço","stage":"Estágio","room":"Quarto","active.parcels":"Encomendas ativas","parcel.history":"Histórico de encomendas","receiver":"Destinatário","parcel":"Encomenda","parcel.cancelled":"Encomenda cancelada","phone.number":"Número de telefone","type":"Tipo","parcels":"Encomendas","sign.in.parcel.order":"Entre para usar entrega porta a porta","up.to.weight":"até {{ number }} kg","up.to.length":"até {{ number }} m","length":"Comprimento","width":"Largura","height":"Altura","weight":"Peso","hero.title":"Explore Nossas Lojas com entrega rápida","offers":"Ofertas","view.all":"Ver todos","number.of.offers":"{{number}} ofertas","door.to.door.delivery.service":"Seu serviço pessoal de entrega porta a porta","favorite.brands":"Marcas favoritas","popular.near.you":"Popular perto de você","daily.offers":"Ofertas diárias","follow.us":"Siga-nos nas Redes Sociais","home.page":"Página inicial","all.stories":"Todas as histórias","categories":"Categorias","trending":"Em alta","delivery.free":"Entrega grátis","delivery.with.in":"Entrega em","shop.banner.title":"Algo quente. Algo saboroso.","shop.banner.desc":"Avaliações altas e serviço consistentemente excelente","order.now":"Pedir agora","error.something.went.wrong":"Ops, algo deu errado!","supported.image.formats.only":"Apenas formatos de imagem são suportados!","invalid.image.source":"Fonte de imagem inválida","user.successfully.login":"Usuário logado com sucesso","verify.code.sent":"Código de verificação enviado","empty":"Vazio","welcome":"Bem-vindo","image":"Imagem","new.items.with.discount":"Novidades com desconto","select_delivery_payment":"Selecione o método de pagamento na entrega","no_delivery_payment_methods":"Nenhum método de pagamento na entrega disponível","need_change":"Precisa de troco?","change_for_amount":"Troco para R$ {{amount}}","Cash_delivery":"Dinheiro na Entrega","Card_delivery":"Cartão na Entrega","Pix_delivery":"PIX na Entrega","Debit_delivery":"Débito na Entrega","payment_instructions":"Instruções de pagamento","change_amount":"Valor do troco","exact_amount":"Valor exato","payment_on_delivery":"Pagamento na entrega","delivery_payment_methods":"Métodos de pagamento na entrega","pay_now":"Pagar Agora","pay_on_delivery":"Pagar na Entrega","choose_payment_category":"Como você gostaria de pagar?","online_payment_methods":"Métodos de pagamento online","delivery_payment_methods_desc":"Pague diretamente ao entregador","online_payment_methods_desc":"Pague agora online de forma segura","need_change_question":"Precisa de troco?","change_amount_label":"Valor para troco","change_amount_placeholder":"Ex: R$ 100,00","mercado-pago":"Mercado Pago","stripe":"Cartão de Crédito"}');

/***/ })

};
;