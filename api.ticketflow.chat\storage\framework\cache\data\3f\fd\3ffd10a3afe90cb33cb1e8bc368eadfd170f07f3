9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:23:"App\Models\DeliveryZone":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"shop_delivery_zone";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:1;s:7:"shop_id";i:501;s:7:"address";s:167:"[[-18.43314412242974, -50.34828174353944], [-18.38037133817466, -50.4610203528534], [-18.48394705262582, -50.53998458625184], [-18.527573449445125, -50.4445408606659]]";s:10:"created_at";s:19:"2025-07-23 22:58:51";s:10:"updated_at";s:19:"2025-07-23 22:58:51";s:10:"deleted_at";N;}s:11:" * original";a:6:{s:2:"id";i:1;s:7:"shop_id";i:501;s:7:"address";s:167:"[[-18.43314412242974, -50.34828174353944], [-18.38037133817466, -50.4610203528534], [-18.48394705262582, -50.53998458625184], [-18.527573449445125, -50.4445408606659]]";s:10:"created_at";s:19:"2025-07-23 22:58:51";s:10:"updated_at";s:19:"2025-07-23 22:58:51";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:7:"address";s:5:"array";s:10:"created_at";s:20:"datetime:Y-m-d H:i:s";s:10:"updated_at";s:20:"datetime:Y-m-d H:i:s";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}