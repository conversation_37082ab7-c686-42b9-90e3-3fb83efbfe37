(() => {
var exports = {};
exports.id = 2888;
exports.ids = [2888,7935,7830];
exports.modules = {

/***/ 43600:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "addressPopover_wrapper__PBHhn",
	"label": "addressPopover_label__RtXrA",
	"text": "addressPopover_text__VY9An",
	"actions": "addressPopover_actions__pWGrq"
};


/***/ }),

/***/ 48923:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "notificationCenterItem_wrapper__sREQB",
	"imgWrapper": "notificationCenterItem_imgWrapper__RJ_63",
	"block": "notificationCenterItem_block__ybZRD",
	"naming": "notificationCenterItem_naming__QgCtn",
	"title": "notificationCenterItem_title__q26wP",
	"dot": "notificationCenterItem_dot__BtAZT",
	"text": "notificationCenterItem_text__SMnuH",
	"muted": "notificationCenterItem_muted__LbjA3"
};


/***/ }),

/***/ 15894:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "notificationCenter_wrapper__cgQgF",
	"header": "notificationCenter_header__CqoDH",
	"title": "notificationCenter_title___5SGD",
	"tabs": "notificationCenter_tabs__SP_qq",
	"tab": "notificationCenter_tab__DBZEp",
	"active": "notificationCenter_active__uJ66v",
	"text": "notificationCenter_text__B3P6D",
	"badge": "notificationCenter_badge__08TnR",
	"body": "notificationCenter_body__K0SjH",
	"list": "notificationCenter_list__z3_B7",
	"shimmerContainer": "notificationCenter_shimmerContainer__pU9mh",
	"shimmer": "notificationCenter_shimmer__bikzm",
	"actions": "notificationCenter_actions__h57sE",
	"button": "notificationCenter_button__h8LOG",
	"footer": "notificationCenter_footer__KvNQ5",
	"textBtn": "notificationCenter_textBtn__rPzcB"
};


/***/ }),

/***/ 9098:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "notificationStats_wrapper__t6Wl5",
	"icon": "notificationStats_icon__sVy39",
	"badge": "notificationStats_badge__72wCW"
};


/***/ }),

/***/ 93881:
/***/ ((module) => {

// Exports
module.exports = {
	"row": "searchResultItem_row__3W2Do",
	"flex": "searchResultItem_flex__GU1FF",
	"naming": "searchResultItem_naming__kcii_",
	"shopTitle": "searchResultItem_shopTitle___tQCy",
	"desc": "searchResultItem_desc__2Fw5M",
	"price": "searchResultItem_price__ICfd2",
	"shopNaming": "searchResultItem_shopNaming__03EtZ",
	"titleRateContainer": "searchResultItem_titleRateContainer__jw8MZ",
	"rating": "searchResultItem_rating___0F1z",
	"text": "searchResultItem_text__O6SXX",
	"workTime": "searchResultItem_workTime__bl1k1",
	"imgWrapper": "searchResultItem_imgWrapper__KLKVB"
};


/***/ }),

/***/ 25298:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "searchResult_wrapper__e31Dg",
	"block": "searchResult_block__lSbJ1",
	"line": "searchResult_line__evu3v",
	"header": "searchResult_header__xe_se",
	"title": "searchResult_title__WCRWn",
	"text": "searchResult_text__D5Gl9",
	"body": "searchResult_body__ozhC3",
	"container": "searchResult_container__yDY5s",
	"shimmer": "searchResult_shimmer__rIrJN"
};


/***/ }),

/***/ 97499:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "searchSuggestion_wrapper__DhzAx",
	"header": "searchSuggestion_header__2yJUq",
	"title": "searchSuggestion_title__cQBBx",
	"clearBtn": "searchSuggestion_clearBtn__ivlWk",
	"body": "searchSuggestion_body__wRRwX",
	"flex": "searchSuggestion_flex__Dkbu3",
	"textBtn": "searchSuggestion_textBtn__bNQ_P",
	"text": "searchSuggestion_text__EOAxX",
	"closeBtn": "searchSuggestion_closeBtn__znyx7"
};


/***/ }),

/***/ 82302:
/***/ ((module) => {

// Exports
module.exports = {
	"address": "addressContainer_address__0f1YB",
	"icon": "addressContainer_icon__993dr",
	"addressTitle": "addressContainer_addressTitle__nIMg6",
	"addressWrapper": "addressContainer_addressWrapper__vy0he",
	"list": "addressContainer_list__O2lVD",
	"radioGroup": "addressContainer_radioGroup__6CuEt",
	"radio": "addressContainer_radio__fhZf_",
	"addressTypeIcon": "addressContainer_addressTypeIcon__hURKH",
	"text": "addressContainer_text__hrb7I",
	"twoLine": "addressContainer_twoLine__ANzOV",
	"desc": "addressContainer_desc__nvBaM",
	"label": "addressContainer_label__F53rB",
	"edit": "addressContainer_edit__9DwcA",
	"add": "addressContainer_add__ZJxjJ"
};


/***/ }),

/***/ 86261:
/***/ ((module) => {

// Exports
module.exports = {
	"wrapper": "errorBoundary_wrapper__kMewT",
	"header": "errorBoundary_header__SoKQ1",
	"body": "errorBoundary_body__rzFfH"
};


/***/ }),

/***/ 50761:
/***/ ((module) => {

// Exports
module.exports = {
	"container": "header_container__EWEDv",
	"header": "header_header__SAJHp",
	"navItem": "header_navItem__7Y6f4",
	"brandLogo": "header_brandLogo__A0_aV",
	"menuBtn": "header_menuBtn__Vexr0",
	"search": "header_search__EipNy",
	"searchBar": "header_searchBar__kGcPB",
	"actions": "header_actions__rbgla",
	"iconBtn": "header_iconBtn__Is3SD"
};


/***/ }),

/***/ 63173:
/***/ ((module) => {

// Exports
module.exports = {
	"header": "mobileHeader_header__D9_iq",
	"navItem": "mobileHeader_navItem__4L5Zg",
	"actions": "mobileHeader_actions__b9967",
	"iconBtn": "mobileHeader_iconBtn__Dsbz3",
	"brandLogo": "mobileHeader_brandLogo__i44vX",
	"menuBtn": "mobileHeader_menuBtn__zwTYf",
	"stickyHeader": "mobileHeader_stickyHeader__LGYNE"
};


/***/ }),

/***/ 87337:
/***/ ((module) => {

// Exports
module.exports = {
	"container": "profileHeader_container__9QalR",
	"header": "profileHeader_header__12Hr9",
	"backBtn": "profileHeader_backBtn__pudXj",
	"text": "profileHeader_text__VFWKq"
};


/***/ }),

/***/ 95645:
/***/ ((module) => {

// Exports
module.exports = {
	"search": "searchContainer_search__Z8MFl"
};


/***/ }),

/***/ 51384:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ AddressPopover)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var components_button_primaryButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(77262);
/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(80892);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57987);
/* harmony import */ var _addressPopover_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(43600);
/* harmony import */ var _addressPopover_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_addressPopover_module_scss__WEBPACK_IMPORTED_MODULE_5__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_4__]);
react_i18next__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];






function AddressPopover({ handleOpenAddressModal , handleCloseAddressPopover , defaultAddress  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();
    const rejectAddress = ()=>{
        handleCloseAddressPopover();
        handleOpenAddressModal();
    };
    const acceptAddress = ()=>{
        handleCloseAddressPopover();
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_addressPopover_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("label", {
                className: (_addressPopover_module_scss__WEBPACK_IMPORTED_MODULE_5___default().label),
                children: t("order.for.address")
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                className: (_addressPopover_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text),
                children: defaultAddress
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_addressPopover_module_scss__WEBPACK_IMPORTED_MODULE_5___default().actions),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                        size: "small",
                        onClick: rejectAddress,
                        children: t("no")
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_button_primaryButton__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                        size: "small",
                        onClick: acceptAddress,
                        children: t("yes")
                    })
                ]
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 37935:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Loader)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);



function Loader({ size  }) {
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        style: {
            textAlign: "center",
            padding: "10px 0"
        },
        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.CircularProgress, {
            size: size
        })
    });
}


/***/ }),

/***/ 44465:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ NotificationCenterItem)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(48923);
/* harmony import */ var _notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var interfaces_user_interface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15212);
/* harmony import */ var components_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(11295);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1635);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(18074);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_avatar__WEBPACK_IMPORTED_MODULE_3__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_5__]);
([components_avatar__WEBPACK_IMPORTED_MODULE_3__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);







function NotificationCenterItem({ data , handleClick  }) {
    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)();
    const renderTitle = ()=>{
        switch(data.type){
            case interfaces_user_interface__WEBPACK_IMPORTED_MODULE_2__/* .NotificationStatus.STATUS_CHANGED */ .E.STATUS_CHANGED:
                return t("order") + " #" + data.title;
            case interfaces_user_interface__WEBPACK_IMPORTED_MODULE_2__/* .NotificationStatus.BOOKING_STATUS */ .E.BOOKING_STATUS:
                return t("reservation") + " #" + data.title;
            default:
                return data.body;
        }
    };
    const renderBody = ()=>{
        switch(data.type){
            case interfaces_user_interface__WEBPACK_IMPORTED_MODULE_2__/* .NotificationStatus.NEWS_PUBLISH */ .E.NEWS_PUBLISH:
                return "";
            default:
                return data.body;
        }
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),
        onClick: ()=>handleClick(data),
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: (_notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6___default().imgWrapper),
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_avatar__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                    data: data.client
                })
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6___default().block),
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6___default().naming),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                                className: (_notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),
                                children: renderTitle()
                            }),
                            !data.read_at && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                className: (_notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6___default().dot)
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                        className: (_notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6___default().text),
                        children: renderBody()
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                        className: (_notificationCenterItem_module_scss__WEBPACK_IMPORTED_MODULE_6___default().muted),
                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(data.created_at).format("DD.MM.YYYY, HH:mm")
                    })
                ]
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 64571:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ NotificationCenter)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(15894);
/* harmony import */ var _notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(18074);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var services_notification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(76058);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(29969);
/* harmony import */ var remixicon_react_CheckDoubleLineIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(991);
/* harmony import */ var remixicon_react_CheckDoubleLineIcon__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CheckDoubleLineIcon__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var components_notificationCenterItem_notificationCenterItem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(44465);
/* harmony import */ var components_loader_loader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(37935);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var interfaces_user_interface__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(15212);
/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(74621);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_12__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__, services_notification__WEBPACK_IMPORTED_MODULE_4__, components_notificationCenterItem_notificationCenterItem__WEBPACK_IMPORTED_MODULE_7__, components_alert_toast__WEBPACK_IMPORTED_MODULE_11__]);
([hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__, services_notification__WEBPACK_IMPORTED_MODULE_4__, components_notificationCenterItem_notificationCenterItem__WEBPACK_IMPORTED_MODULE_7__, components_alert_toast__WEBPACK_IMPORTED_MODULE_11__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);














const tabs = [
    {
        label: "all",
        value: undefined,
        type: "notification"
    },
    {
        label: "news",
        value: interfaces_user_interface__WEBPACK_IMPORTED_MODULE_10__/* .NotificationStatus.NEWS_PUBLISH */ .E.NEWS_PUBLISH,
        type: "news_publish"
    },
    {
        label: "orders",
        value: interfaces_user_interface__WEBPACK_IMPORTED_MODULE_10__/* .NotificationStatus.STATUS_CHANGED */ .E.STATUS_CHANGED,
        type: "status_changed"
    }
];
function NotificationCenter({ onClose  }) {
    const { t , locale  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)();
    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter)();
    const { isAuthenticated  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_5__/* .useAuth */ .a)();
    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();
    const { data , refetch , isLoading: isLoadingStatistics  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "notificationStatistic",
        isAuthenticated
    ], ()=>services_notification__WEBPACK_IMPORTED_MODULE_4__/* ["default"].getStatistics */ .Z.getStatistics(), {
        enabled: isAuthenticated,
        retry: false,
        refetchInterval: 10000,
        refetchOnWindowFocus: isAuthenticated,
        staleTime: 0
    });
    const { data: list , fetchNextPage , hasNextPage , isFetchingNextPage , isLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useInfiniteQuery)([
        "pushNotifications",
        locale,
        isAuthenticated,
        type,
        data?.notification
    ], ({ pageParam =1  })=>services_notification__WEBPACK_IMPORTED_MODULE_4__/* ["default"].getAll */ .Z.getAll({
            page: pageParam,
            perPage: 5,
            type,
            column: "id",
            sort: "desc"
        }), {
        staleTime: 0,
        enabled: isAuthenticated,
        getNextPageParam: (lastPage)=>{
            if (lastPage.meta.current_page < lastPage.meta.last_page) {
                return lastPage.meta.current_page + 1;
            }
            return undefined;
        }
    });
    const notifications = list?.pages?.flatMap((item)=>item.data);
    const { mutate: readAll , isLoading: isReadAllLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({
        mutationFn: (data)=>services_notification__WEBPACK_IMPORTED_MODULE_4__/* ["default"].readAll */ .Z.readAll(data),
        onSuccess: ()=>refetch(),
        onError: (err)=>{
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_11__/* .error */ .vU)(err?.data?.message);
        }
    });
    const { mutate: readMessage  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({
        mutationFn: (data)=>services_notification__WEBPACK_IMPORTED_MODULE_4__/* ["default"].readById */ .Z.readById(data),
        onError: (err)=>{
            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_11__/* .error */ .vU)(err?.data?.message);
        }
    });
    const handleReadMessage = (item)=>{
        switch(item.type){
            case interfaces_user_interface__WEBPACK_IMPORTED_MODULE_10__/* .NotificationStatus.STATUS_CHANGED */ .E.STATUS_CHANGED:
                {
                    if ("parcel" in item) {
                        push(`/parcels/${item.parcel?.id}`);
                    } else {
                        push(`/orders/${item.order?.id}`);
                    }
                    break;
                }
            case interfaces_user_interface__WEBPACK_IMPORTED_MODULE_10__/* .NotificationStatus.BOOKING_STATUS */ .E.BOOKING_STATUS:
                push(`/reservations`);
                break;
            case interfaces_user_interface__WEBPACK_IMPORTED_MODULE_10__/* .NotificationStatus.NEWS_PUBLISH */ .E.NEWS_PUBLISH:
                push(`/?news=${item.blog?.uuid}`);
                break;
            case interfaces_user_interface__WEBPACK_IMPORTED_MODULE_10__/* .NotificationStatus.DELIVERY_REFUNDED */ .E.DELIVERY_REFUNDED:
                push(`/wallet`);
                break;
            default:
                break;
        }
        if (!item.read_at) {
            readMessage(item.id);
        }
        onClose();
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().wrapper),
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().header),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h1", {
                        className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().title),
                        children: t("notifications")
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().tabs),
                        children: tabs.map((item)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                                className: `${(_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().tab)} ${type === item.value ? (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().active) : ""}`,
                                onClick: ()=>setType(item.value),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().text),
                                        children: t(item.label)
                                    }),
                                    !!data && data[item.type] ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().badge),
                                        children: data[item.type]
                                    }) : ""
                                ]
                            }, item.label))
                    })
                ]
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().body),
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().list),
                        children: [
                            notifications?.map((item)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_notificationCenterItem_notificationCenterItem__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                                    data: item,
                                    handleClick: handleReadMessage
                                }, item.id)),
                            !notifications?.length && !isLoading && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().text),
                                children: t("no.notifications")
                            }),
                            isLoading && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().shimmerContainer),
                                children: Array.from(new Array(3)).map((item, idx)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {
                                        variant: "rectangular",
                                        className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().shimmer)
                                    }, "notify" + idx))
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().actions),
                        children: hasNextPage && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                            className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().button),
                            onClick: ()=>fetchNextPage(),
                            children: !isFetchingNextPage ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().text),
                                children: t("view.more")
                            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_loader_loader__WEBPACK_IMPORTED_MODULE_8__["default"], {
                                size: 24
                            })
                        })
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().footer),
                children: !!data?.notification && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                    className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().textBtn),
                    onClick: ()=>readAll({}),
                    disabled: isReadAllLoading || isLoadingStatistics,
                    children: [
                        !isReadAllLoading && !isLoadingStatistics ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_CheckDoubleLineIcon__WEBPACK_IMPORTED_MODULE_6___default()), {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_9__.CircularProgress, {
                            size: 24
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                            className: (_notificationCenter_module_scss__WEBPACK_IMPORTED_MODULE_13___default().text),
                            children: t("mark.read")
                        })
                    ]
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 61105:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ NotificationStats)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _notificationStats_module_scss__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(9098);
/* harmony import */ var _notificationStats_module_scss__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_notificationStats_module_scss__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(37490);
/* harmony import */ var remixicon_react_Notification2LineIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(92853);
/* harmony import */ var remixicon_react_Notification2LineIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_Notification2LineIcon__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var services_notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(76058);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(29969);
/* harmony import */ var containers_modal_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(47567);
/* harmony import */ var components_notificationCenter_notificationCenter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(64571);
/* harmony import */ var containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(30182);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_10__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([services_notification__WEBPACK_IMPORTED_MODULE_5__, components_notificationCenter_notificationCenter__WEBPACK_IMPORTED_MODULE_8__]);
([services_notification__WEBPACK_IMPORTED_MODULE_5__, components_notificationCenter_notificationCenter__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);












function NotificationStats({}) {
    const isMobile = (0,_mui_material__WEBPACK_IMPORTED_MODULE_10__.useMediaQuery)("(max-width:576px)");
    const { isAuthenticated  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_6__/* .useAuth */ .a)();
    const [modal, handleOpen, handleClose] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)();
    const { data  } = (0,react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)([
        "notificationStatistic",
        isAuthenticated
    ], ()=>services_notification__WEBPACK_IMPORTED_MODULE_5__/* ["default"].getStatistics */ .Z.getStatistics(), {
        enabled: isAuthenticated,
        retry: false,
        refetchInterval: 10000,
        refetchOnWindowFocus: isAuthenticated,
        staleTime: 0
    });
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                className: (_notificationStats_module_scss__WEBPACK_IMPORTED_MODULE_11___default().wrapper),
                onClick: handleOpen,
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_notificationStats_module_scss__WEBPACK_IMPORTED_MODULE_11___default().icon),
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_Notification2LineIcon__WEBPACK_IMPORTED_MODULE_3___default()), {})
                    }),
                    !!data?.notification && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_notificationStats_module_scss__WEBPACK_IMPORTED_MODULE_11___default().badge),
                        children: data?.notification
                    })
                ]
            }),
            !isMobile ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_modal_modal__WEBPACK_IMPORTED_MODULE_7__["default"], {
                open: modal,
                onClose: handleClose,
                position: "right",
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_notificationCenter_notificationCenter__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
                    onClose: handleClose
                })
            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_9__["default"], {
                open: modal,
                onClose: handleClose,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_notificationCenter_notificationCenter__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
                    onClose: handleClose
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 22098:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ProductResultItem)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(93881);
/* harmony import */ var _searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(41664);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var components_price_price__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(90026);
/* harmony import */ var utils_getImage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(95785);
/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(37562);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_4__]);
components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];







function ProductResultItem({ data , onClickItem  }) {
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5___default().row),
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
            href: `/shop/${data.shop?.id}?product=${data.uuid}`,
            shallow: true,
            className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5___default().flex),
            onClick: onClickItem,
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5___default().imgWrapper),
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
                        fill: true,
                        src: (0,utils_getImage__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z)(data.img),
                        alt: data.translation?.title,
                        sizes: "320px",
                        quality: 90
                    })
                }),
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5___default().naming),
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                            className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5___default().shopTitle),
                            children: data.translation?.title
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                            className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5___default().desc),
                            children: data.translation?.description
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_5___default().price),
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_price_price__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                                number: data.stocks?.length ? data.stocks[0]?.price : 0
                            })
                        })
                    ]
                })
            ]
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 14486:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ShopResultItem)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(93881);
/* harmony import */ var _searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(41664);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var components_shopLogoBackground_shopLogoBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(45122);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_shopLogoBackground_shopLogoBackground__WEBPACK_IMPORTED_MODULE_3__]);
components_shopLogoBackground_shopLogoBackground__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];





function ShopResultItem({ data , onClickItem  }) {
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_4___default().row),
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
            href: `/shop/${data.id}`,
            className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_4___default().flex),
            onClick: onClickItem,
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_shopLogoBackground_shopLogoBackground__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                    data: data
                }),
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_4___default().naming),
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                            className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_4___default().shopTitle),
                            children: data.translation?.title
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                            className: (_searchResultItem_module_scss__WEBPACK_IMPORTED_MODULE_4___default().desc),
                            children: data.translation?.description
                        })
                    ]
                })
            ]
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 99496:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ SearchResult)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(25298);
/* harmony import */ var _searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57987);
/* harmony import */ var components_searchResultItem_shopResultItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(14486);
/* harmony import */ var components_searchResultItem_productResultItem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(22098);
/* harmony import */ var components_loader_loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(37935);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_searchResultItem_shopResultItem__WEBPACK_IMPORTED_MODULE_4__, components_searchResultItem_productResultItem__WEBPACK_IMPORTED_MODULE_5__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_searchResultItem_shopResultItem__WEBPACK_IMPORTED_MODULE_4__, components_searchResultItem_productResultItem__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);








function SearchResult({ shops , products , isLoading , isVisibleShops , handleClickItem , productTotal , shopTotal , isFetchingShopsNextPage , isFetchingProductsNextPage , hasProductsNextPage , hasShopsNextPage , fetchShopsNextPage , fetchProductsNextPage  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();
    const shopLoader = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const productLoader = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const handleObserverShops = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entries)=>{
        const target = entries[0];
        if (target.isIntersecting && hasShopsNextPage) {
            fetchShopsNextPage();
        }
    }, [
        hasShopsNextPage,
        fetchShopsNextPage
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const option = {
            root: null,
            rootMargin: "20px",
            threshold: 0
        };
        const observer = new IntersectionObserver(handleObserverShops, option);
        if (shopLoader.current) observer.observe(shopLoader.current);
    }, [
        handleObserverShops,
        hasShopsNextPage,
        fetchShopsNextPage
    ]);
    const handleObserverProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entries)=>{
        const target = entries[0];
        if (target.isIntersecting && hasProductsNextPage) {
            fetchProductsNextPage();
        }
    }, [
        hasProductsNextPage,
        fetchProductsNextPage
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const option = {
            root: null,
            rootMargin: "20px",
            threshold: 0
        };
        const observer = new IntersectionObserver(handleObserverProducts, option);
        if (productLoader.current) observer.observe(productLoader.current);
    }, [
        handleObserverProducts,
        hasProductsNextPage,
        fetchProductsNextPage
    ]);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        children: !isLoading ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
            className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().wrapper),
            children: [
                isVisibleShops && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: `${(_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().block)} ${(_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().line)}`,
                    children: [
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().header),
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h3", {
                                    className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().title),
                                    children: t("restaurant")
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                                    className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().text),
                                    children: t("found.number.results", {
                                        count: shopTotal
                                    })
                                })
                            ]
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().body),
                            children: shops.map((item)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_searchResultItem_shopResultItem__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
                                    data: item,
                                    onClickItem: handleClickItem
                                }, item.id))
                        })
                    ]
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    ref: shopLoader
                }),
                isFetchingShopsNextPage && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_loader_loader__WEBPACK_IMPORTED_MODULE_6__["default"], {}),
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().block),
                    children: [
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().header),
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h3", {
                                    className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().title),
                                    children: t("products")
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                                    className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().text),
                                    children: t("found.number.results", {
                                        count: productTotal
                                    })
                                })
                            ]
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().body),
                            children: products.map((item)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_searchResultItem_productResultItem__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
                                    data: item,
                                    onClickItem: handleClickItem
                                }, item.id))
                        })
                    ]
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    ref: productLoader
                }),
                isFetchingProductsNextPage && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_loader_loader__WEBPACK_IMPORTED_MODULE_6__["default"], {})
            ]
        }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
            className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().wrapper),
            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().container),
                children: Array.from(new Array(2)).map((item, idx)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Skeleton, {
                        variant: "rectangular",
                        className: (_searchResult_module_scss__WEBPACK_IMPORTED_MODULE_7___default().shimmer)
                    }, "result" + idx))
            })
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 54935:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ SearchSuggestion)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(97499);
/* harmony import */ var _searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57987);
/* harmony import */ var remixicon_react_TimeLineIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2923);
/* harmony import */ var remixicon_react_TimeLineIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_TimeLineIcon__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var remixicon_react_CloseLineIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(22179);
/* harmony import */ var remixicon_react_CloseLineIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseLineIcon__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var hooks_useRedux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(34349);
/* harmony import */ var redux_slices_search__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(20330);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_2__]);
react_i18next__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];








function SearchSuggestion({ setSearchTerm  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();
    const history = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_5__/* .useAppSelector */ .C)(redux_slices_search__WEBPACK_IMPORTED_MODULE_6__/* .selectSearchHistory */ .ag);
    const dispatch = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_5__/* .useAppDispatch */ .T)();
    function handleClick(event, item) {
        event.preventDefault();
        setSearchTerm(item);
    }
    function handleClear() {
        dispatch((0,redux_slices_search__WEBPACK_IMPORTED_MODULE_6__/* .clearSearch */ .AQ)());
    }
    function handleRemove(item) {
        dispatch((0,redux_slices_search__WEBPACK_IMPORTED_MODULE_6__/* .removeFromSearch */ .iU)(item));
    }
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default().wrapper),
        style: {
            display: history.length ? "block" : "none"
        },
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default().header),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h3", {
                        className: (_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default().title),
                        children: t("recent.searches")
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                        className: (_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default().clearBtn),
                        onClick: handleClear,
                        children: t("clear")
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: (_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default().body),
                children: history.map((item)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default().flex),
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("a", {
                                href: "suggestion",
                                className: (_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default().textBtn),
                                onClick: (event)=>handleClick(event, item),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_TimeLineIcon__WEBPACK_IMPORTED_MODULE_3___default()), {}),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                        className: (_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default().text),
                                        children: item
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                                className: (_searchSuggestion_module_scss__WEBPACK_IMPORTED_MODULE_7___default().closeBtn),
                                onClick: ()=>handleRemove(item),
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_CloseLineIcon__WEBPACK_IMPORTED_MODULE_4___default()), {})
                            })
                        ]
                    }, "search" + item))
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 11198:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "v": () => (/* binding */ config)
/* harmony export */ });
const config = {
    defaultOptions: {
        queries: {
            staleTime: 1 * 60 * 60 * 1000,
            cacheTime: 5 * 60 * 60 * 1000,
            refetchOnWindowFocus: false,
            retry: false
        }
    }
};


/***/ }),

/***/ 41380:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ AddressContainer)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(37490);
/* harmony import */ var hooks_usePopover__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(58287);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57987);
/* harmony import */ var remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(99893);
/* harmony import */ var remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(82302);
/* harmony import */ var _addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_17__);
/* harmony import */ var components_addressPopover_addressPopover__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(51384);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(21697);
/* harmony import */ var utils_getAddressFromLocation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(60291);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(30182);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(29969);
/* harmony import */ var _savedAddressList__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(86653);
/* harmony import */ var services_address__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(82027);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_4__, components_addressPopover_addressPopover__WEBPACK_IMPORTED_MODULE_6__, utils_getAddressFromLocation__WEBPACK_IMPORTED_MODULE_9__, _savedAddressList__WEBPACK_IMPORTED_MODULE_15__, services_address__WEBPACK_IMPORTED_MODULE_16__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_4__, components_addressPopover_addressPopover__WEBPACK_IMPORTED_MODULE_6__, utils_getAddressFromLocation__WEBPACK_IMPORTED_MODULE_9__, _savedAddressList__WEBPACK_IMPORTED_MODULE_15__, services_address__WEBPACK_IMPORTED_MODULE_16__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);


















const PopoverContainer = next_dynamic__WEBPACK_IMPORTED_MODULE_7___default()(()=>__webpack_require__.e(/* import() */ 6060).then(__webpack_require__.bind(__webpack_require__, 56060)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\addressContainer\\addressContainer.tsx -> " + "containers/popover/popover"
        ]
    }
});
const AddressModal = next_dynamic__WEBPACK_IMPORTED_MODULE_7___default()(()=>Promise.all(/* import() */[__webpack_require__.e(251), __webpack_require__.e(5567), __webpack_require__.e(6711)]).then(__webpack_require__.bind(__webpack_require__, 26711)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\addressContainer\\addressContainer.tsx -> " + "components/addressModal/addressModal"
        ]
    }
});
function AddressContainer({}) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_10__.useMediaQuery)("(min-width:1140px)");
    const addressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
    const [addressModal, handleOpenAddressModal, handleCloseAddressModal] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)();
    const [savedAddressList, savedListAnchorEl, handleOpenSavedAddressList, handleCloseSavedAddressList] = (0,hooks_usePopover__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)();
    const [addressPopover, anchorEl, handleOpenAddressPopover, handleCloseAddressPopover] = (0,hooks_usePopover__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)();
    const { address , location , updateAddress , updateLocation  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_8__/* .useSettings */ .r)();
    const [userAddress, setUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);
    const [userLocation, setUserLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
    const { query  } = (0,next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter)();
    const { user  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_14__/* .useAuth */ .a)();
    const [editedAddress, setEditedAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const { data: addresses  } = (0,react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)("addresses", ()=>services_address__WEBPACK_IMPORTED_MODULE_16__/* ["default"].getAll */ .Z.getAll({
            perPage: 100
        }), {
        onSuccess: (res)=>{
            if (res.length !== 0) {
                const defaultAddress = res.find((item)=>Boolean(item.active));
                let latlng;
                if (defaultAddress?.location) {
                    latlng = `${defaultAddress?.location.at(0)},${defaultAddress.location.at(1)}`;
                } else {
                    latlng = location;
                }
                setUserAddress(defaultAddress?.address?.address || "");
                updateAddress(defaultAddress?.address?.address);
                updateLocation(latlng);
            }
        },
        enabled: Boolean(user)
    });
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (!address) {
            window.navigator.geolocation.getCurrentPosition(defineLocation, defineLocation);
        }
    }, []);
    async function defineLocation(position) {
        const { coords  } = position;
        let latlng;
        if (coords) {
            latlng = `${coords.latitude},${coords.longitude}`;
        } else {
            latlng = location;
        }
        const addr = await (0,utils_getAddressFromLocation__WEBPACK_IMPORTED_MODULE_9__/* .getAddressFromLocation */ .K)(latlng);
        setUserAddress(addr);
        setUserLocation(latlng);
        updateLocation(latlng);
        try {
            addressRef.current.click();
        } catch (err) {
            console.log("err => ", err);
        }
        if (query.g) {
            updateAddress(addr);
            handleCloseAddressPopover();
        }
    }
    const saveAndCloseAddressPopover = ()=>{
        updateAddress(userAddress);
        handleCloseAddressPopover();
    };
    const handleClickAddressRef = (event)=>{
        event.stopPropagation();
        handleOpenAddressPopover(event);
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_17___default().address),
                onClick: (e)=>Boolean(user) ? handleOpenSavedAddressList(e) : handleOpenAddressModal(),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_17___default().icon),
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_MapPinRangeLineIcon__WEBPACK_IMPORTED_MODULE_5___default()), {})
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        ref: addressRef,
                        onClick: handleClickAddressRef,
                        className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_17___default().addressTitle),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("label", {
                                children: t("delivery.address")
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                                children: address
                            })
                        ]
                    })
                ]
            }),
            isDesktop ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(PopoverContainer, {
                open: addressPopover,
                anchorEl: anchorEl,
                onClose: saveAndCloseAddressPopover,
                anchorOrigin: {
                    vertical: "bottom",
                    horizontal: "center"
                },
                transformOrigin: {
                    vertical: "top",
                    horizontal: "center"
                },
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_addressPopover_addressPopover__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                    handleOpenAddressModal: handleOpenAddressModal,
                    handleCloseAddressPopover: saveAndCloseAddressPopover,
                    defaultAddress: userAddress
                })
            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_11__["default"], {
                open: addressPopover,
                onClose: saveAndCloseAddressPopover,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_addressPopover_addressPopover__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                    handleOpenAddressModal: handleOpenAddressModal,
                    handleCloseAddressPopover: saveAndCloseAddressPopover,
                    defaultAddress: userAddress
                })
            }),
            isDesktop ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(PopoverContainer, {
                sx: {
                    top: 10
                },
                open: savedAddressList,
                anchorEl: savedListAnchorEl,
                onClose: handleCloseSavedAddressList,
                anchorOrigin: {
                    vertical: "bottom",
                    horizontal: "center"
                },
                transformOrigin: {
                    vertical: "top",
                    horizontal: "center"
                },
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_savedAddressList__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
                    handleOpenAddressModal: handleOpenAddressModal,
                    addresses: addresses,
                    handleCloseList: handleCloseSavedAddressList,
                    onSelectAddress: (value)=>{
                        console.log(value);
                        setEditedAddress(value);
                        handleCloseSavedAddressList();
                        handleOpenAddressModal();
                    }
                })
            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_11__["default"], {
                open: savedAddressList,
                onClose: handleCloseSavedAddressList,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_savedAddressList__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
                    handleOpenAddressModal: handleOpenAddressModal,
                    addresses: addresses,
                    handleCloseList: handleCloseSavedAddressList,
                    onSelectAddress: (value)=>{
                        console.log(value);
                        setEditedAddress(value);
                        handleCloseSavedAddressList();
                        handleOpenAddressModal();
                    }
                })
            }),
            addressModal && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(AddressModal, {
                open: addressModal,
                onClose: ()=>{
                    handleCloseAddressModal();
                    setEditedAddress(null);
                },
                latlng: editedAddress?.location.join(",") || userLocation || location,
                address: editedAddress?.address?.address || userAddress || address,
                fullScreen: !isDesktop,
                editedAddress: editedAddress,
                onClearAddress: ()=>setEditedAddress(null)
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 86653:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ SavedAddressList)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(82302);
/* harmony import */ var _addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(21697);
/* harmony import */ var components_inputs_radioInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(80865);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57987);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var services_address__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82027);
/* harmony import */ var remixicon_react_AddLineIcon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(75265);
/* harmony import */ var remixicon_react_AddLineIcon__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_AddLineIcon__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var remixicon_react_EqualizerFillIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(10865);
/* harmony import */ var remixicon_react_EqualizerFillIcon__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_EqualizerFillIcon__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var remixicon_react_UserLocationFillIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(86073);
/* harmony import */ var remixicon_react_UserLocationFillIcon__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_UserLocationFillIcon__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var remixicon_react_Briefcase2FillIcon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(33211);
/* harmony import */ var remixicon_react_Briefcase2FillIcon__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_Briefcase2FillIcon__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var remixicon_react_MapPinFillIcon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(22069);
/* harmony import */ var remixicon_react_MapPinFillIcon__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_MapPinFillIcon__WEBPACK_IMPORTED_MODULE_11__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_4__, services_address__WEBPACK_IMPORTED_MODULE_6__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_4__, services_address__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);













function SavedAddressList({ handleOpenAddressModal , addresses , handleCloseList , onSelectAddress  }) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();
    const getAddressTypeIcon = (type)=>{
        switch(type){
            case "work":
                return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_Briefcase2FillIcon__WEBPACK_IMPORTED_MODULE_10___default()), {
                    size: 16
                });
            case "other":
                return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_MapPinFillIcon__WEBPACK_IMPORTED_MODULE_11___default()), {
                    size: 16
                });
            case "home":
            default:
                return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_UserLocationFillIcon__WEBPACK_IMPORTED_MODULE_9___default()), {
                    size: 16
                });
        }
    };
    const { updateAddress , updateLocation , updateLocationId  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_2__/* .useSettings */ .r)();
    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();
    const handleChange = (item)=>{
        updateAddress(item.address?.address);
        updateLocation(item.location.join(","));
        updateLocationId(item.id.toString());
    };
    const controlProps = (item)=>({
            checked: Boolean(item.active),
            onChange: ()=>{
                handleChange(item);
                setActive(item.id);
                handleCloseList();
            },
            value: String(item.id),
            id: String(item.id),
            name: "addrss",
            inputProps: {
                "aria-label": String(item.id)
            }
        });
    const { mutate: setActive  } = (0,react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({
        mutationFn: (id)=>services_address__WEBPACK_IMPORTED_MODULE_6__/* ["default"].setDefault */ .Z.setDefault(id),
        onMutate: async (id)=>{
            await queryClient.cancelQueries("addresses");
            const prevAddresses = queryClient.getQueryData("addresses");
            queryClient.setQueryData("addresses", (old)=>{
                if (!old) return prevAddresses;
                return old.flatMap((addressList)=>addressList).map((oldAddress)=>{
                    if (oldAddress.id === id) {
                        updateAddress(oldAddress.address?.address);
                        updateLocation(`${oldAddress.location.at(0)},${oldAddress.location.at(1)}`);
                        return {
                            ...oldAddress,
                            active: true
                        };
                    }
                    return {
                        ...oldAddress,
                        active: false
                    };
                });
            });
        }
    });
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().addressWrapper),
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().list),
                children: addresses?.map((item)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().radioGroup),
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().radio),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_inputs_radioInput__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                                        ...controlProps(item)
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                        className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().addressTypeIcon),
                                        children: getAddressTypeIcon(item.type)
                                    }),
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", {
                                        className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().label),
                                        htmlFor: String(item.id),
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                                className: `${(_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text)} ${!item.title && (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().twoLine)}`,
                                                children: item.title ? item.title : item.address?.address
                                            }),
                                            item.title && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                                className: `${(_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text)} ${(_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().desc)}`,
                                                children: item.address?.address
                                            })
                                        ]
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                                onClick: ()=>onSelectAddress(item),
                                className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().edit),
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_EqualizerFillIcon__WEBPACK_IMPORTED_MODULE_8___default()), {
                                    size: 16
                                })
                            })
                        ]
                    }, item.id))
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                onClick: ()=>{
                    handleOpenAddressModal();
                    handleCloseList();
                },
                className: (_addressContainer_module_scss__WEBPACK_IMPORTED_MODULE_12___default().add),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_AddLineIcon__WEBPACK_IMPORTED_MODULE_7___default()), {}),
                    " ",
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                        children: t("add.address")
                    })
                ]
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 15924:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _errorBoundary_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(86261);
/* harmony import */ var _errorBoundary_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_errorBoundary_module_scss__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6684);
/* harmony import */ var components_button_darkButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(94660);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(41664);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(57987);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_icons__WEBPACK_IMPORTED_MODULE_2__, react_i18next__WEBPACK_IMPORTED_MODULE_5__]);
([components_icons__WEBPACK_IMPORTED_MODULE_2__, react_i18next__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);







class ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {
    constructor(props){
        super(props);
        this.state = {
            hasError: false
        };
    }
    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return {
            hasError: true
        };
    }
    componentDidCatch(error, errorInfo) {
        console.log({
            error,
            errorInfo
        });
    }
    render() {
        if (this.state.hasError) {
            return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_errorBoundary_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: (_errorBoundary_module_scss__WEBPACK_IMPORTED_MODULE_6___default().header),
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
                            href: "/",
                            style: {
                                display: "block"
                            },
                            onClick: ()=>this.setState({
                                    hasError: false
                                }),
                            children: this.props.isDarkMode ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_2__/* .BrandLogoDark */ .$C, {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_2__/* .BrandLogo */ .Oc, {})
                        })
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_errorBoundary_module_scss__WEBPACK_IMPORTED_MODULE_6___default().body),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h1", {
                                children: this.props.t("error.something.went.wrong")
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_button_darkButton__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                                type: "button",
                                onClick: ()=>this.setState({
                                        hasError: false
                                    }),
                                children: this.props.t("try.again")
                            })
                        ]
                    })
                ]
            });
        }
        return this.props.children;
    }
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.withTranslation)()(ErrorBoundary));

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 44190:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(21697);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);
/* eslint-disable @next/next/no-img-element */ 



const uiTypes = {
    "1": next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/* import() */ 7662).then(__webpack_require__.bind(__webpack_require__, 57662)), {
        loadableGenerated: {
            modules: [
                "..\\containers\\layout\\footer\\footer.tsx -> " + "containers/layout/footer/v1"
            ]
        }
    }),
    "2": next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/* import() */ 9550).then(__webpack_require__.bind(__webpack_require__, 99550)), {
        loadableGenerated: {
            modules: [
                "..\\containers\\layout\\footer\\footer.tsx -> " + "containers/layout/footer/v2"
            ]
        }
    }),
    "3": next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/* import() */ 9550).then(__webpack_require__.bind(__webpack_require__, 99550)), {
        loadableGenerated: {
            modules: [
                "..\\containers\\layout\\footer\\footer.tsx -> " + "containers/layout/footer/v2"
            ]
        }
    }),
    "4": next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/* import() */ 9550).then(__webpack_require__.bind(__webpack_require__, 99550)), {
        loadableGenerated: {
            modules: [
                "..\\containers\\layout\\footer\\footer.tsx -> " + "containers/layout/footer/v2"
            ]
        }
    })
};
function Footer({}) {
    const { settings  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_2__/* .useSettings */ .r)();
    const Ui = uiTypes[settings?.ui_type];
    const FooterV1 = uiTypes["1"];
    return !!Ui ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(Ui, {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(FooterV1, {});
}


/***/ }),

/***/ 64986:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(41664);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _header_module_scss__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(50761);
/* harmony import */ var _header_module_scss__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(_header_module_scss__WEBPACK_IMPORTED_MODULE_19__);
/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6684);
/* harmony import */ var remixicon_react_SunFillIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(88873);
/* harmony import */ var remixicon_react_SunFillIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_SunFillIcon__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var remixicon_react_MoonFillIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(92379);
/* harmony import */ var remixicon_react_MoonFillIcon__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_MoonFillIcon__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(80108);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var containers_searchContainer_searchContainer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(95471);
/* harmony import */ var components_languagePopover_languagePopover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(45049);
/* harmony import */ var components_currencyList_currencyList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(1729);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(29969);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(37490);
/* harmony import */ var hooks_usePopover__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(58287);
/* harmony import */ var containers_addressContainer_addressContainer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(41380);
/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(80892);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(57987);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_17__);
/* harmony import */ var components_notificationStats_notificationStats__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(61105);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_icons__WEBPACK_IMPORTED_MODULE_3__, containers_searchContainer_searchContainer__WEBPACK_IMPORTED_MODULE_8__, components_languagePopover_languagePopover__WEBPACK_IMPORTED_MODULE_9__, components_currencyList_currencyList__WEBPACK_IMPORTED_MODULE_10__, containers_addressContainer_addressContainer__WEBPACK_IMPORTED_MODULE_14__, react_i18next__WEBPACK_IMPORTED_MODULE_16__, components_notificationStats_notificationStats__WEBPACK_IMPORTED_MODULE_18__]);
([components_icons__WEBPACK_IMPORTED_MODULE_3__, containers_searchContainer_searchContainer__WEBPACK_IMPORTED_MODULE_8__, components_languagePopover_languagePopover__WEBPACK_IMPORTED_MODULE_9__, components_currencyList_currencyList__WEBPACK_IMPORTED_MODULE_10__, containers_addressContainer_addressContainer__WEBPACK_IMPORTED_MODULE_14__, react_i18next__WEBPACK_IMPORTED_MODULE_16__, components_notificationStats_notificationStats__WEBPACK_IMPORTED_MODULE_18__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);




















const AppDrawer = next_dynamic__WEBPACK_IMPORTED_MODULE_7___default()(()=>Promise.all(/* import() */[__webpack_require__.e(7107), __webpack_require__.e(4898)]).then(__webpack_require__.bind(__webpack_require__, 44898)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\layout\\header\\header.tsx -> " + "components/appDrawer/appDrawer"
        ]
    }
});
const PopoverContainer = next_dynamic__WEBPACK_IMPORTED_MODULE_7___default()(()=>__webpack_require__.e(/* import() */ 6060).then(__webpack_require__.bind(__webpack_require__, 56060)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\layout\\header\\header.tsx -> " + "containers/popover/popover"
        ]
    }
});
const ProfileDropdown = next_dynamic__WEBPACK_IMPORTED_MODULE_7___default()(()=>__webpack_require__.e(/* import() */ 1502).then(__webpack_require__.bind(__webpack_require__, 21502)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\layout\\header\\header.tsx -> " + "components/profileDropdown/profileDropdown"
        ]
    }
});
function Header() {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_16__.useTranslation)();
    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_17__.useRouter)();
    const { isDarkMode , toggleDarkMode  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_6__/* .ThemeContext */ .N);
    const [appDrawer, handleOpenAppDrawer, handleCloseAppDrawer] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z)();
    const [openLang, anchorLang, handleOpenLang, handleCloseLang] = (0,hooks_usePopover__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z)();
    const searchContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const [openCurrency, anchorCurrency, handleOpenCurrency, handleCloseCurrency] = (0,hooks_usePopover__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z)();
    const { isAuthenticated , user  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_11__/* .useAuth */ .a)();
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().container),
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                className: "fluid-container",
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("header", {
                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().header),
                    children: [
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().navItem),
                            children: [
                                !isAuthenticated && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().menuBtn),
                                    onClick: handleOpenAppDrawer,
                                    children: "menu"
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                                    href: "/",
                                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().brandLogo),
                                    children: isDarkMode ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_3__/* .BrandLogoDark */ .$C, {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_3__/* .BrandLogo */ .Oc, {})
                                })
                            ]
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            className: `${(_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().navItem)} ${(_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().searchBar)}`,
                            ref: searchContainerRef,
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().search),
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_searchContainer_searchContainer__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
                                    searchContainerRef: searchContainerRef
                                })
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().navItem),
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_addressContainer_addressContainer__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {})
                        }),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().navItem),
                            children: [
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().actions),
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                                            className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_19___default().iconBtn),
                                            onClick: toggleDarkMode,
                                            children: isDarkMode ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_MoonFillIcon__WEBPACK_IMPORTED_MODULE_5___default()), {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_SunFillIcon__WEBPACK_IMPORTED_MODULE_4___default()), {})
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_notificationStats_notificationStats__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {})
                                    ]
                                }),
                                isAuthenticated ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ProfileDropdown, {
                                    data: user
                                }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
                                    onClick: ()=>push("/login"),
                                    children: t("login")
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(AppDrawer, {
                open: appDrawer,
                handleClose: handleCloseAppDrawer
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(PopoverContainer, {
                open: openLang,
                anchorEl: anchorLang,
                onClose: handleCloseLang,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_languagePopover_languagePopover__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                    onClose: handleCloseLang
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(PopoverContainer, {
                open: openCurrency,
                anchorEl: anchorCurrency,
                onClose: handleCloseCurrency,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_currencyList_currencyList__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {
                    onClose: handleCloseCurrency
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 98461:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ Layout)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _header_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(64986);
/* harmony import */ var _mobileHeader_mobileHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35913);
/* harmony import */ var _profileHeader_profileHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(62191);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var services_cart__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(18423);
/* harmony import */ var hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(34349);
/* harmony import */ var redux_slices_userCart__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(96477);
/* harmony import */ var redux_slices_cart__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(13508);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(29969);
/* harmony import */ var redux_slices_currency__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(64698);
/* harmony import */ var services_currency__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(85221);
/* harmony import */ var services_language__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(12067);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var services_information__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(49073);
/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(21697);
/* harmony import */ var containers_errorBoundary_errorBoundary__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(15924);
/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(80108);
/* harmony import */ var _footer_footer__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(44190);
/* harmony import */ var services_translations__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(14303);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(18074);
/* harmony import */ var utils_createSettings__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(58648);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(1635);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_24__);
/* harmony import */ var dayjs_locale_nl__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(11067);
/* harmony import */ var dayjs_locale_nl__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_nl__WEBPACK_IMPORTED_MODULE_25__);
/* harmony import */ var dayjs_locale_pl__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(83490);
/* harmony import */ var dayjs_locale_pl__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_pl__WEBPACK_IMPORTED_MODULE_26__);
/* harmony import */ var dayjs_locale_pt_br__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(45288);
/* harmony import */ var dayjs_locale_pt_br__WEBPACK_IMPORTED_MODULE_27___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_pt_br__WEBPACK_IMPORTED_MODULE_27__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_header_header__WEBPACK_IMPORTED_MODULE_3__, _mobileHeader_mobileHeader__WEBPACK_IMPORTED_MODULE_4__, _profileHeader_profileHeader__WEBPACK_IMPORTED_MODULE_5__, services_cart__WEBPACK_IMPORTED_MODULE_8__, services_currency__WEBPACK_IMPORTED_MODULE_14__, services_language__WEBPACK_IMPORTED_MODULE_15__, services_information__WEBPACK_IMPORTED_MODULE_17__, containers_errorBoundary_errorBoundary__WEBPACK_IMPORTED_MODULE_19__, services_translations__WEBPACK_IMPORTED_MODULE_22__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_23__]);
([_header_header__WEBPACK_IMPORTED_MODULE_3__, _mobileHeader_mobileHeader__WEBPACK_IMPORTED_MODULE_4__, _profileHeader_profileHeader__WEBPACK_IMPORTED_MODULE_5__, services_cart__WEBPACK_IMPORTED_MODULE_8__, services_currency__WEBPACK_IMPORTED_MODULE_14__, services_language__WEBPACK_IMPORTED_MODULE_15__, services_information__WEBPACK_IMPORTED_MODULE_17__, containers_errorBoundary_errorBoundary__WEBPACK_IMPORTED_MODULE_19__, services_translations__WEBPACK_IMPORTED_MODULE_22__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_23__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);





























const PushNotification = next_dynamic__WEBPACK_IMPORTED_MODULE_16___default()(()=>__webpack_require__.e(/* import() */ 7527).then(__webpack_require__.bind(__webpack_require__, 67527)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\layout\\layout.tsx -> " + "containers/pushNotification/pushNotification"
        ]
    }
});
const profileRoutes = [
    "checkout",
    "profile",
    "settings",
    "help",
    "orders/",
    "be-seller"
];
function Layout({ children , locale  }) {
    const { pathname  } = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();
    const { addResourceBundle  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_23__/* ["default"] */ .Z)();
    const isProfileRoute = profileRoutes.find((item)=>pathname.includes(item));
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_6__.useMediaQuery)("(min-width:1140px)");
    const dispatch = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__/* .useAppDispatch */ .T)();
    const { isAuthenticated  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_12__/* .useAuth */ .a)();
    const cart = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__/* .useAppSelector */ .C)(redux_slices_cart__WEBPACK_IMPORTED_MODULE_11__/* .selectCart */ .KY);
    const currency = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_9__/* .useAppSelector */ .C)(redux_slices_currency__WEBPACK_IMPORTED_MODULE_13__/* .selectCurrency */ .j);
    const { updateSettings  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_18__/* .useSettings */ .r)();
    const { isDarkMode , setDirection  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_20__/* .ThemeContext */ .N);
    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();
    const isShopDetailPage = router.pathname.startsWith("/shop/");
    (0,react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)([
        "translation",
        locale
    ], ()=>services_translations__WEBPACK_IMPORTED_MODULE_22__/* ["default"].getAll */ .Z.getAll({
            lang: locale
        }), {
        enabled: !!locale,
        onSuccess: (data)=>{
            addResourceBundle(locale, "translation", data.data);
        }
    });
    (0,react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)("currencies", ()=>services_currency__WEBPACK_IMPORTED_MODULE_14__/* ["default"].getAll */ .Z.getAll(), {
        onSuccess: (data)=>{
            // Prioritize BRL currency for Brazilian market
            const brlCurrency = data.data.find((item)=>item.title?.toLowerCase().includes("real") || item.symbol === "R$" || item.title?.toLowerCase().includes("brl"));
            const activeCurrency = brlCurrency || data.data.find((item)=>item.default);
            const savedCurrency = data.data.find((item)=>item.id === currency?.id);
            dispatch((0,redux_slices_currency__WEBPACK_IMPORTED_MODULE_13__/* .setDefaultCurrency */ .bJ)(activeCurrency));
            if (savedCurrency) {
                dispatch((0,redux_slices_currency__WEBPACK_IMPORTED_MODULE_13__/* .setCurrency */ .NW)(savedCurrency));
            } else {
                dispatch((0,redux_slices_currency__WEBPACK_IMPORTED_MODULE_13__/* .setCurrency */ .NW)(activeCurrency));
            }
        }
    });
    (0,react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)("languages", ()=>services_language__WEBPACK_IMPORTED_MODULE_15__/* ["default"].getAllActive */ .Z.getAllActive(), {
        onSuccess: (data)=>{
            const isRTL = !!data?.data.find((item)=>item.locale == locale)?.backward;
            setDirection(isRTL ? "rtl" : "ltr");
        }
    });
    (0,react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)("settings", ()=>services_information__WEBPACK_IMPORTED_MODULE_17__/* ["default"].getSettings */ .Z.getSettings(), {
        onSuccess: (data)=>{
            const obj = (0,utils_createSettings__WEBPACK_IMPORTED_MODULE_28__/* ["default"] */ .Z)(data.data);
            updateSettings({
                payment_type: obj.payment_type,
                instagram_url: obj.instagram,
                facebook_url: obj.facebook,
                twitter_url: obj.twitter,
                referral_active: obj.referral_active,
                otp_expire_time: obj.otp_expire_time,
                customer_app_android: obj.customer_app_android,
                customer_app_ios: obj.customer_app_ios,
                delivery_app_android: obj.delivery_app_android,
                delivery_app_ios: obj.delivery_app_ios,
                vendor_app_android: obj.vendor_app_android,
                vendor_app_ios: obj.vendor_app_ios,
                group_order: obj.group_order,
                footer_text: obj.footer_text,
                ui_type: obj.ui_type,
                address_text: obj.address,
                phone: obj.phone,
                email: obj.email,
                reservation_time_durations: obj.reservation_time_durations,
                reservation_before_time: obj.reservation_before_time,
                min_reservation_time: obj.min_reservation_time,
                active_parcel: obj.active_parcel,
                before_order_phone_required: obj.before_order_phone_required,
                reservation_enable_for_user: obj.reservation_enable_for_user
            });
        }
    });
    const { mutate: insertProducts  } = (0,react_query__WEBPACK_IMPORTED_MODULE_7__.useMutation)({
        mutationFn: (data)=>services_cart__WEBPACK_IMPORTED_MODULE_8__/* ["default"].insert */ .Z.insert(data),
        onSuccess: (data)=>{
            dispatch((0,redux_slices_cart__WEBPACK_IMPORTED_MODULE_11__/* .clearCart */ .LL)());
            dispatch((0,redux_slices_userCart__WEBPACK_IMPORTED_MODULE_10__/* .updateUserCart */ .CR)(data.data));
        }
    });
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (isAuthenticated && !!cart.length) {
            let addons = [];
            let products = [];
            cart.forEach((item)=>{
                products.push({
                    stock_id: item.stock.id,
                    quantity: item.quantity
                });
                item.addons.forEach((el)=>{
                    addons.push({
                        stock_id: el.stock.id,
                        quantity: el.quantity,
                        parent_id: item.stock.id
                    });
                });
            });
            const payload = {
                shop_id: cart.find((item)=>!!item.shop_id)?.shop_id,
                currency_id: currency?.id,
                rate: currency?.rate,
                products: [
                    ...products,
                    ...addons
                ]
            };
            insertProducts(payload);
        }
    }, [
        cart,
        currency,
        isAuthenticated,
        insertProducts
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        dayjs__WEBPACK_IMPORTED_MODULE_24___default().locale(locale);
    }, [
        locale
    ]);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_errorBoundary_errorBoundary__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
        isDarkMode: isDarkMode,
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
            className: "layout-container",
            children: [
                isProfileRoute ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_profileHeader_profileHeader__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {}) : isDesktop ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_header_header__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mobileHeader_mobileHeader__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
                    isShopDetailPage: isShopDetailPage
                }),
                children,
                isAuthenticated && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(PushNotification, {}),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_footer_footer__WEBPACK_IMPORTED_MODULE_21__/* ["default"] */ .Z, {})
            ]
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 35913:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ MobileHeader)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(41664);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(63173);
/* harmony import */ var _mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6684);
/* harmony import */ var remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(78428);
/* harmony import */ var remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(80108);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var containers_addressContainer_addressContainer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(41380);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(37490);
/* harmony import */ var components_notificationStats_notificationStats__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(61105);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_icons__WEBPACK_IMPORTED_MODULE_3__, containers_addressContainer_addressContainer__WEBPACK_IMPORTED_MODULE_7__, components_notificationStats_notificationStats__WEBPACK_IMPORTED_MODULE_9__]);
([components_icons__WEBPACK_IMPORTED_MODULE_3__, containers_addressContainer_addressContainer__WEBPACK_IMPORTED_MODULE_7__, components_notificationStats_notificationStats__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);











const AppDrawer = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>Promise.all(/* import() */[__webpack_require__.e(7107), __webpack_require__.e(4898)]).then(__webpack_require__.bind(__webpack_require__, 44898)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\layout\\mobileHeader\\mobileHeader.tsx -> " + "components/appDrawer/appDrawer"
        ]
    }
});
const MobileSearchContainer = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>__webpack_require__.e(/* import() */ 2742).then(__webpack_require__.bind(__webpack_require__, 32742)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\layout\\mobileHeader\\mobileHeader.tsx -> " + "containers/mobileSearchContainer/mobileSearchContainer"
        ]
    }
});
function MobileHeader({ isShopDetailPage =false  }) {
    const { isDarkMode  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_5__/* .ThemeContext */ .N);
    const [appDrawer, handleOpenAppDrawer, handleCloseAppDrawer] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z)();
    const [searchModal, handleOpenSearchModal, handleCloseSearchModal] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z)();
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("header", {
        className: `container ${(_mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().header)} ${isShopDetailPage ? "" : (_mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().stickyHeader)}`,
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                className: (_mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().navItem),
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                        className: (_mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().menuBtn),
                        onClick: handleOpenAppDrawer,
                        children: "menu"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {
                        href: "/",
                        className: (_mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().brandLogo),
                        children: isDarkMode ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_3__/* .BrandLogoDark */ .$C, {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_icons__WEBPACK_IMPORTED_MODULE_3__/* .BrandLogo */ .Oc, {})
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().actions),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_notificationStats_notificationStats__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {}),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_addressContainer_addressContainer__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {}),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
                                className: (_mobileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().iconBtn),
                                onClick: handleOpenSearchModal,
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_4___default()), {})
                            })
                        ]
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(AppDrawer, {
                open: appDrawer,
                handleClose: handleCloseAppDrawer
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(MobileSearchContainer, {
                open: searchModal,
                onClose: handleCloseSearchModal,
                fullScreen: true
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 62191:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ProfileHeader)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _profileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(87337);
/* harmony import */ var _profileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_profileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(71116);
/* harmony import */ var remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(51406);
/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(57987);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(29969);
/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(80892);
/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(80108);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_5__]);
react_i18next__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];











const ProfileDropdown = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>__webpack_require__.e(/* import() */ 1502).then(__webpack_require__.bind(__webpack_require__, 21502)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\layout\\profileHeader\\profileHeader.tsx -> " + "components/profileDropdown/profileDropdown"
        ]
    }
});
function ProfileHeader({}) {
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();
    const { pathname , back , push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();
    const { user , isAuthenticated  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_7__/* .useAuth */ .a)();
    const { direction  } = (0,contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_9__/* .useTheme */ .F)();
    const handleBack = ()=>{
        if (pathname.includes("orders")) {
            push("/orders");
            return;
        }
        if (pathname.includes("checkout")) {
            back();
            return;
        }
        push("/");
    };
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        className: (_profileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().container),
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("header", {
            className: `container ${(_profileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().header)}`,
            children: [
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                    className: (_profileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().backBtn),
                    onClick: handleBack,
                    children: [
                        direction === "rtl" ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3___default()), {}) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_2___default()), {}),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                            className: (_profileHeader_module_scss__WEBPACK_IMPORTED_MODULE_10___default().text),
                            children: t("back")
                        })
                    ]
                }),
                isAuthenticated ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ProfileDropdown, {
                    data: user
                }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
                        onClick: ()=>push("/login"),
                        children: t("login")
                    })
                })
            ]
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 38104:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ MainContainer)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(18074);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var services_translations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(14303);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__, services_translations__WEBPACK_IMPORTED_MODULE_4__]);
([hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__, services_translations__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);





function MainContainer({ children , locale  }) {
    const { addResourceBundle , changeLanguage  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)();
    (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([
        "translation",
        locale
    ], ()=>services_translations__WEBPACK_IMPORTED_MODULE_4__/* ["default"].getAll */ .Z.getAll({
            lang: locale
        }), {
        enabled: !!locale,
        onSuccess: (data)=>{
            addResourceBundle(locale, "translation", data.data);
            changeLanguage(locale);
        }
    });
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: children
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 95471:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ SearchContainer)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(78428);
/* harmony import */ var remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _searchContainer_module_scss__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(95645);
/* harmony import */ var _searchContainer_module_scss__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(_searchContainer_module_scss__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57987);
/* harmony import */ var hooks_useDebounce__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(48606);
/* harmony import */ var hooks_useDidUpdate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(68416);
/* harmony import */ var components_searchResult_searchResult__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(99496);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var services_shop__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1612);
/* harmony import */ var services_product__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(32837);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var hooks_useUserLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(2950);
/* harmony import */ var components_searchSuggestion_searchSuggestion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(54935);
/* harmony import */ var hooks_useRedux__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34349);
/* harmony import */ var redux_slices_search__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(20330);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_searchResult_searchResult__WEBPACK_IMPORTED_MODULE_6__, services_shop__WEBPACK_IMPORTED_MODULE_8__, services_product__WEBPACK_IMPORTED_MODULE_9__, components_searchSuggestion_searchSuggestion__WEBPACK_IMPORTED_MODULE_13__]);
([react_i18next__WEBPACK_IMPORTED_MODULE_3__, components_searchResult_searchResult__WEBPACK_IMPORTED_MODULE_6__, services_shop__WEBPACK_IMPORTED_MODULE_8__, services_product__WEBPACK_IMPORTED_MODULE_9__, components_searchSuggestion_searchSuggestion__WEBPACK_IMPORTED_MODULE_13__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);

















const PopoverContainer = next_dynamic__WEBPACK_IMPORTED_MODULE_11___default()(()=>__webpack_require__.e(/* import() */ 6060).then(__webpack_require__.bind(__webpack_require__, 56060)), {
    loadableGenerated: {
        modules: [
            "..\\containers\\searchContainer\\searchContainer.tsx -> " + "containers/popover/popover"
        ]
    }
});
const shopRoutes = [
    "restaurant",
    "shop/"
];
function SearchContainer({ searchContainerRef  }) {
    const { t , i18n  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();
    const locale = i18n.language;
    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
    const debouncedSearchTerm = (0,hooks_useDebounce__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)(searchTerm.trim(), 400);
    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const open = Boolean(anchorEl);
    const [anchorSuggestion, setAnchorSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const openSuggetion = Boolean(anchorSuggestion);
    const { pathname , query  } = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();
    const shopId = Number(query.id);
    const isRestaurantRoute = shopRoutes.some((item)=>pathname.includes(item));
    const location = (0,hooks_useUserLocation__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z)();
    const dispatch = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_14__/* .useAppDispatch */ .T)();
    const history = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_14__/* .useAppSelector */ .C)(redux_slices_search__WEBPACK_IMPORTED_MODULE_15__/* .selectSearchHistory */ .ag);
    const { data  } = (0,react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)([
        "shop",
        shopId,
        locale
    ], ()=>services_shop__WEBPACK_IMPORTED_MODULE_8__/* ["default"].getById */ .Z.getById(shopId), {
        enabled: isRestaurantRoute
    });
    const { data: shops , fetchNextPage: fetchShopsNextPage , hasNextPage: hasShopsNextPage , isFetchingNextPage: isFetchingShopsNextPage , isLoading: isShopsLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_7__.useInfiniteQuery)([
        "shopResult",
        debouncedSearchTerm,
        location,
        locale
    ], ({ pageParam =1  })=>services_shop__WEBPACK_IMPORTED_MODULE_8__/* ["default"].search */ .Z.search({
            search: debouncedSearchTerm,
            page: pageParam,
            address: location,
            open: 1
        }), {
        getNextPageParam: (lastPage)=>{
            if (lastPage.meta.current_page < lastPage.meta.last_page) {
                return lastPage.meta.current_page + 1;
            }
            return undefined;
        },
        retry: false,
        enabled: !!debouncedSearchTerm && !isRestaurantRoute
    });
    const { data: products , fetchNextPage: fetchProductsNextPage , hasNextPage: hasProductsNextPage , isFetchingNextPage: isFetchingProductsNextPage , isLoading: isProductsLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_7__.useInfiniteQuery)([
        "productResult",
        debouncedSearchTerm,
        locale
    ], ({ pageParam =1  })=>services_product__WEBPACK_IMPORTED_MODULE_9__/* ["default"].search */ .Z.search({
            search: debouncedSearchTerm,
            page: pageParam,
            shop_id: isRestaurantRoute ? query.id : undefined,
            address: location
        }), {
        getNextPageParam: (lastPage)=>{
            if (lastPage.meta.current_page < lastPage.meta.last_page) {
                return lastPage.meta.current_page + 1;
            }
            return undefined;
        },
        retry: false,
        enabled: !!debouncedSearchTerm,
        onSuccess: ()=>{
            dispatch((0,redux_slices_search__WEBPACK_IMPORTED_MODULE_15__/* .addToSearch */ .fy)(debouncedSearchTerm));
        }
    });
    const resetSearch = ()=>setSearchTerm("");
    const handleOpen = ()=>setAnchorEl(searchContainerRef.current);
    const handleClose = ()=>setAnchorEl(null);
    const handleOpenSuggestion = ()=>setAnchorSuggestion(searchContainerRef.current);
    const handleCloseSuggestion = ()=>setAnchorSuggestion(null);
    const handleClick = ()=>{
        handleOpenSuggestion();
        if (debouncedSearchTerm) {
            handleOpen();
            handleCloseSuggestion();
        }
    };
    (0,hooks_useDidUpdate__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)(()=>{
        if (debouncedSearchTerm) {
            handleOpen();
            handleCloseSuggestion();
        } else {
            handleClose();
            handleOpenSuggestion();
        }
    }, [
        debouncedSearchTerm
    ]);
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        className: (_searchContainer_module_scss__WEBPACK_IMPORTED_MODULE_16___default().search),
        ref: searchContainerRef,
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("label", {
                htmlFor: "search",
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_2___default()), {})
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("input", {
                type: "text",
                id: "search",
                placeholder: isRestaurantRoute ? t("search.products.in", {
                    shop: data?.data?.translation?.title
                }) : t("search.restaurants.products"),
                autoComplete: "off",
                value: searchTerm,
                onChange: (event)=>setSearchTerm(event.target.value),
                onClick: handleClick
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(PopoverContainer, {
                open: open,
                anchorEl: anchorEl,
                onClose: handleClose,
                anchorOrigin: {
                    vertical: "bottom",
                    horizontal: "left"
                },
                anchorPosition: {
                    left: 30,
                    top: 30
                },
                disableAutoFocus: true,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_searchResult_searchResult__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                    isVisibleShops: !isRestaurantRoute,
                    shops: shops?.pages?.flatMap((item)=>item.data) || [],
                    products: products?.pages?.flatMap((item)=>item.data) || [],
                    isLoading: isShopsLoading || isProductsLoading,
                    handleClickItem: ()=>{
                        // resetSearch();
                        handleClose();
                    },
                    productTotal: products?.pages ? products.pages[0].meta.total : 0,
                    shopTotal: shops?.pages ? shops.pages[0].meta.total : 0,
                    isFetchingShopsNextPage: isFetchingShopsNextPage,
                    isFetchingProductsNextPage: isFetchingProductsNextPage,
                    hasProductsNextPage: !!hasProductsNextPage,
                    hasShopsNextPage: !!hasShopsNextPage,
                    fetchProductsNextPage: fetchProductsNextPage,
                    fetchShopsNextPage: fetchShopsNextPage
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(PopoverContainer, {
                open: openSuggetion && !!history.length,
                anchorEl: anchorSuggestion,
                onClose: handleCloseSuggestion,
                anchorOrigin: {
                    vertical: "bottom",
                    horizontal: "left"
                },
                anchorPosition: {
                    left: 30,
                    top: 30
                },
                disableAutoFocus: true,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_searchSuggestion_searchSuggestion__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
                    setSearchTerm: setSearchTerm
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 8304:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "H": () => (/* binding */ AuthProvider)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(60401);
/* harmony import */ var services_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(50849);
/* harmony import */ var _auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(29969);
/* harmony import */ var utils_session__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(24941);
/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(21697);
/* harmony import */ var hooks_useRedux__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(34349);
/* harmony import */ var redux_slices_userCart__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(96477);
/* harmony import */ var redux_slices_cart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(13508);
/* harmony import */ var redux_slices_favoriteRestaurants__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(67560);
/* harmony import */ var redux_slices_shopFilter__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(5215);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var services_profile__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(45641);
/* harmony import */ var redux_slices_search__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(20330);
/* harmony import */ var redux_slices_currency__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(64698);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, services_firebase__WEBPACK_IMPORTED_MODULE_3__, utils_session__WEBPACK_IMPORTED_MODULE_5__, services_profile__WEBPACK_IMPORTED_MODULE_13__]);
([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, services_firebase__WEBPACK_IMPORTED_MODULE_3__, utils_session__WEBPACK_IMPORTED_MODULE_5__, services_profile__WEBPACK_IMPORTED_MODULE_13__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);
//@ts-nocheck

















var AuthActionKind;
(function(AuthActionKind) {
    AuthActionKind["SIGN_IN"] = "SIGN_IN";
    AuthActionKind["LOGOUT"] = "LOGOUT";
})(AuthActionKind || (AuthActionKind = {}));
function reducer(state, action) {
    const { type , payload  } = action;
    switch(type){
        case AuthActionKind.SIGN_IN:
            (0,utils_session__WEBPACK_IMPORTED_MODULE_5__/* .setCookie */ .d8)("user", JSON.stringify(payload));
            return payload;
        case AuthActionKind.LOGOUT:
            (0,utils_session__WEBPACK_IMPORTED_MODULE_5__/* .removeCookie */ .nJ)("access_token");
            (0,utils_session__WEBPACK_IMPORTED_MODULE_5__/* .setCookie */ .d8)("user", null);
            return null;
        default:
            return state;
    }
}
function AuthProvider({ children , authState  }) {
    const [user, authDispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(reducer, authState);
    const { resetSettings  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_6__/* .useSettings */ .r)();
    const dispatch = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_7__/* .useAppDispatch */ .T)();
    const currency = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_7__/* .useAppSelector */ .C)(redux_slices_currency__WEBPACK_IMPORTED_MODULE_15__/* .selectCurrency */ .j);
    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();
    const { refetch  } = (0,react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)([
        "profile",
        currency?.id
    ], ()=>services_profile__WEBPACK_IMPORTED_MODULE_13__/* ["default"].get */ .Z.get({
            currency_id: currency?.id
        }), {
        enabled: Boolean(user),
        onSuccess: (data)=>authDispatch({
                type: AuthActionKind.SIGN_IN,
                payload: data.data
            })
    });
    function googleSignIn() {
        const googleAuthProvider = new firebase_auth__WEBPACK_IMPORTED_MODULE_2__.GoogleAuthProvider();
        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithPopup)(services_firebase__WEBPACK_IMPORTED_MODULE_3__/* .auth */ .I8, googleAuthProvider);
    }
    function facebookSignIn() {
        const facebookAuthProvider = new firebase_auth__WEBPACK_IMPORTED_MODULE_2__.FacebookAuthProvider();
        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithPopup)(services_firebase__WEBPACK_IMPORTED_MODULE_3__/* .auth */ .I8, facebookAuthProvider);
    }
    function appleSignIn() {
        const appleAuthProvider = new firebase_auth__WEBPACK_IMPORTED_MODULE_2__.OAuthProvider("apple.com");
        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithPopup)(services_firebase__WEBPACK_IMPORTED_MODULE_3__/* .auth */ .I8, appleAuthProvider);
    }
    function phoneNumberSignIn(phoneNumber) {
        const appVerifier = new firebase_auth__WEBPACK_IMPORTED_MODULE_2__.RecaptchaVerifier("sign-in-button", {
            size: "invisible",
            callback: ()=>{
                console.log("Callback!");
            }
        }, services_firebase__WEBPACK_IMPORTED_MODULE_3__/* .auth */ .I8);
        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithPhoneNumber)(services_firebase__WEBPACK_IMPORTED_MODULE_3__/* .auth */ .I8, phoneNumber, appVerifier);
    }
    function setUserData(data) {
        authDispatch({
            type: AuthActionKind.SIGN_IN,
            payload: data
        });
    }
    function logout() {
        authDispatch({
            type: AuthActionKind.LOGOUT
        });
        resetSettings();
        dispatch((0,redux_slices_userCart__WEBPACK_IMPORTED_MODULE_8__/* .clearUserCart */ .tx)());
        dispatch((0,redux_slices_cart__WEBPACK_IMPORTED_MODULE_9__/* .clearCart */ .LL)());
        dispatch((0,redux_slices_favoriteRestaurants__WEBPACK_IMPORTED_MODULE_10__/* .clearLikedRestaurants */ .dz)());
        dispatch((0,redux_slices_shopFilter__WEBPACK_IMPORTED_MODULE_11__/* .clearFilter */ .Dg)());
        dispatch((0,redux_slices_search__WEBPACK_IMPORTED_MODULE_14__/* .clearSearch */ .AQ)());
        queryClient.invalidateQueries([
            "notificationStatistic"
        ], {
            exact: false
        });
    }
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_auth_context__WEBPACK_IMPORTED_MODULE_4__/* .AuthContext.Provider */ .V.Provider, {
        value: {
            googleSignIn,
            facebookSignIn,
            appleSignIn,
            user,
            setUserData,
            isAuthenticated: Boolean(user),
            logout,
            refetchUser: refetch,
            phoneNumberSignIn
        },
        children: children
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 26191:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "Z": () => (/* binding */ MuiThemeProvider)
});

// EXTERNAL MODULE: external "react/jsx-runtime"
var jsx_runtime_ = __webpack_require__(20997);
// EXTERNAL MODULE: external "react"
var external_react_ = __webpack_require__(16689);
;// CONCATENATED MODULE: external "css-mediaquery"
const external_css_mediaquery_namespaceObject = require("css-mediaquery");
var external_css_mediaquery_default = /*#__PURE__*/__webpack_require__.n(external_css_mediaquery_namespaceObject);
// EXTERNAL MODULE: external "@mui/material"
var material_ = __webpack_require__(65692);
;// CONCATENATED MODULE: ./contexts/muiTheme/muiTheme.provider.tsx




function MuiThemeProvider({ children , deviceType: { mobile , tablet , desktop  }  }) {
    const ssrMatchMedia = (query)=>({
            matches: external_css_mediaquery_default().match(query, {
                width: mobile ? "360px" : tablet ? "768px" : "1140px"
            })
        });
    const theme = (0,material_.createTheme)({
        components: {
            MuiUseMediaQuery: {
                defaultProps: {
                    ssrMatchMedia
                }
            }
        }
    });
    return /*#__PURE__*/ jsx_runtime_.jsx(material_.ThemeProvider, {
        theme: theme,
        children: children
    });
}


/***/ }),

/***/ 25278:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "m": () => (/* binding */ SettingsProvider)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _settings_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(21697);
/* harmony import */ var utils_session__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(24941);
/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3075);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([utils_session__WEBPACK_IMPORTED_MODULE_3__]);
utils_session__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];





const location = constants_config__WEBPACK_IMPORTED_MODULE_4__/* .DEFAULT_LOCATION */ .PX;
var SettingActionKind;
(function(SettingActionKind) {
    SettingActionKind["UPDATE"] = "UPDATE_SETTINGS";
    SettingActionKind["RESET"] = "RESET_SETTINGS";
    SettingActionKind["SET_ADDRESS"] = "SET_ADDRESS";
    SettingActionKind["SET_LOCATION"] = "SET_LOCATION";
    SettingActionKind["SET_LOCATION_ID"] = "SET_LOCATION_ID";
})(SettingActionKind || (SettingActionKind = {}));
function reducer(state, action) {
    const { type , payload  } = action;
    switch(type){
        case SettingActionKind.UPDATE:
            (0,utils_session__WEBPACK_IMPORTED_MODULE_3__/* .setCookie */ .d8)("settings", JSON.stringify({
                ...state,
                ...payload
            }));
            return {
                ...state,
                ...payload
            };
        case SettingActionKind.RESET:
            (0,utils_session__WEBPACK_IMPORTED_MODULE_3__/* .removeCookie */ .nJ)("settings");
            return {
                location: state.location
            };
        case SettingActionKind.SET_LOCATION:
            (0,utils_session__WEBPACK_IMPORTED_MODULE_3__/* .setCookie */ .d8)("settings", JSON.stringify({
                ...state,
                location: payload
            }));
            return {
                ...state,
                location: payload
            };
        case SettingActionKind.SET_ADDRESS:
            (0,utils_session__WEBPACK_IMPORTED_MODULE_3__/* .setCookie */ .d8)("settings", JSON.stringify({
                ...state,
                address: payload
            }));
            return {
                ...state,
                address: payload
            };
        case SettingActionKind.SET_LOCATION_ID:
            (0,utils_session__WEBPACK_IMPORTED_MODULE_3__/* .setCookie */ .d8)("settings", JSON.stringify({
                ...state,
                location_id: payload
            }));
            return {
                ...state,
                location_id: payload
            };
        default:
            return state;
    }
}
function SettingsProvider({ children , settingsState ={} , defaultAddress  }) {
    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(reducer, {
        location,
        address: defaultAddress,
        ...settingsState
    });
    function updateSettings(data = {}) {
        dispatch({
            type: SettingActionKind.UPDATE,
            payload: data
        });
    }
    function resetSettings() {
        dispatch({
            type: SettingActionKind.RESET,
            payload: null
        });
    }
    function updateAddress(data) {
        if (data) {
            dispatch({
                type: SettingActionKind.SET_ADDRESS,
                payload: data
            });
        }
    }
    function updateLocation(data) {
        dispatch({
            type: SettingActionKind.SET_LOCATION,
            payload: data
        });
    }
    function updateLocationId(data) {
        dispatch({
            type: SettingActionKind.SET_LOCATION_ID,
            payload: data
        });
    }
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_settings_context__WEBPACK_IMPORTED_MODULE_2__/* .SettingsContext.Provider */ .J.Provider, {
        value: {
            settings: state,
            updateSettings,
            resetSettings,
            address: state?.address,
            updateAddress,
            location: state?.location,
            updateLocation,
            updateLocationId,
            location_id: state.location_id
        },
        children: children
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 80108:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "F": () => (/* binding */ useTheme),
/* harmony export */   "N": () => (/* binding */ ThemeContext)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

const ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});
const useTheme = ()=>(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);


/***/ }),

/***/ 94863:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var utils_session__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(24941);
/* harmony import */ var _theme_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(80108);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([utils_session__WEBPACK_IMPORTED_MODULE_2__]);
utils_session__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];




var ThemeActionKind;
(function(ThemeActionKind) {
    ThemeActionKind["TOGGLE_THEME"] = "TOGGLE_THEME";
    ThemeActionKind["SET_DIRECTION"] = "SET_DIRECTION";
})(ThemeActionKind || (ThemeActionKind = {}));
function reducer(state, action) {
    const { type , payload  } = action;
    switch(type){
        case ThemeActionKind.TOGGLE_THEME:
            const changedTheme = state.theme === "dark" ? "light" : "dark";
            (0,utils_session__WEBPACK_IMPORTED_MODULE_2__/* .setCookie */ .d8)("theme", changedTheme);
            return {
                ...state,
                theme: changedTheme
            };
        case ThemeActionKind.SET_DIRECTION:
            (0,utils_session__WEBPACK_IMPORTED_MODULE_2__/* .setCookie */ .d8)("dir", payload);
            return {
                ...state,
                direction: payload
            };
        default:
            return state;
    }
}
function ThemeProvider({ children , appTheme , appDirection  }) {
    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(reducer, {
        theme: appTheme,
        direction: appDirection
    });
    const toggleDarkMode = ()=>{
        dispatch({
            type: ThemeActionKind.TOGGLE_THEME
        });
    };
    const setDirection = (payload)=>{
        dispatch({
            type: ThemeActionKind.SET_DIRECTION,
            payload
        });
    };
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const body = document.body;
        body.setAttribute("data-theme", state.theme);
        body.setAttribute("dir", state.direction);
    }, [
        state
    ]);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_theme_context__WEBPACK_IMPORTED_MODULE_3__/* .ThemeContext.Provider */ .N.Provider, {
        value: {
            direction: state.direction,
            theme: state.theme,
            toggleDarkMode,
            setDirection,
            isDarkMode: state.theme === "dark"
        },
        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
            className: "App",
            "data-theme": state.theme,
            dir: state.direction,
            children: children
        })
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 68416:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

const useDidUpdate = (f, conditions)=>{
    const didMountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{
        if (!didMountRef.current) {
            didMountRef.current = true;
            return;
        }
        // Cleanup effects when f returns a function
        return f && f(); //eslint-disable-line
    }, conditions);
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useDidUpdate);


/***/ }),

/***/ 58287:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ usePopover)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

function usePopover() {
    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
    const open = Boolean(anchorEl);
    const handleOpen = (event)=>setAnchorEl(event?.currentTarget);
    const handleClose = ()=>setAnchorEl(null);
    return [
        open,
        anchorEl,
        handleOpen,
        handleClose
    ];
}


/***/ }),

/***/ 2950:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ useUserLocation)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(21697);


function useUserLocation() {
    const { location: userLocation  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_1__/* .useSettings */ .r)();
    const location = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{
        const latlng = userLocation;
        if (!latlng) {
            return undefined;
        }
        return {
            latitude: latlng.split(",")[0],
            longitude: latlng.split(",")[1]
        };
    }, [
        userLocation
    ]);
    return location;
}


/***/ }),

/***/ 15212:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "E": () => (/* binding */ NotificationStatus)
/* harmony export */ });
var NotificationStatus;
(function(NotificationStatus) {
    NotificationStatus["NEW_ORDER"] = "new_order";
    NotificationStatus["NEW_USER_BY_REFERRAL"] = "new_user_by_referral";
    NotificationStatus["STATUS_CHANGED"] = "status_changed";
    NotificationStatus["NEW_IN_TABLE"] = "new_in_table";
    NotificationStatus["BOOKING_STATUS"] = "booking_status";
    NotificationStatus["NEW_BOOKING"] = "new_booking";
    NotificationStatus["NEWS_PUBLISH"] = "news_publish";
    NotificationStatus["DELIVERY_REFUNDED"] = "order_refunded";
})(NotificationStatus || (NotificationStatus = {}));


/***/ }),

/***/ 13847:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ExtendedApp)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _chatscope_chat_ui_kit_styles_dist_default_styles_min_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97512);
/* harmony import */ var _chatscope_chat_ui_kit_styles_dist_default_styles_min_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_chatscope_chat_ui_kit_styles_dist_default_styles_min_css__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(86764);
/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _styles_chat_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(98599);
/* harmony import */ var _styles_chat_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_chat_scss__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var next_app__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7544);
/* harmony import */ var containers_layout_layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(98461);
/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(3590);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(71853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var utils_session__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(24941);
/* harmony import */ var containers_main_mainContainer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(38104);
/* harmony import */ var contexts_theme_theme_provider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(94863);
/* harmony import */ var utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(59748);
/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(53139);
/* harmony import */ var contexts_muiTheme_muiTheme_provider__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(26191);
/* harmony import */ var utils_useDeviceType__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(36399);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(61175);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var contexts_auth_auth_provider__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(8304);
/* harmony import */ var contexts_settings_settings_provider__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(25278);
/* harmony import */ var nprogress__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(30808);
/* harmony import */ var nprogress__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(nprogress__WEBPACK_IMPORTED_MODULE_19__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(6022);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_20__);
/* harmony import */ var redux_store__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(35318);
/* harmony import */ var redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(61127);
/* harmony import */ var redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_22__);
/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(61664);
/* harmony import */ var utils_getLanguage__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(77347);
/* harmony import */ var constants_reactQuery_config__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(11198);
/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(4298);
/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_26__);
/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(5848);
/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(88819);
/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_28___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_28__);
/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(58722);
/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_29___default = /*#__PURE__*/__webpack_require__.n(swiper_css__WEBPACK_IMPORTED_MODULE_29__);
/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(69176);
/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_30___default = /*#__PURE__*/__webpack_require__.n(swiper_css_navigation__WEBPACK_IMPORTED_MODULE_30__);
/* harmony import */ var nprogress_nprogress_css__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(54204);
/* harmony import */ var nprogress_nprogress_css__WEBPACK_IMPORTED_MODULE_31___default = /*#__PURE__*/__webpack_require__.n(nprogress_nprogress_css__WEBPACK_IMPORTED_MODULE_31__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([containers_layout_layout__WEBPACK_IMPORTED_MODULE_6__, react_toastify__WEBPACK_IMPORTED_MODULE_7__, utils_session__WEBPACK_IMPORTED_MODULE_9__, containers_main_mainContainer__WEBPACK_IMPORTED_MODULE_10__, contexts_theme_theme_provider__WEBPACK_IMPORTED_MODULE_11__, utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_12__, _emotion_react__WEBPACK_IMPORTED_MODULE_13__, contexts_auth_auth_provider__WEBPACK_IMPORTED_MODULE_17__, contexts_settings_settings_provider__WEBPACK_IMPORTED_MODULE_18__, _i18n__WEBPACK_IMPORTED_MODULE_23__]);
([containers_layout_layout__WEBPACK_IMPORTED_MODULE_6__, react_toastify__WEBPACK_IMPORTED_MODULE_7__, utils_session__WEBPACK_IMPORTED_MODULE_9__, containers_main_mainContainer__WEBPACK_IMPORTED_MODULE_10__, contexts_theme_theme_provider__WEBPACK_IMPORTED_MODULE_11__, utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_12__, _emotion_react__WEBPACK_IMPORTED_MODULE_13__, contexts_auth_auth_provider__WEBPACK_IMPORTED_MODULE_17__, contexts_settings_settings_provider__WEBPACK_IMPORTED_MODULE_18__, _i18n__WEBPACK_IMPORTED_MODULE_23__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);
































next_router__WEBPACK_IMPORTED_MODULE_8__.Router.events.on("routeChangeStart", ()=>nprogress__WEBPACK_IMPORTED_MODULE_19___default().start());
next_router__WEBPACK_IMPORTED_MODULE_8__.Router.events.on("routeChangeComplete", ()=>nprogress__WEBPACK_IMPORTED_MODULE_19___default().done());
next_router__WEBPACK_IMPORTED_MODULE_8__.Router.events.on("routeChangeError", ()=>nprogress__WEBPACK_IMPORTED_MODULE_19___default().done());
const clientSideEmotionCache = (0,utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_12__/* .createEmotionCache */ .S)();
const clientSideRtlEmotionCache = (0,utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_12__/* .createRtlEmotionCache */ .b)();
const pagesWithoutLayout = [
    "register",
    "login",
    "reset-password",
    "verify-phone",
    "update-password",
    "update-details",
    "welcome"
];
const uiTypes = [
    "1",
    "2",
    "3",
    "4"
];
function ExtendedApp({ Component , pageProps , userAgent , appTheme , emotionCache , authState , settingsState , defaultAddress , locale , appDirection , uiType  }) {
    nprogress__WEBPACK_IMPORTED_MODULE_19___default().configure({
        showSpinner: false
    });
    const { pathname  } = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();
    const isAuthPage = pagesWithoutLayout.some((item)=>pathname.includes(item));
    const deviceType = (0,utils_useDeviceType__WEBPACK_IMPORTED_MODULE_15__/* .useDeviceType */ .h)(userAgent);
    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_16__.QueryClient(constants_reactQuery_config__WEBPACK_IMPORTED_MODULE_25__/* .config */ .v));
    const csEmotionCache = appDirection === "rtl" ? clientSideRtlEmotionCache : clientSideEmotionCache;
    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{
        _i18n__WEBPACK_IMPORTED_MODULE_23__/* ["default"].changeLanguage */ .Z.changeLanguage(locale);
    }, [
        locale
    ]);
    // Handle chunk loading errors
    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{
        const handleChunkError = (event)=>{
            if (event.message?.includes("Loading chunk") || event.message?.includes("Loading CSS chunk")) {
                console.warn("Chunk loading error detected, reloading page...");
                window.location.reload();
            }
        };
        const handleUnhandledRejection = (event)=>{
            if (event.reason?.message?.includes("Loading chunk") || event.reason?.message?.includes("Loading CSS chunk")) {
                console.warn("Chunk loading promise rejection detected, reloading page...");
                window.location.reload();
            }
        };
        window.addEventListener("error", handleChunkError);
        window.addEventListener("unhandledrejection", handleUnhandledRejection);
        return ()=>{
            window.removeEventListener("error", handleChunkError);
            window.removeEventListener("unhandledrejection", handleUnhandledRejection);
        };
    }, []);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_query__WEBPACK_IMPORTED_MODULE_16__.QueryClientProvider, {
        client: queryClient,
        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_query__WEBPACK_IMPORTED_MODULE_16__.Hydrate, {
            state: pageProps.dehydratedState,
            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_emotion_react__WEBPACK_IMPORTED_MODULE_13__.CacheProvider, {
                value: emotionCache || csEmotionCache,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(contexts_muiTheme_muiTheme_provider__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
                    deviceType: deviceType,
                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(contexts_theme_theme_provider__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
                        appTheme: appTheme,
                        appDirection: appDirection,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_redux__WEBPACK_IMPORTED_MODULE_20__.Provider, {
                                store: redux_store__WEBPACK_IMPORTED_MODULE_21__/* .store */ .h,
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(contexts_settings_settings_provider__WEBPACK_IMPORTED_MODULE_18__/* .SettingsProvider */ .m, {
                                        settingsState: settingsState,
                                        defaultAddress: defaultAddress,
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(contexts_auth_auth_provider__WEBPACK_IMPORTED_MODULE_17__/* .AuthProvider */ .H, {
                                            authState: authState,
                                            children: isAuthPage ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_main_mainContainer__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {
                                                locale: locale,
                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(Component, {
                                                    uiType: uiType,
                                                    ...pageProps
                                                })
                                            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_22__.PersistGate, {
                                                loading: null,
                                                persistor: redux_store__WEBPACK_IMPORTED_MODULE_21__/* .persistor */ .D,
                                                children: ()=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(containers_layout_layout__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                                                        locale: locale,
                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(Component, {
                                                            uiType: uiType,
                                                            ...pageProps
                                                        })
                                                    })
                                            })
                                        })
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {
                                        position: "top-right",
                                        autoClose: 5000,
                                        hideProgressBar: true,
                                        newestOnTop: false,
                                        closeOnClick: true,
                                        pauseOnFocusLoss: true,
                                        draggable: true,
                                        pauseOnHover: true,
                                        closeButton: false,
                                        className: "toast-alert"
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((next_script__WEBPACK_IMPORTED_MODULE_26___default()), {
                                src: `https://www.googletagmanager.com/gtag/js?id=${constants_constants__WEBPACK_IMPORTED_MODULE_27__/* .G_TAG */ .DA}`,
                                strategy: "afterInteractive"
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((next_script__WEBPACK_IMPORTED_MODULE_26___default()), {
                                id: "google-analytics",
                                strategy: "afterInteractive",
                                children: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){window.dataLayer.push(arguments);}
                gtag('js', new Date());

                gtag('config', '${constants_constants__WEBPACK_IMPORTED_MODULE_27__/* .G_TAG */ .DA}');
              `
                            })
                        ]
                    })
                })
            })
        })
    });
}
ExtendedApp.getInitialProps = async (appContext)=>{
    const appProps = await next_app__WEBPACK_IMPORTED_MODULE_5__["default"].getInitialProps(appContext);
    const { req  } = appContext.ctx;
    const userAgent = req ? req.headers["user-agent"] : navigator.userAgent;
    const appTheme = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__/* .getCookie */ .ej)("theme", appContext.ctx);
    const appDirection = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__/* .getCookie */ .ej)("dir", appContext.ctx);
    const authState = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__/* .getCookie */ .ej)("user", appContext.ctx);
    const settingsState = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__/* .getCookie */ .ej)("settings", appContext.ctx);
    const defaultAddress = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__/* .getCookie */ .ej)("address", appContext.ctx);
    const locale = (0,utils_getLanguage__WEBPACK_IMPORTED_MODULE_24__/* ["default"] */ .Z)((0,utils_session__WEBPACK_IMPORTED_MODULE_9__/* .getCookie */ .ej)("locale", appContext.ctx));
    const uiType = uiTypes.find((type)=>type === appContext.router.query?.v) || "1";
    _i18n__WEBPACK_IMPORTED_MODULE_23__/* ["default"].changeLanguage */ .Z.changeLanguage(locale);
    let props = {
        ...appProps,
        userAgent,
        appTheme,
        appDirection,
        authState,
        settingsState,
        defaultAddress,
        locale,
        uiType
    };
    return props;
};

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 82027:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const addressService = {
    create: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post("dashboard/user/addresses", data).then((res)=>res.data),
    update: (id, data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].put */ .Z.put(`dashboard/user/addresses/${id}`, data).then((res)=>res.data),
    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get("dashboard/user/addresses", {
            params
        }).then((res)=>res.data),
    setDefault: (id)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`dashboard/user/address/set-active/${id}`).then((res)=>res.data),
    delete: (id)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"]["delete"] */ .Z["delete"](`dashboard/user/addresses/delete?ids[0]=${id}`).then((res)=>res.data)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (addressService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 49073:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const informationService = {
    getSettings: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/settings`, {
            params
        }),
    getReferrals: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/rest/referral`, {
            params
        })
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (informationService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 76058:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const notificationService = {
    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/dashboard/notifications`, {
            params
        }),
    getById: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/dashboard/notifications/${id}`, {
            params
        }),
    getStatistics: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/dashboard/user/profile/notifications-statistic`, {
            params
        }),
    readAll: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/notifications/read-all`, data),
    readById: (id, data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/notifications/${id}/read-at`, data)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (notificationService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 45641:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);
_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

const profileService = {
    update: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].put */ .Z.put(`/dashboard/user/profile/update`, data),
    passwordUpdate: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/profile/password/update`, data),
    get: (params, headers)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/dashboard/user/profile/show`, {
            params,
            headers
        }),
    getNotifications: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/dashboard/user/notifications`, {
            params
        }),
    updateNotifications: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/update/notifications`, data),
    firebaseTokenUpdate: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/profile/firebase/token/update`, data),
    updatePhone: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].put */ .Z.put(`/dashboard/user/profile/update`, {}, {
            params
        }),
    userList: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].get */ .Z.get(`/dashboard/user/search-sending`, {
            params
        }),
    sendMoney: (data)=>_request__WEBPACK_IMPORTED_MODULE_0__/* ["default"].post */ .Z.post(`/dashboard/user/wallet/send`, data)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (profileService);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 59748:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "S": () => (/* binding */ createEmotionCache),
/* harmony export */   "b": () => (/* binding */ createRtlEmotionCache)
/* harmony export */ });
/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8440);
/* harmony import */ var stylis_plugin_rtl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(93195);
/* harmony import */ var stylis_plugin_rtl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(stylis_plugin_rtl__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var stylis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(44615);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__, stylis__WEBPACK_IMPORTED_MODULE_2__]);
([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__, stylis__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);



const isBrowser = typeof document !== "undefined";
// On the client side, Create a meta tag at the top of the <head> and set it as insertionPoint.
// This assures that MUI styles are loaded first.
// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.
function createEmotionCache() {
    let insertionPoint;
    if (isBrowser) {
        const emotionInsertionPoint = document.querySelector('meta[name="emotion-insertion-point"]');
        insertionPoint = emotionInsertionPoint ?? undefined;
    }
    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__["default"])({
        key: "mui-style",
        insertionPoint
    });
}
function createRtlEmotionCache() {
    let insertionPoint;
    if (isBrowser) {
        const emotionInsertionPoint = document.querySelector('meta[name="emotion-insertion-point"]');
        insertionPoint = emotionInsertionPoint ?? undefined;
    }
    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__["default"])({
        key: "mui-style",
        insertionPoint,
        stylisPlugins: [
            stylis__WEBPACK_IMPORTED_MODULE_2__.prefixer,
            (stylis_plugin_rtl__WEBPACK_IMPORTED_MODULE_1___default())
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 58648:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ createSettings)
/* harmony export */ });
function createSettings(list) {
    const result = list.map((item)=>({
            [item.key]: item.value
        }));
    return Object.assign({}, ...result);
}


/***/ }),

/***/ 95785:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ getImage)
/* harmony export */ });
// import { IMAGE_URL } from "constants/constants";
function getImage(img) {
    if (img) {
        return img;
    } else {
        return "";
    }
}


/***/ }),

/***/ 77347:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ getLanguage)
/* harmony export */ });
/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3075);

function getLanguage(lang) {
    return lang || constants_config__WEBPACK_IMPORTED_MODULE_0__/* .DEFAULT_LANGUAGE */ .k$;
}


/***/ }),

/***/ 36399:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "h": () => (/* binding */ useDeviceType)
});

;// CONCATENATED MODULE: external "mobile-detect"
const external_mobile_detect_namespaceObject = require("mobile-detect");
var external_mobile_detect_default = /*#__PURE__*/__webpack_require__.n(external_mobile_detect_namespaceObject);
;// CONCATENATED MODULE: ./utils/useDeviceType.ts

function useDeviceType(userAgent) {
    const md = new (external_mobile_detect_default())(userAgent);
    let mobile = false, tablet = false, desktop = false;
    if (md.tablet()) {
        tablet = true;
    } else if (md.mobile()) {
        mobile = true;
    } else {
        desktop = true;
    }
    return {
        mobile,
        tablet,
        desktop
    };
}


/***/ }),

/***/ 98599:
/***/ (() => {



/***/ }),

/***/ 86764:
/***/ (() => {



/***/ }),

/***/ 65692:
/***/ ((module) => {

"use strict";
module.exports = require("@mui/material");

/***/ }),

/***/ 18442:
/***/ ((module) => {

"use strict";
module.exports = require("@mui/material/styles");

/***/ }),

/***/ 75184:
/***/ ((module) => {

"use strict";
module.exports = require("@reduxjs/toolkit");

/***/ }),

/***/ 1635:
/***/ ((module) => {

"use strict";
module.exports = require("dayjs");

/***/ }),

/***/ 11067:
/***/ ((module) => {

"use strict";
module.exports = require("dayjs/locale/nl");

/***/ }),

/***/ 83490:
/***/ ((module) => {

"use strict";
module.exports = require("dayjs/locale/pl");

/***/ }),

/***/ 45288:
/***/ ((module) => {

"use strict";
module.exports = require("dayjs/locale/pt-br");

/***/ }),

/***/ 2296:
/***/ ((module) => {

"use strict";
module.exports = require("formik");

/***/ }),

/***/ 58557:
/***/ ((module) => {

"use strict";
module.exports = require("google-map-react");

/***/ }),

/***/ 7486:
/***/ ((module) => {

"use strict";
module.exports = require("next-cookies");

/***/ }),

/***/ 3280:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/app-router-context.js");

/***/ }),

/***/ 92796:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/head-manager-context.js");

/***/ }),

/***/ 94957:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/head.js");

/***/ }),

/***/ 34014:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/i18n/normalize-locale-path.js");

/***/ }),

/***/ 64486:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-blur-svg.js");

/***/ }),

/***/ 50744:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-config-context.js");

/***/ }),

/***/ 35843:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-config.js");

/***/ }),

/***/ 99552:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-loader");

/***/ }),

/***/ 78524:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ 95832:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/loadable.js");

/***/ }),

/***/ 78020:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/mitt.js");

/***/ }),

/***/ 64406:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/page-path/denormalize-page-path.js");

/***/ }),

/***/ 24964:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router-context.js");

/***/ }),

/***/ 11751:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix.js");

/***/ }),

/***/ 46220:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/compare-states.js");

/***/ }),

/***/ 10299:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-next-pathname-info.js");

/***/ }),

/***/ 23938:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-url.js");

/***/ }),

/***/ 29565:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/get-asset-path-from-route.js");

/***/ }),

/***/ 35789:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/get-next-pathname-info.js");

/***/ }),

/***/ 1897:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot.js");

/***/ }),

/***/ 1428:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-dynamic.js");

/***/ }),

/***/ 28854:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/parse-path.js");

/***/ }),

/***/ 91292:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/parse-relative-url.js");

/***/ }),

/***/ 34567:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/path-has-prefix.js");

/***/ }),

/***/ 80979:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/querystring.js");

/***/ }),

/***/ 93297:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash.js");

/***/ }),

/***/ 36052:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/resolve-rewrites.js");

/***/ }),

/***/ 84226:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/route-matcher.js");

/***/ }),

/***/ 95052:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/route-regex.js");

/***/ }),

/***/ 59232:
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ 71853:
/***/ ((module) => {

"use strict";
module.exports = require("next/router");

/***/ }),

/***/ 30808:
/***/ ((module) => {

"use strict";
module.exports = require("nprogress");

/***/ }),

/***/ 87104:
/***/ ((module) => {

"use strict";
module.exports = require("qs");

/***/ }),

/***/ 16689:
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ 66405:
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ 61175:
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ 6022:
/***/ ((module) => {

"use strict";
module.exports = require("react-redux");

/***/ }),

/***/ 20997:
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 14161:
/***/ ((module) => {

"use strict";
module.exports = require("redux-persist");

/***/ }),

/***/ 61127:
/***/ ((module) => {

"use strict";
module.exports = require("redux-persist/integration/react");

/***/ }),

/***/ 98936:
/***/ ((module) => {

"use strict";
module.exports = require("redux-persist/lib/storage");

/***/ }),

/***/ 75265:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/AddLineIcon");

/***/ }),

/***/ 89224:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ArchiveLineIcon");

/***/ }),

/***/ 625:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ArrowLeftLineIcon");

/***/ }),

/***/ 71116:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ArrowLeftSLineIcon");

/***/ }),

/***/ 51406:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ArrowRightSLineIcon");

/***/ }),

/***/ 33211:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/Briefcase2FillIcon");

/***/ }),

/***/ 991:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CheckDoubleLineIcon");

/***/ }),

/***/ 4634:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CheckboxCircleLineIcon");

/***/ }),

/***/ 11060:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CloseFillIcon");

/***/ }),

/***/ 22179:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CloseLineIcon");

/***/ }),

/***/ 5948:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/CompassDiscoverLineIcon");

/***/ }),

/***/ 10865:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/EqualizerFillIcon");

/***/ }),

/***/ 53112:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/ErrorWarningLineIcon");

/***/ }),

/***/ 53921:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/FacebookCircleFillIcon");

/***/ }),

/***/ 17618:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/FileList3LineIcon");

/***/ }),

/***/ 26582:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/HeartLineIcon");

/***/ }),

/***/ 81812:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/HistoryLineIcon");

/***/ }),

/***/ 12564:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/InformationLineIcon");

/***/ }),

/***/ 51788:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/InstagramLineIcon");

/***/ }),

/***/ 72324:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/LogoutCircleRLineIcon");

/***/ }),

/***/ 80110:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/MapPin2LineIcon");

/***/ }),

/***/ 22069:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/MapPinFillIcon");

/***/ }),

/***/ 99893:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/MapPinRangeLineIcon");

/***/ }),

/***/ 92379:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/MoonFillIcon");

/***/ }),

/***/ 92853:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/Notification2LineIcon");

/***/ }),

/***/ 97240:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/QuestionLineIcon");

/***/ }),

/***/ 78428:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/Search2LineIcon");

/***/ }),

/***/ 78740:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/Settings3LineIcon");

/***/ }),

/***/ 88873:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/SunFillIcon");

/***/ }),

/***/ 2923:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/TimeLineIcon");

/***/ }),

/***/ 68523:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/TwitterFillIcon");

/***/ }),

/***/ 86073:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/UserLocationFillIcon");

/***/ }),

/***/ 29932:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/UserStarLineIcon");

/***/ }),

/***/ 907:
/***/ ((module) => {

"use strict";
module.exports = require("remixicon-react/Wallet3LineIcon");

/***/ }),

/***/ 93195:
/***/ ((module) => {

"use strict";
module.exports = require("stylis-plugin-rtl");

/***/ }),

/***/ 8440:
/***/ ((module) => {

"use strict";
module.exports = import("@emotion/cache");;

/***/ }),

/***/ 53139:
/***/ ((module) => {

"use strict";
module.exports = import("@emotion/react");;

/***/ }),

/***/ 99648:
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ 23745:
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ 60401:
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ 1492:
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ }),

/***/ 33512:
/***/ ((module) => {

"use strict";
module.exports = import("firebase/messaging");;

/***/ }),

/***/ 63392:
/***/ ((module) => {

"use strict";
module.exports = import("firebase/storage");;

/***/ }),

/***/ 22021:
/***/ ((module) => {

"use strict";
module.exports = import("i18next");;

/***/ }),

/***/ 64329:
/***/ ((module) => {

"use strict";
module.exports = import("i18next-http-backend");;

/***/ }),

/***/ 69915:
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ 57987:
/***/ ((module) => {

"use strict";
module.exports = import("react-i18next");;

/***/ }),

/***/ 3590:
/***/ ((module) => {

"use strict";
module.exports = import("react-toastify");;

/***/ }),

/***/ 44615:
/***/ ((module) => {

"use strict";
module.exports = import("stylis");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [2078,5675,676,1664,5152,2480,3075,5728,7562,7262,6684,26,7567,1612,5122,1929,256,8423,865,182,5215,6461,7850,849,6393,6395,7234], () => (__webpack_exec__(13847)));
module.exports = __webpack_exports__;

})();