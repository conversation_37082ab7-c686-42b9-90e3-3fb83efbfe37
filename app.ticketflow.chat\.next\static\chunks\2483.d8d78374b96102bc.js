(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2483],{2483:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return b}});var n=t(85893),i=t(67294),l=t(22246),d=t.n(l),o=t(77262),r=t(98396),s=t(37490),u=t(5152),p=t.n(u),c=t(21697),v=t(88767),m=t(85943),y=t(5848),_=t(84272),h=t(6734),g=t(73714);let x=p()(()=>t.e(7107).then(t.bind(t,47107)),{loadableGenerated:{webpack:()=>[47107]}}),f=p()(()=>Promise.resolve().then(t.bind(t,21014)),{loadableGenerated:{webpack:()=>[21014]}});function b(e){var a,t,l,u;let{data:p}=e,b=(0,r.Z)("(min-width:1140px)"),{t:w,i18n:j}=(0,h.$G)(),M=(0,v.useQueryClient)(),[C,Z,N]=(0,s.Z)(),{settings:k}=(0,c.r)(),{data:F}=(0,v.useQuery)("payments",()=>m.Z.getAll(),{enabled:y.de.includes((null==p?void 0:null===(a=p.transaction)||void 0===a?void 0:a.status)||"paid")&&(null==p?void 0:null===(t=p.transaction)||void 0===t?void 0:t.payment_system.tag)!=="cash"}),{paymentTypes:S}=(0,i.useMemo)(()=>{let e,a;if((null==k?void 0:k.payment_type)==="admin")e=null==F?void 0:F.data.find(e=>"cash"===e.tag),a=(null==F?void 0:F.data)||[];else{var t,n,i,l,d;e=null===(i=null==p?void 0:null===(t=p.shop)||void 0===t?void 0:null===(n=t.shop_payments)||void 0===n?void 0:n.find(e=>"cash"===e.payment.tag))||void 0===i?void 0:i.payment,a=(null==p?void 0:null===(l=p.shop)||void 0===l?void 0:null===(d=l.shop_payments)||void 0===d?void 0:d.map(e=>e.payment))||[]}return{paymentType:e,paymentTypes:a}},[k,p,F]),{isLoading:E,mutate:G}=(0,v.useMutation)({mutationFn:e=>m.Z.createTransaction(e.id,e.payment),onSuccess(){M.invalidateQueries(["profile"],{exact:!1}),M.invalidateQueries(["order",null==p?void 0:p.id,j.language])},onError(e){var a;(0,g.vU)(null==e?void 0:null===(a=e.data)||void 0===a?void 0:a.message)},onSettled(){N()}}),{isLoading:Q,mutate:L}=(0,v.useMutation)({mutationFn:e=>m.Z.payExternal(e.name,e.data),onSuccess(e){window.location.replace(e.data.data.url)},onError(e){var a;(0,g.vU)(null==e?void 0:null===(a=e.data)||void 0===a?void 0:a.message)}}),T=e=>{let a=S.find(a=>a.tag===e),t={id:null==p?void 0:p.id,payment:{payment_sys_id:null==a?void 0:a.id}};y.DH.includes(e)&&L({name:e,data:{order_id:t.id}}),"alipay"===e&&window.location.replace("".concat(y._n,"/api/alipay-prepay?order_id=").concat(t.id)),G(t)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:d().payButton,children:(0,n.jsx)(o.Z,{onClick:Z,type:"button",children:w("pay")})}),b?(0,n.jsx)(x,{open:C,onClose:N,title:w("payment.method"),children:(0,n.jsx)(_.Z,{value:null==p?void 0:null===(l=p.transaction)||void 0===l?void 0:l.payment_system.tag,list:S,handleClose:N,isButtonLoading:E||Q,onSubmit(e){e&&T(e)}})}):(0,n.jsx)(f,{open:C,onClose:N,title:w("payment.method"),children:(0,n.jsx)(_.Z,{value:null==p?void 0:null===(u=p.transaction)||void 0===u?void 0:u.payment_system.tag,list:S,handleClose:N,onSubmit(e){e&&T(e)}})})]})}},84272:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});var n=t(85893),i=t(67294),l=t(6734),d=t(80865),o=t(2289),r=t.n(o);function s(e){let{value:a,list:t,onSubmit:o,isButtonLoading:s=!1,category:u}=e,{t:p}=(0,l.$G)(),[c,v]=(0,i.useState)(a),m=["mercado-pago","stripe","wallet"],y=["cash_delivery","card_delivery","pix_delivery","debit_delivery"],_=["cash_delivery","pix_delivery","card_delivery","debit_delivery"],h=(0,i.useMemo)(()=>{if(!u)return t;if("pay_now"===u)return t.filter(e=>m.includes(e.tag));if("pay_on_delivery"===u){let e=t.filter(e=>y.includes(e.tag));return e.sort((e,a)=>{let t=_.indexOf(e.tag),n=_.indexOf(a.tag);return -1!==t&&-1!==n?t-n:-1!==t?-1:-1!==n?1:0})}return t},[t,u]),g=e=>{v(e.target.value),o(e.target.value)},x=e=>({checked:c===e,onChange:g,value:e,id:e,name:"payment_method",inputProps:{"aria-label":e}});return(0,n.jsx)("div",{className:r().wrapper,children:(0,n.jsx)("div",{className:r().body,children:h.map(e=>(0,n.jsxs)("div",{className:r().row,children:[(0,n.jsx)(d.Z,{...x(e.tag)}),(0,n.jsx)("label",{className:r().label,htmlFor:e.tag,children:(0,n.jsx)("span",{className:r().text,children:p(e.tag)})})]},e.id))})})}},22246:function(e){e.exports={wrapper:"payToUnpaidOrders_wrapper__FpUXl"}},2289:function(e){e.exports={wrapper:"paymentMethod_wrapper__hDB06",body:"paymentMethod_body__niNGC",row:"paymentMethod_row__pHCIA",label:"paymentMethod_label__FI5nM",text:"paymentMethod_text__cmylm",footer:"paymentMethod_footer__3olxQ",action:"paymentMethod_action__rnLFd"}}}]);