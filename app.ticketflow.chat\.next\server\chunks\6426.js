exports.id = 6426;
exports.ids = [6426];
exports.modules = {

/***/ 73533:
/***/ ((module) => {

// Exports
module.exports = {
	"loading": "loading_loading__hXLim",
	"pageLoading": "loading_pageLoading__0nn5j"
};


/***/ }),

/***/ 72427:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ PhoneInputWithVerification)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(18442);
/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var remixicon_react_EditLineIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(15423);
/* harmony import */ var remixicon_react_EditLineIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_EditLineIcon__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var hooks_useModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(37490);
/* harmony import */ var components_editPhone_editPhone__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(42606);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(5152);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_7__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_editPhone_editPhone__WEBPACK_IMPORTED_MODULE_6__]);
components_editPhone_editPhone__WEBPACK_IMPORTED_MODULE_6__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];








const ModalContainer = next_dynamic__WEBPACK_IMPORTED_MODULE_7___default()(()=>__webpack_require__.e(/* import() */ 7567).then(__webpack_require__.bind(__webpack_require__, 47567)), {
    loadableGenerated: {
        modules: [
            "..\\components\\inputs\\phoneInputWithVerification.tsx -> " + "containers/modal/modal"
        ]
    }
});
const MobileDrawer = next_dynamic__WEBPACK_IMPORTED_MODULE_7___default()(()=>__webpack_require__.e(/* import() */ 182).then(__webpack_require__.bind(__webpack_require__, 30182)), {
    loadableGenerated: {
        modules: [
            "..\\components\\inputs\\phoneInputWithVerification.tsx -> " + "containers/drawer/mobileDrawer"
        ]
    }
});
const Input = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_3__.TextField)({
    width: "100%",
    backgroundColor: "transparent",
    "& .MuiInputLabel-root": {
        fontSize: 12,
        lineHeight: "14px",
        fontWeight: 500,
        textTransform: "uppercase",
        color: "var(--black)",
        "&.Mui-error": {
            color: "var(--red)"
        }
    },
    "& .MuiInputLabel-root.Mui-focused": {
        color: "var(--black)"
    },
    "& .MuiInput-root": {
        fontSize: 16,
        fontWeight: 500,
        lineHeight: "19px",
        color: "var(--black)",
        fontFamily: "'Inter', sans-serif",
        "&.Mui-error::after": {
            borderBottomColor: "var(--red)"
        }
    },
    "& .MuiInput-root::before": {
        borderBottom: "1px solid var(--grey)"
    },
    "& .MuiInput-root:hover:not(.Mui-disabled)::before": {
        borderBottom: "2px solid var(--black)"
    },
    "& .MuiInput-root::after": {
        borderBottom: "2px solid var(--primary)"
    }
});
function PhoneInputWithVerification(props) {
    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_3__.useMediaQuery)("(min-width:1140px)");
    const [phoneModal, handleOpenPhone, handleClosePhone] = (0,hooks_useModal__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)();
    const isUsingCustomPhoneSignIn = "false" === "true";
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(Input, {
                variant: "standard",
                type: "text",
                InputLabelProps: {
                    shrink: true
                },
                InputProps: isUsingCustomPhoneSignIn ? undefined : {
                    endAdornment: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_3__.InputAdornment, {
                        position: "end",
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_3__.IconButton, {
                            onClick: handleOpenPhone,
                            disableRipple: true,
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((remixicon_react_EditLineIcon__WEBPACK_IMPORTED_MODULE_4___default()), {})
                        })
                    })
                },
                ...props
            }),
            isDesktop ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ModalContainer, {
                open: phoneModal,
                onClose: handleClosePhone,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_editPhone_editPhone__WEBPACK_IMPORTED_MODULE_6__["default"], {
                    handleClose: handleClosePhone
                })
            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(MobileDrawer, {
                open: phoneModal,
                onClose: handleClosePhone,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(components_editPhone_editPhone__WEBPACK_IMPORTED_MODULE_6__["default"], {
                    handleClose: handleClosePhone
                })
            })
        ]
    });
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 75619:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ Loading)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(65692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(73533);
/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_loading_module_scss__WEBPACK_IMPORTED_MODULE_3__);




function Loading({}) {
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        className: (_loading_module_scss__WEBPACK_IMPORTED_MODULE_3___default().loading),
        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_mui_material__WEBPACK_IMPORTED_MODULE_2__.CircularProgress, {})
    });
}


/***/ }),

/***/ 21697:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "J": () => (/* binding */ SettingsContext),
/* harmony export */   "r": () => (/* binding */ useSettings)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(16689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

const SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});
const useSettings = ()=>(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SettingsContext);


/***/ }),

/***/ 26221:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ getAvatar)
/* harmony export */ });
/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5848);

const avatar_placeholder = "/images/avatar_placeholder.png";
function getAvatar(img) {
    if (img) {
        return img.includes("http") ? img : constants_constants__WEBPACK_IMPORTED_MODULE_0__/* .IMAGE_URL */ .yA + img;
    } else {
        return avatar_placeholder;
    }
}


/***/ })

};
;