(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1648],{21648:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return x}});var n=i(85893),o=i(67294),a=i(38498),c=i.n(a),s=i(6734),l=i(9883),r=i(30569),d=i(3075),u=i(45641);let h=(e,t)=>{let i=(0,l.KL)(r.ZP);(0,l.LP)(i,{vapidKey:d.ft}).then(n=>{n?(u.Z.firebaseTokenUpdate({firebase_token:n}).then(()=>{}).catch(e=>{console.log(e)}),(0,l.ps)(i,i=>{t&&t(null==i?void 0:i.data),e(null==i?void 0:i.notification)})):console.log("No registration token available. Request permission to generate one.")}).catch(e=>{console.log("An error occurred while retrieving token. ",e)})};var _=i(48654),p=i.n(_),f=i(23926),N=i(41664),v=i.n(N);function x(e){let{}=e,{t}=(0,s.$G)(),[i,a]=(0,o.useState)(void 0),[l,r]=(0,o.useState)(void 0);function d(){a(void 0)}return(0,o.useEffect)(()=>{h(a,r)},[]),(0,n.jsx)(f.Z,{onClickAway:d,children:(0,n.jsx)("div",{className:c().container,children:(0,n.jsxs)("div",{className:"".concat(c().wrapper," ").concat(i?"":c().hidden),children:[(0,n.jsxs)("div",{className:c().header,children:[(0,n.jsxs)("h3",{className:c().title,children:[t("your.order")," #",null==i?void 0:i.title]}),(0,n.jsx)("button",{className:c().closeBtn,type:"button",onClick:d,children:(0,n.jsx)(p(),{})})]}),(0,n.jsx)("p",{className:c().text,children:t("your.order.status.updated.text")}),(0,n.jsx)(v(),{href:"/orders/".concat((null==l?void 0:l.id)||(null==i?void 0:i.title)),className:c().cta,onClick:d,children:t("show")})]})})})}},38498:function(e){e.exports={container:"pushNotification_container__Vxgfo",wrapper:"pushNotification_wrapper__P0pPH",hidden:"pushNotification_hidden__42tJb",header:"pushNotification_header__xzpgF",title:"pushNotification_title__Gve8N",closeBtn:"pushNotification_closeBtn__2muvj",text:"pushNotification_text__jnOun",cta:"pushNotification_cta__N2pF3"}}}]);