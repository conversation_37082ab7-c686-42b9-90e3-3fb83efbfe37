(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9326],{92038:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return P}});var n=a(85893),r=a(98396),s=a(5152),l=a.n(s),i=a(88767),o=a(1612),d=a(67294),g=a(56457),u=a(5215),c=a(34349),p=a(13443),b=a(94910),v=a(2950),h=a(18074),f=a(80129),Z=a.n(f),y=a(11163);let k=l()(()=>Promise.all([a.e(719),a.e(2028)]).then(a.bind(a,52028)),{loadableGenerated:{webpack:()=>[52028]}}),m=l()(()=>Promise.resolve().then(a.bind(a,37935)),{loadableGenerated:{webpack:()=>[37935]}}),x=l()(()=>a.e(3135).then(a.bind(a,3135)),{loadableGenerated:{webpack:()=>[3135]}}),w=l()(()=>Promise.all([a.e(6694),a.e(6519)]).then(a.bind(a,48709)),{loadableGenerated:{webpack:()=>[48709]}}),A=l()(()=>a.e(8253).then(a.bind(a,28253)),{loadableGenerated:{webpack:()=>[28253]}}),_=l()(()=>a.e(520).then(a.bind(a,20520)),{loadableGenerated:{webpack:()=>[20520]}}),j=l()(()=>Promise.all([a.e(6886),a.e(1363),a.e(4077)]).then(a.bind(a,41363)),{loadableGenerated:{webpack:()=>[41363]}}),C=l()(()=>Promise.all([a.e(4564),a.e(6886),a.e(2175),a.e(2598),a.e(224),a.e(6860),a.e(6515),a.e(65)]).then(a.bind(a,16515)),{loadableGenerated:{webpack:()=>[16515]}});function P(){var e,t;let{t:a,locale:s}=(0,h.Z)(),{query:l}=(0,y.useRouter)(),f=(0,r.Z)("(min-width:1140px)"),P=(0,d.useRef)(null),{category_id:G,newest:I,order_by:S,group:Q}=(0,c.C)(u.qs),B=(0,v.Z)(),{data:N,isLoading:E}=(0,i.useQuery)(["stories",s],()=>p.Z.getAll()),{data:L,isLoading:M}=(0,i.useQuery)(["banners",s],()=>b.Z.getAll()),{isSuccess:R,isLoading:q}=(0,i.useQuery)(["shopZones",B],()=>o.Z.checkZone({address:B})),{data:F,error:O,fetchNextPage:z,hasNextPage:D,isFetchingNextPage:H,isLoading:J}=(0,i.useInfiniteQuery)(["shops",G,s,S,Q,B,I,null==l?void 0:l.verfiy],e=>{var t;let{pageParam:a=1}=e;return o.Z.getAllShops(Z().stringify({page:a,perPage:12,category_id:null!=G?G:void 0,order_by:I?"new":S,free_delivery:Q.free_delivery,take:Q.tag,rating:null===(t=Q.rating)||void 0===t?void 0:t.split(","),prices:Q.prices,address:B,open:Number(Q.open)||void 0,deals:Q.deals,verify:null==l?void 0:l.verify}))},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1}}),K=(null==F?void 0:null===(e=F.pages)||void 0===e?void 0:e.flatMap(e=>e.data))||[],{data:T}=(0,i.useQuery)("shopCategories",()=>g.Z.getAllShopCategories()),U=(0,d.useCallback)(e=>{let t=e[0];t.isIntersecting&&D&&z()},[]);return(0,d.useEffect)(()=>{let e=new IntersectionObserver(U,{root:null,rootMargin:"20px",threshold:0});P.current&&e.observe(P.current)},[U]),O&&console.log("error => ",O),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(k,{stories:N||[],banners:(null==L?void 0:L.data)||[],loadingStory:E,loadingBanner:M}),f?(0,n.jsx)(w,{categories:(null==T?void 0:T.data)||[]}):(0,n.jsx)(A,{categories:(null==T?void 0:T.data)||[]}),(0,n.jsx)(j,{title:a("all.shops"),shops:(null==F?void 0:null===(t=F.pages)||void 0===t?void 0:t.flatMap(e=>e.data))||[],loading:J&&!H}),H&&(0,n.jsx)(m,{}),(0,n.jsx)("div",{ref:P}),!R&&!q&&(0,n.jsx)(x,{}),!K.length&&!J&&R&&(0,n.jsx)(_,{text:a("no.shops")}),(0,n.jsx)(C,{})]})}},94910:function(e,t,a){"use strict";var n=a(25728);t.Z={getAll:e=>n.Z.get("/rest/banners/paginate",{params:e}),getById:(e,t)=>n.Z.get("/rest/banners/".concat(e),{params:t}),getAllAds:e=>n.Z.get("/rest/banners-ads",{params:e}),getAdById:(e,t)=>n.Z.get("/rest/banners-ads/".concat(e),{params:t})}},56457:function(e,t,a){"use strict";var n=a(25728);t.Z={getAllShopCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"shop"}})},getAllSubCategories:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("rest/categories/sub-shop/".concat(e),{params:t})},getAllProductCategories:(e,t)=>n.Z.get("/rest/shops/".concat(e,"/categories"),{params:t}),getAllRecipeCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"receipt"}})},getById:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("/rest/categories/".concat(e),{params:t})}}},13443:function(e,t,a){"use strict";var n=a(25728);t.Z={getAll:e=>n.Z.get("/rest/stories/paginate",{params:e})}},24654:function(){}}]);